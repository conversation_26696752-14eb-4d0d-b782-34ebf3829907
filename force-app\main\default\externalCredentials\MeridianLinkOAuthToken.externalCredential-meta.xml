<?xml version="1.0" encoding="UTF-8"?>
<ExternalCredential xmlns="http://soap.sforce.com/2006/04/metadata">
    <authenticationProtocol>Oauth</authenticationProtocol>
    <externalCredentialParameters>
        <parameterGroup>DefaultGroup</parameterGroup>
        <parameterName>Oauth</parameterName>
        <parameterType>AuthProtocolVariant</parameterType>
        <parameterValue>ClientCredentialsClientSecret</parameterValue>
    </externalCredentialParameters>
    <externalCredentialParameters>
        <parameterGroup>DefaultGroup</parameterGroup>
        <parameterName>Scope</parameterName>
        <parameterType>AuthParameter</parameterType>
        <parameterValue>https://graph.microsoft.com/.default</parameterValue>
    </externalCredentialParameters>
    <externalCredentialParameters>
        <parameterGroup>DefaultGroup</parameterGroup>
        <parameterName>AuthProviderUrl</parameterName>
        <parameterType>AuthProviderUrl</parameterType>
        <parameterValue>https://login.microsoftonline.com/61e68e02-96f7-4fa1-92ca-390bebbc1f1f/oauth2/v2.0/token</parameterValue>
    </externalCredentialParameters>
    <externalCredentialParameters>
        <parameterGroup>MeridianLinkSandboxKeys</parameterGroup>
        <parameterName>MeridianLinkSandboxKeys</parameterName>
        <parameterType>NamedPrincipal</parameterType>
        <sequenceNumber>1</sequenceNumber>
    </externalCredentialParameters>
    <label>MeridianLinkOAuthToken</label>
</ExternalCredential>
