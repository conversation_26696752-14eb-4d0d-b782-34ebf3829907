import { LightningElement } from 'lwc';
import {OmniscriptBaseMixin} from 'omnistudio/omniscriptBaseMixin';
export default class RcuMoneyMarketAndCds extends OmniscriptBaseMixin(LightningElement) {

  handleCheckboxChange1(event) {
    
   
}

handleCheckboxChange2(event) {
}

handleCheckboxChange3(event) {
}

handleCheckboxChange4(event) {
}


    handleNextPage(event) {
            this.omniNextStep(); // Move to next step
        }

        // Check if Choice is selected and fields are filled
    handlePreviousPage(){
        this.omniPrevStep();
    }
}