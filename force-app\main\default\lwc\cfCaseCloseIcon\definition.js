let definition =
      {"states":[{"fields":[],"conditions":{"id":"state-condition-object","isParent":true,"group":[]},"definedActions":{"actions":[]},"name":"Active","isSmartAction":false,"smartAction":{},"styleObject":{"padding":[{"type":"around","size":"x-small"}],"margin":[{"type":"around","size":"none"}],"container":{"class":"slds-card"},"size":{"isResponsive":false,"default":"12"},"sizeClass":"slds-size_12-of-12","class":"slds-card slds-p-around_x-small slds-m-bottom_x-small"},"components":{"layer-0":{"children":[{"name":"Icon","element":"flexIcon","size":{"isResponsive":false,"default":"12"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","card":"{card}","iconType":"Salesforce SVG","iconName":"action:approval","size":"large","extraclass":"slds-icon_container slds-icon-action-approval slds-icon_container--circle","variant":"inverse","imgsrc":""},"type":"element","styleObject":{"sizeClass":"slds-size_12-of-12"},"elementLabel":"Icon-0"}]}},"childCards":[],"actions":[],"omniscripts":[],"documents":[]}],"dataSource":{"type":null,"value":{},"orderBy":{},"contextVariables":[]},"title":"CaseCloseIcon","enableLwc":true,"isFlex":true,"theme":"slds","selectableMode":"Multi","Name":"CaseCloseIcon","uniqueKey":"CaseCloseIcon_Salesforce_1","Id":"flexmetadata0.94828849694743591726728464791","OmniUiCardType":"Child"};
  export default definition