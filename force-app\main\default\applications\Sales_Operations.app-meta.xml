<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Opportunity_Record_Lighting_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
    </actionOverrides>
    <brand>
        <headerColor>#0070D2</headerColor>
        <logoVersion>1</logoVersion>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <description>Customize and automate the sales process, and analyze your data to make informed business decisions.</description>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Sales Operations</label>
    <navType>Standard</navType>
    <tabs>standard-home</tabs>
    <tabs>standard-Dashboard</tabs>
    <tabs>standard-report</tabs>
    <tabs>standard-Pricebook2</tabs>
    <tabs>standard-Product2</tabs>
    <tabs>standard-CollaborationGroup</tabs>
    <tabs>standard-Feed</tabs>
    <tabs>Email_Preferences__c</tabs>
    <tabs>Email_Preference_Category__c</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>Sales_Operations_UtilityBar</utilityBar>
</CustomApplication>
