<?xml version="1.0" encoding="UTF-8"?>
<LightningComponentBundle xmlns="http://soap.sforce.com/2006/04/metadata" fqn="daoCustomDocusignSignatureAction">
    <apiVersion>60.0</apiVersion>
    <isExposed>true</isExposed>
    <targets>
        <target>lightning__AppPage</target>
        <target>lightning__RecordPage</target>
        <target>lightning__HomePage</target>
        <target>lightning__FlowScreen</target>
    </targets>
    <targetConfigs>
        <targetConfig targets="lightning__AppPage,lightning__RecordPage,lightning__HomePage">
            <property name="name" type="String" label="Name"/>
            <property name="fieldLabel" type="String" label="Field Label"/>
            <property name="signerName" type="String" label="Signer Name"/>
            <property name="signerEmail" type="String" label="Signer Email"/>
            <property name="emailSubject" type="String" label="Email Subject"/>
            <property name="docusignReturnUrl" type="String" label="DocuSign Return Url"/>
            <property name="dateFormat" type="String" label="Date Format"/>
            <property name="dateTimeFormat" type="String" label="Date Time Format"/>
            <property name="timeFormat" type="String" label="Time Format"/>
            <property name="validationRequired" type="String" label="Validation Required"/>
            <property name="templates" type="String" label="DocuSign Templates" description="JSON string or template name(s)"/>
        </targetConfig>
    </targetConfigs>
</LightningComponentBundle>