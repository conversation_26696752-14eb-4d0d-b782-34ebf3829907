<template>
  <h1 style="font-size:0">This is {selectedOption}</h1>
  <div class="button-container">
    <template if:true={showFirstButton}>
      <lightning-button
        variant="brand"
        class="first-button"
        stretch
        label={firstButtonLabel}
        title={firstButtonLabel}
        onclick={NavigateToUrl}>
      </lightning-button>
    </template>
    <template if:true={showSecondButton}>
      <lightning-button
        class="second-button"
        variant="brand"
        label={secondButtonLabel}
        title={secondButtonLabel}
        onclick={MoveToNextStep}>
      </lightning-button>
    </template>
  </div>
</template>