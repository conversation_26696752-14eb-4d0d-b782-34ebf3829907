global with sharing class DocuSignPayloadGenerator implements Callable {

    public Boolean call(String action, Map<String, Object> args) {

        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');

        return invokeMethod(action, input, output, options);
    }

    global Boolean invokeMethod(String methodName, Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        Boolean result = true;

        try {
            if (methodName == 'buildTextTabs') {

                // Extract values from inputMap
                String email = (String) inputMap.get('email');
                String name = (String) inputMap.get('name');
                String phone = (String) inputMap.get('phone');
                String description = (String) inputMap.get('description');

                // Create list of textTabs
                List<Map<String, String>> textTabs = new List<Map<String, String>>();
                textTabs.add(new Map<String, String>{ 'tabLabel' => 'email', 'value' => email });
                textTabs.add(new Map<String, String>{ 'tabLabel' => 'name', 'value' => name });
                textTabs.add(new Map<String, String>{ 'tabLabel' => 'phone', 'value' => phone });
                textTabs.add(new Map<String, String>{ 'tabLabel' => 'description', 'value' => description });

                // Add to output map
                Map<String, Object> tabs = new Map<String, Object>{
                    'textTabs' => textTabs
                };

                outMap.put('tabs', tabs);
            }

        } catch (Exception e) {
            result = false;
            outMap.put('error', e.getMessage());
        }

        return result;
    }
}