<template>

    <div class="slds-m-around_medium">
        <lightning-button-icon
            icon-name="utility:attach"
            alternative-text="Attach File"
            class="slds-icon-text-default"
            onclick={handleClick}
        ></lightning-button-icon>
    </div>

    <template if:true={isModalOpen}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Close" onclick={closeModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="Close"></lightning-icon>
                        <span class="slds-assistive-text">Close</span>
                    </button>
                    <h2 class="slds-text-heading_medium">Attach File</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <p style="font-size: 20px;"><b>Please Select the document type and enter a description</b></p></br>
                    <lightning-combobox
                        name="firstPicklist"
                        label="Document Type"
                        required = "true"
                        value={firstPicklistValue}
                        placeholder="Choose an option"
                        options={firstPicklistOptions}
                        onchange={handlePicklistChange}
                    ></lightning-combobox>
                    <lightning-input
                        type="text"
                        required = "true"
                        label="Document Description"
                        value={description}
                        onchange={handleDescriptionChange}
                        class="slds-m-top_medium"
                    ></lightning-input>
                    <lightning-combobox
                        name="secondPicklist"
                        label="Role"
                        required = "true"
                        if:true={isId}
                        value={secondPicklistValue}
                        placeholder="Choose an option"
                        options={secondPicklistOptions}
                        onchange={handlePicklistChange}
                        class="slds-m-top_medium"
                    ></lightning-combobox>
                     <lightning-combobox
                        name="rolesPicklistOptions"
                        label="Select Role"
                        required="true"
                        if:true={isId}
                        value={selectedRole}
                        placeholder="Choose a role"
                        options={rolesPicklistOptions}
                        onchange={handleRolePicklistChange}
                        class="slds-m-top_medium"
                    ></lightning-combobox>
                    <lightning-file-upload
                        label="Upload Files"
                        multiple
                        accept={acceptedFormats}
                        onuploadfinished={handleUploadFinished}
                        key={uploadKey}
                        class="slds-m-top_medium"
                    ></lightning-file-upload>
                    <div class="slds-m-top_medium slds-text-align_center">
                    <lightning-button
                        label="Submit"
                        variant="brand"
                        class="slds-button_success"
                        onclick={handleSubmit}
                        disabled={hasError}
                    ></lightning-button>
                </div></br></br>

                <template if:true={documentsData.length}>
                    <lightning-datatable
                        key-field="id"
                        key={uniqueKeyId}
                        data={documentsData}
                        columns={columns}
                        onrowaction={handleRowAction}
                    ></lightning-datatable>
                </template>

                </div>
                <footer class="slds-modal__footer">
                    <lightning-button label="Close" onclick={closeModal}></lightning-button>
                </footer>
                
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>