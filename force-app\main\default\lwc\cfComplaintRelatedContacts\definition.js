let definition =
      {"states":[{"fields":[],"conditions":{"id":"state-condition-object","isParent":true,"group":[]},"definedActions":{"actions":[]},"name":"Active","isSmartAction":false,"smartAction":{},"styleObject":{"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"margin":[{"type":"bottom","size":"x-small","label":"bottom:x-small"}],"container":{"class":""},"size":{"isResponsive":false,"default":"12"},"sizeClass":"slds-size_12-of-12 ","class":"slds-p-around_x-small slds-m-bottom_x-small ","background":{"color":"#FFFFFF","image":"","size":"","repeat":"","position":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"background-color:#FFFFFF;      \n         "},"components":{"layer-0":{"children":[{"name":"Block","element":"block","size":{"isResponsive":false,"default":"12"},"stateIndex":0,"class":"slds-col ","property":{"label":"Card","collapsible":false,"record":"{record}","collapsedByDefault":false,"card":"{card}","action":{"label":"Action","iconName":"standard-default","eventType":"onclick","actionList":[{"key":"1654203339731-pk0hymvb3","label":"Select Card","draggable":false,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-1654203348931","type":"cardAction","displayName":"Action","vlocityIcon":"standard-default","targetType":"Web Page","openUrlIn":"Current Window","Web Page":{"targetName":"/apex"},"eventName":"selectcards"},"actionIndex":0}],"showSpinner":"false"}},"type":"block","styleObject":{"padding":[],"class":"slds-theme_default slds-border_top slds-border_right slds-border_bottom slds-border_left ","sizeClass":"slds-size_12-of-12 ","margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"size":{"isResponsive":false,"default":"12"},"container":{"class":""},"border":{"type":["border_top","border_right","border_bottom","border_left"],"width":"1","color":"#1B96FF","radius":".25em","style":"solid"},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"max-width: 400px;\nmargin: auto;\npadding: 0 !important;\nposition: relative;\noutline: #1B96FF solid 1px;\n","style":"     border-top: #1B96FF 1px solid;border-right: #1B96FF 1px solid;border-bottom: #1B96FF 1px solid;border-left: #1B96FF 1px solid; \n    border-radius:.25em;     max-width: 400px;\nmargin: auto;\npadding: 0 !important;\nposition: relative;\noutline: #1B96FF solid 1px;\n","theme":"theme_default"},"children":[{"name":"Icon","element":"flexIcon","size":{"isResponsive":true,"default":"2","large":"2","medium":"2","small":"2"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","card":"{card}","iconType":"Salesforce SVG","iconName":"utility:check","size":"x-small","extraclass":"slds-icon_container slds-icon__svg--default ","variant":"inverse","imgsrc":"","color":"#FFFFFF"},"type":"element","styleObject":{"sizeClass":"slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 ","size":{"isResponsive":true,"default":"2","large":"2","medium":"2","small":"2"},"padding":[{"type":"left","size":"x-large","label":"left:x-large"},{"type":"right","size":"xx-small","label":"right:xx-small"},{"type":"bottom","size":"large","label":"bottom:large"}],"margin":[],"background":{"color":"#1B96FF","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":["border_top","border_right"],"width":"1","color":"#1B96FF","radius":".25em","style":""},"elementStyleProperties":{"color":"#FFFFFF"},"text":{"align":"right","color":""},"inlineStyle":"clip-path: polygon(28% 0, 100% 76%, 100% 0);\nposition: absolute;\nright: 0;\ntop: 0;\nmargin-top: -1px;\nmargin-right: -1px;\nz-index: 2;\noverflow: hidden;","class":"slds-text-align_right slds-border_top slds-border_right slds-p-left_x-large slds-p-right_xx-small slds-p-bottom_large ","style":"background-color:#1B96FF;     border-top: #1B96FF 1px solid;border-right: #1B96FF 1px solid; \n    border-radius:.25em; height:38 px; min-height:38 px; max-height:38 px;  clip-path: polygon(28% 0, 100% 76%, 100% 0);\nposition: absolute;\nright: 0;\ntop: 0;\nmargin-top: -1px;\nmargin-right: -1px;\nz-index: 2;\noverflow: hidden;","height":"38 px","minHeight":"38 px","maxHeight":"38 px"},"elementLabel":"Card-Icon-0","styleObjects":[{"key":0,"conditions":"default","styleObject":{"sizeClass":"slds-size_1-of-12 ","size":{"isResponsive":false,"default":"1"},"padding":[{"type":"top","size":"x-small","label":"top:x-small"},{"type":"left","size":"x-large","label":"left:x-large"},{"type":"right","size":"xx-small","label":"right:xx-small"},{"type":"top","size":"xx-small","label":"top:xx-small"},{"type":"bottom","size":"large","label":"bottom:large"}],"margin":[],"background":{"color":"#FFFFFF","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":["border_top","border_right"],"width":"2","color":"#FFFFFF","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"right","color":""},"inlineStyle":"clip-path: polygon(9% 0, 100% 100%, 100% 0);\nposition: absolute;\nright: 0;\ntop: 0;\nz-index: -1;","class":"slds-text-align_right slds-border_top slds-border_right slds-p-top_x-small slds-p-left_x-large slds-p-right_xx-small slds-p-top_xx-small slds-p-bottom_large ","style":"background-color:#FFFFFF;     border-top: #FFFFFF 2px solid;border-right: #FFFFFF 2px solid; \n         clip-path: polygon(9% 0, 100% 100%, 100% 0);\nposition: absolute;\nright: 0;\ntop: 0;\nz-index: -1;"},"label":"Default","name":"Default","conditionString":"","draggable":false,"isSetForDesignTime":false,"isopen":true},{"key":1,"conditions":{"id":"state-condition-object","isParent":true,"group":[{"id":"state-new-condition-0","field":"Selected","operator":"==","value":"true","type":"custom","hasMergeField":false}]},"styleObject":{"sizeClass":"slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 ","size":{"isResponsive":true,"default":"2","large":"2","medium":"2","small":"2"},"padding":[{"type":"left","size":"x-large","label":"left:x-large"},{"type":"right","size":"xx-small","label":"right:xx-small"},{"type":"bottom","size":"large","label":"bottom:large"}],"margin":[],"background":{"color":"#1B96FF","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":["border_top","border_right"],"width":"1","color":"#1B96FF","radius":".25em","style":""},"elementStyleProperties":{"color":"#FFFFFF"},"text":{"align":"right","color":""},"inlineStyle":"clip-path: polygon(28% 0, 100% 76%, 100% 0);\nposition: absolute;\nright: 0;\ntop: 0;\nmargin-top: -1px;\nmargin-right: -1px;\nz-index: 2;\noverflow: hidden;","class":"slds-text-align_right slds-border_top slds-border_right slds-p-left_x-large slds-p-right_xx-small slds-p-bottom_large ","style":"background-color:#1B96FF;     border-top: #1B96FF 1px solid;border-right: #1B96FF 1px solid; \n    border-radius:.25em; height:38 px; min-height:38 px; max-height:38 px;  clip-path: polygon(28% 0, 100% 76%, 100% 0);\nposition: absolute;\nright: 0;\ntop: 0;\nmargin-top: -1px;\nmargin-right: -1px;\nz-index: 2;\noverflow: hidden;","height":"38 px","minHeight":"38 px","maxHeight":"38 px"},"label":"SelectedCheck","name":"SelectedCheck","conditionString":"Selected == true","isSetForDesignTime":true,"draggable":true,"isopen":true}],"key":"element_element_block_0_0_flexIcon_0_0","parentElementKey":"element_block_0_0"},{"name":"Block","element":"block","size":{"isResponsive":true,"default":"12","large":"12","medium":"12","small":"12"},"stateIndex":0,"class":"slds-col ","property":{"label":"FinancialAccounts","collapsible":false,"record":"{record}","collapsedByDefault":false,"card":"{card}"},"type":"block","styleObject":{"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":" slds-p-around_x-small ","sizeClass":"slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 ","margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"size":{"isResponsive":true,"default":"12","large":"12","medium":"12","small":"12"},"container":{"class":""},"border":{"type":[],"width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"      \n         "},"children":[{"name":"Radio","element":"flexRadioInput","size":{"isResponsive":false,"default":"11"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","type":"radiogroup.vertical","card":"{card}","propertyObj":{"options":[],"label":"","radioDisplayValue":"radiogroup.vertical","controlWidth":"100","controlHeight":"100","imageCountInRow":"3","enabledCaption":"true","name":"Block-1-Radio-1"}},"type":"element","styleObject":{"size":{"isResponsive":false,"default":"11"},"padding":[{"type":"right","size":"small"}],"margin":[{"type":"bottom","size":"xx-small"}],"class":"slds-p-right_x-small","sizeClass":"slds-size_11-of-12 "},"elementLabel":"TopicInfo-Radio-2","key":"element_element_element_block_0_0_block_1_0_flexRadioInput_0_0","parentElementKey":"element_element_block_0_0_block_1_0"},{"name":"Text","element":"outputField","size":{"isResponsive":true,"default":"12","large":"12","medium":"12","small":"12"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%7BcontactName%7D%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 ","size":{"isResponsive":true,"default":"12","large":"12","medium":"12","small":"12"},"padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","class":"","style":"      \n         "},"elementLabel":"Info-Text-1","key":"element_element_element_block_0_0_block_1_0_outputField_1_0","parentElementKey":"element_element_block_0_0_block_1_0","userUpdatedElementLabel":true,"styleObjects":[{"key":0,"conditions":"default","styleObject":{"sizeClass":"slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 ","size":{"isResponsive":true,"default":"12","large":"12","medium":"12","small":"12"},"padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","class":"","style":"      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}]}],"elementLabel":"FinancialAccounts","styleObjects":[{"key":0,"conditions":"default","styleObject":{"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":" slds-p-around_x-small ","sizeClass":"slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 ","margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"size":{"isResponsive":true,"default":"12","large":"12","medium":"12","small":"12"},"container":{"class":""},"border":{"type":[],"width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false,"isSetForDesignTime":false,"isopen":true}],"key":"element_element_block_0_0_block_1_0","parentElementKey":"element_block_0_0","userUpdatedElementLabel":true}],"elementLabel":"Card","styleObjects":[{"key":0,"conditions":"default","styleObject":{"padding":[],"class":"slds-border_top slds-border_right slds-border_bottom slds-border_left ","sizeClass":"slds-size_12-of-12 ","margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"size":{"isResponsive":false,"default":"12","large":"12","medium":"12","small":"12"},"container":{"class":""},"border":{"type":["border_top","border_right","border_bottom","border_left"],"width":"1","color":"#cccccc","radius":".25em","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"max-width: 400px;\nmargin: auto;\nposition: relative;\npadding: 0 !important;","style":"     border-top: #cccccc 1px solid;border-right: #cccccc 1px solid;border-bottom: #cccccc 1px solid;border-left: #cccccc 1px solid; \n    border-radius:.25em;     max-width: 400px;\nmargin: auto;\nposition: relative;\npadding: 0 !important;"},"label":"Default","name":"Default","conditionString":"","draggable":false,"isSetForDesignTime":false,"isopen":true},{"key":1,"conditions":{"id":"state-condition-object","isParent":true,"group":[{"id":"state-new-condition-7","field":"Selected","operator":"==","value":"true","type":"custom","hasMergeField":false}]},"styleObject":{"padding":[],"class":"slds-theme_default slds-border_top slds-border_right slds-border_bottom slds-border_left ","sizeClass":"slds-size_12-of-12 ","margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"size":{"isResponsive":false,"default":"12"},"container":{"class":""},"border":{"type":["border_top","border_right","border_bottom","border_left"],"width":"1","color":"#1B96FF","radius":".25em","style":"solid"},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"max-width: 400px;\nmargin: auto;\npadding: 0 !important;\nposition: relative;\noutline: #1B96FF solid 1px;\n","style":"     border-top: #1B96FF 1px solid;border-right: #1B96FF 1px solid;border-bottom: #1B96FF 1px solid;border-left: #1B96FF 1px solid; \n    border-radius:.25em;     max-width: 400px;\nmargin: auto;\npadding: 0 !important;\nposition: relative;\noutline: #1B96FF solid 1px;\n","theme":"theme_default"},"label":"Selected","name":"Selected","conditionString":"Selected == true","isSetForDesignTime":true,"draggable":true,"isopen":true}],"userUpdatedElementLabel":true}]}},"childCards":[],"actions":[],"omniscripts":[],"documents":[],"blankCardState":false}],"dataSource":{"type":"Custom","value":{"dsDelay":"","body":"[\n    {\n      \"name\": \"Julia Green\",\n      \"Name\": \"Adv Plus Banking\",\n      \"Id\": \"a0URM0000004OCM2A2\",\n      \"AccountNumberLast4\": \"3509\",\n      \"RecordTypeName\": \"Checking Account\",\n      \"FinancialAccountNumber\": \"********\",\n      \"Role\": \"Primary Owner\"\n    },\n{\n      \"name\": \"Julia Red\",\n      \"Name\": \"Adv Plus Banking\",\n      \"Id\": \"a0URM0000004OCM2A2\",\n      \"AccountNumberLast4\": \"3509\",\n      \"RecordTypeName\": \"Checking Account\",\n      \"FinancialAccountNumber\": \"********\",\n      \"Role\": \"Attorney\"\n    }\n  ]","resultVar":""},"orderBy":{"name":"","isReverse":""},"contextVariables":[]},"title":"ComplaintRelatedContacts","enableLwc":true,"isFlex":true,"theme":"slds","selectableMode":"Single","lwc":{"DeveloperName":"cfFSCFeeReversalFinancialAccounts","Id":"0Rbxx0000004GB2CAM","MasterLabel":"cfFSCFeeReversalFinancialAccounts","NamespacePrefix":"c","ManageableState":"unmanaged"},"multilanguageSupport":false,"osSupport":true,"events":[{"eventname":"selectcards_relatedcontact","channelname":"selecttopic","element":"action","eventtype":"event","recordIndex":"0","actionList":[{"key":"*************-i9u0f1whe","label":"Selected = true","draggable":true,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-*************","type":"cardAction","displayName":"Action","vlocityIcon":"standard-default","targetType":"Web Page","openUrlIn":"Current Window","Web Page":{"targetName":"/apex"},"eventName":"setValues","fieldValues":[{"fieldName":"Selected","fieldValue":"true"}]},"actionIndex":0},{"key":"*************-40lpgqutp","label":"Update Omniscript","draggable":false,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-*************","type":"updateOmniScript","displayName":"Action","vlocityIcon":"standard-default","openUrlIn":"Current Window","elementId":"selected","hasExtraParams":true,"extraParams":{"relatedcontact":"{relatedcontact}"}},"actionIndex":1}],"key":"event-0","displayLabel":"selectcards_relatedcontact","eventLabel":"custom event","showSpinner":"false"}],"selectedCardsLabel":"relatedcontact","selectableField":"Selected","dynamicCanvasWidth":{"type":"desktop"},"listenToWidthResize":true,"Name":"ComplaintRelatedContacts","uniqueKey":"ComplaintRelatedContacts_Salesforce_1","Id":"flexmetadata0.461973508553199961721917479639","OmniUiCardType":"Parent"};
  export default definition