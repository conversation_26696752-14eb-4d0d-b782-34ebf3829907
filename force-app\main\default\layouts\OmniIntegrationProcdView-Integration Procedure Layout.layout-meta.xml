<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IntegrationProcedureName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SubType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>VersionNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>VersionCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsActive</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsTestProcedure</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsFileBased</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsManagedUsingStdDesigner</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsIntegProcdSignatureAvl</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IntegrationProcedureInput</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IntegrationProcedureOutput</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
