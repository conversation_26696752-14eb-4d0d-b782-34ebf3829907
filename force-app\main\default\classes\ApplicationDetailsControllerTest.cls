@isTest
private class ApplicationDetailsControllerTest {
    @testSetup
    static void setupTestData() {
        // Create application
        DAO_Application__c application = new DAO_Application__c(DBA_Name__c = 'Test App');
        insert application;

        // Create role
        DAO_Roles__c role = new DAO_Roles__c(Name = 'Test Role', DAO_Application__c = application.Id, Individual_Role__c = 'Controlling Individual');
        insert role;

        // Create product
        DAO_Products__c product = new DAO_Products__c(Name = 'Test Product', DAO_Application__c = application.Id, Type_of_Account__c = 'Savings');
        insert product;
    }

    @isTest
    static void testGetApplicationDetails() {
        DAO_Application__c app = [SELECT Id FROM DAO_Application__c LIMIT 1];
        Test.startTest();
        DAO_Application__c result = ApplicationDetailsController.getApplicationDetails(app.Id);
        Test.stopTest();
        System.assertNotEquals(null, result);
        System.assertEquals(app.Id, result.Id);
    }

    @isTest
    static void testGetApplicationRoles() {
        DAO_Application__c app = [SELECT Id FROM DAO_Application__c LIMIT 1];
        Test.startTest();
        List<DAO_Roles__c> roles = ApplicationDetailsController.getApplicationRoles(app.Id);
        Test.stopTest();
        System.assert(roles.size() > 0);
    }

    @isTest
    static void testGetApplicationProducts() {
        DAO_Application__c app = [SELECT Id FROM DAO_Application__c LIMIT 1];
        Test.startTest();
        List<DAO_Products__c> products = ApplicationDetailsController.getApplicationProducts(app.Id);
        Test.stopTest();
        System.assert(products.size() > 0);
    }
}