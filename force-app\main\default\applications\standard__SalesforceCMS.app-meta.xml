<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Opportunity_Record_Lighting_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
    </actionOverrides>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Salesforce CMS</label>
    <navType>Console</navType>
    <tabs>standard-CmsAuthorHome</tabs>
    <tabs>standard-CmsChannel</tabs>
    <tabs>standard-CmsWorkspaces</tabs>
    <tabs>standard-CmsExperiences</tabs>
    <tabs>Email_Preferences__c</tabs>
    <tabs>Email_Preference_Category__c</tabs>
    <uiType>Lightning</uiType>
    <workspaceConfig>
        <mappings>
            <tab>Email_Preference_Category__c</tab>
        </mappings>
        <mappings>
            <tab>Email_Preferences__c</tab>
        </mappings>
        <mappings>
            <tab>standard-CmsAuthorHome</tab>
        </mappings>
        <mappings>
            <tab>standard-CmsChannel</tab>
        </mappings>
        <mappings>
            <tab>standard-CmsExperiences</tab>
        </mappings>
        <mappings>
            <tab>standard-CmsWorkspaces</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
