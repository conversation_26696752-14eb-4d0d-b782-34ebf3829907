<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Everything_matches_except_loan_officer</name>
        <label>Everything matches except loan officer</label>
        <locationX>314</locationX>
        <locationY>647</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <stringValue><EMAIL>,<EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>No opp/lead linkage due to loan officer email lookup</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>LoanOfficerMissmatch</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Send_fault_email</name>
        <label>Send fault email</label>
        <locationX>842</locationX>
        <locationY>431</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <stringValue><EMAIL>,<EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>A fault occurred</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <stringValue>A fault has occurred</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Send_successful_linkage_message</name>
        <label>Send successful linkage message</label>
        <locationX>50</locationX>
        <locationY>863</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <stringValue><EMAIL>,<EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>Successful Lead &lt;-&gt; Opportunity linkage</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>emailBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Any_lead_records</name>
        <label>Any lead records</label>
        <locationX>380</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>No lead record exists</defaultConnectorLabel>
        <rules>
            <name>Lead_record_exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_leads_related_to_borrower_account</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_loan_officer</targetReference>
            </connector>
            <label>Lead record exists</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_loan_officer</name>
        <label>Check loan officer</label>
        <locationX>182</locationX>
        <locationY>539</locationY>
        <defaultConnector>
            <targetReference>Everything_matches_except_loan_officer</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Loan_officer_match</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_leads_related_to_borrower_account.Owner:User.Email</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>$Record.Loan_Officer__r.Email</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_lead_empower_opportunity_linkage</targetReference>
            </connector>
            <label>Loan officer match</label>
        </rules>
    </decisions>
    <description>Mortgage lead search by opportunity</description>
    <environments>Default</environments>
    <interviewLabel>Mortgage lead search by opportunity {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Mortgage lead search by opportunity</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <description>Get leads related to borrower account</description>
        <name>Get_leads_related_to_borrower_account</name>
        <label>Get leads related to borrower account</label>
        <locationX>380</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Any_lead_records</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Send_fault_email</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Email</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Account.PersonEmail</elementReference>
            </value>
        </filters>
        <filters>
            <field>Empower_Opportunity__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Mortgage_Lead_Status__c</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Funded</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsDeleted</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>IsConverted</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Lead</object>
        <sortField>CreatedDate</sortField>
        <sortOrder>Desc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <description>Update empower opportunity lead linkage</description>
        <name>Update_empower_opportunity_lead_linkage</name>
        <label>Update empower opportunity lead linkage</label>
        <locationX>50</locationX>
        <locationY>755</locationY>
        <connector>
            <targetReference>Send_successful_linkage_message</targetReference>
        </connector>
        <inputAssignments>
            <field>Empower_Lead__c</field>
            <value>
                <elementReference>Get_leads_related_to_borrower_account.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_lead_empower_opportunity_linkage</name>
        <label>Update lead empower opportunity linkage</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <connector>
            <targetReference>Update_empower_opportunity_lead_linkage</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_leads_related_to_borrower_account.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Empower_Opportunity__c</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Empower_Stage__c</field>
            <value>
                <elementReference>$Record.StageName</elementReference>
            </value>
        </inputAssignments>
        <object>Lead</object>
    </recordUpdates>
    <start>
        <locationX>254</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_leads_related_to_borrower_account</targetReference>
        </connector>
        <filterFormula>AND (
  {!$Record.RecordType.Name} = &apos;Mortgage Opportunity&apos;,
  OR (
      {!$Record.BK_External_ID__c} = &apos;&apos;,
      NOT(BEGINS(LOWER({!$Record.BK_External_ID__c}), &apos;he&apos;))
  )
)</filterFormula>
        <object>Opportunity</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <name>emailBody</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;A lead and opportunity were successfully linked. &lt;/p&gt;&lt;p&gt;LEAD: {!Get_leads_related_to_borrower_account.Name} --  {!Get_leads_related_to_borrower_account.Id}&lt;/p&gt;&lt;p&gt;OPPORTUNITY: {!$Record.Name} -- {!$Record.Id}&lt;/p&gt;</text>
    </textTemplates>
    <textTemplates>
        <name>LoanOfficerMissmatch</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;An Opportunity closely matches a Lead with the exception of the Loan Officers email address. If the Lead and Opportunity shown below do represent the same loan, please manually connect the Lead and the Opportunity:&lt;/p&gt;&lt;ul&gt;&lt;li&gt;First add the Lead ID (show below) to the &apos;Empower Lead&apos; field on the Opportunity record&lt;/li&gt;&lt;li&gt;Second add the Opportunity ID (shown below) to the &apos;Empower Opportunity&apos; field on the Lead record.&lt;/li&gt;&lt;/ul&gt;&lt;p&gt;Account record email: {!$Record.Account.PersonEmail}&lt;/p&gt;&lt;p&gt;Loan # (BK_External_ID__c): {!$Record.BK_External_ID__c}&lt;/p&gt;&lt;p&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;Opportunity Loan Officer email: {!$Record.Account.PersonEmail}&lt;/p&gt;&lt;p&gt;Opportunity ID: {!$Record.Id}&lt;/p&gt;&lt;p&gt;Lead Owner email: {!Get_leads_related_to_borrower_account.Owner:User.Email}&lt;/p&gt;&lt;p&gt;Lead ID: {!Get_leads_related_to_borrower_account.Id}&lt;/p&gt;</text>
    </textTemplates>
</Flow>
