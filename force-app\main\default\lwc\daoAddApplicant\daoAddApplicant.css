.heading
{
	background: #edf5e4;
	font-weight: bold;
	border: none;
	font-size: 18px;
	padding: 10px;
	padding-top: 20px;
}
.sub-heading
{
	background: #f3f3f3;
}
.evenodd:nth-child(odd)
{
	background-color: #ffffff;
}
.evenodd:nth-child(even)
{
	background-color: #fafafa;
}
.addRoleButton
{
	 padding: 20px 30px; /* Padding */
      background: linear-gradient(45deg, #046a38, #84bd00); /* Gradient background */
      color: #ffffff; /* Text color */
      border-radius: 5px; /* Rounded corners */
      text-decoration: none; /* No underline */
      font-weight: bold; /* Bold text */
      border: none; /* Remove default border */
      cursor: pointer; /* Pointer on hover */
      transition: background 0.3s ease; 
}
.addRoleButton:hover
{	
	background: #046a38;
}