<template>
    <omnistudio-flex-card-standard-runtime-wrapper flexcard-name="RCU_DAO_LandingPage"
      record-id={recordId}
	object-api-name={objectApiName}
	theme={theme}
	org-ns-prefix={orgNsPrefix}
	session-vars={sessionVars}
	search-param={searchParam}
	obj={obj}
	is-recursive={isRecursive}
	debug={debug}
	is-child-card-tracking-enabled={isChildCardTrackingEnabled}
	tracking-obj={trackingObj}
	test-params={testParams}
	size={size}
	records={records}
	card-node={cardNode}
	parent-data={parentData}
	parent-uniquekey={parentUniquekey}
	is-inside-parent={isInsideParent}
	parent-record={parentRecord}
	parent-records={parentRecords}
	parent-attribute={parentAttribute}
	parent-mergefields={parentMergefields}
	listen-os-data-change={listenOsDataChange}
	omni-json-data={omniJsonData}
      >
    </omnistudio-flex-card-standard-runtime-wrapper>
</template>