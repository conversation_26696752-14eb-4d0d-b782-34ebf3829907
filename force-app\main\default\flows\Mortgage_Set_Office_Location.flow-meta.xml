<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Account_Id</name>
        <label>Assign Account Id</label>
        <locationX>50</locationX>
        <locationY>242</locationY>
        <assignmentItems>
            <assignToReference>AgentAccount</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Agent_Record_Type</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Account_Id_2</name>
        <label>Assign Account Id</label>
        <locationX>490</locationX>
        <locationY>242</locationY>
        <assignmentItems>
            <assignToReference>OfficeAccount</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Agent_Record_Type_copy</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Any_preexisting_records</name>
        <label>Any preexisting records</label>
        <locationX>50</locationX>
        <locationY>890</locationY>
        <defaultConnector>
            <targetReference>Empty_preexisting_agent_field</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>yes preexisting</defaultConnectorLabel>
        <rules>
            <name>no_preexisting</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Preexisting_records_1</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Agent_Office_field</targetReference>
            </connector>
            <label>no preexisting</label>
        </rules>
    </decisions>
    <decisions>
        <name>Any_preexisting_records_copy</name>
        <label>Any preexisting records</label>
        <locationX>490</locationX>
        <locationY>782</locationY>
        <defaultConnector>
            <targetReference>Empty_preexisting_agent_field_copy</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>yes preexisting</defaultConnectorLabel>
        <rules>
            <name>no_preexisting_copy</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Preexisting_records_2</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Agent_Office_field_copy</targetReference>
            </connector>
            <label>no preexisting</label>
        </rules>
    </decisions>
    <decisions>
        <name>Calling_Record</name>
        <label>Calling Record</label>
        <locationX>446</locationX>
        <locationY>134</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Agent</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Real_Estate_Agent</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Account_Id</targetReference>
            </connector>
            <label>Agent</label>
        </rules>
        <rules>
            <name>Office</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>recordId.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Real_Estate_Offices</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Account_Id_2</targetReference>
            </connector>
            <label>Office</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Mortgage Set Office Location {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Mortgage Set Office Location</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_ACR_Record</name>
        <label>Create ACR Record</label>
        <locationX>50</locationX>
        <locationY>1406</locationY>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>selectedOffice.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>AgentAccount.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Roles</field>
            <value>
                <stringValue>Real Estate Agent</stringValue>
            </value>
        </inputAssignments>
        <object>AccountContactRelation</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_ACR_Record_copy</name>
        <label>Create ACR Record</label>
        <locationX>490</locationX>
        <locationY>1298</locationY>
        <inputAssignments>
            <field>AccountId</field>
            <value>
                <elementReference>OfficeAccount.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ContactId</field>
            <value>
                <elementReference>selectedAgent.PersonContactId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Roles</field>
            <value>
                <stringValue>Real Estate Agent</stringValue>
            </value>
        </inputAssignments>
        <object>AccountContactRelation</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordDeletes>
        <name>Delete_preexisting_ACR_record</name>
        <label>Delete preexisting ACR record</label>
        <locationX>138</locationX>
        <locationY>1106</locationY>
        <connector>
            <targetReference>Update_Agent_Office_field</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ContactId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>AgentAccount.PersonContactId</elementReference>
            </value>
        </filters>
        <object>AccountContactRelation</object>
    </recordDeletes>
    <recordDeletes>
        <name>Delete_preexisting_ACR_recorda_copy</name>
        <label>Delete preexisting ACR record</label>
        <locationX>578</locationX>
        <locationY>998</locationY>
        <connector>
            <targetReference>Update_Agent_Office_field_copy</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ContactId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>selectedAgent.PersonContactId</elementReference>
            </value>
        </filters>
        <object>AccountContactRelation</object>
    </recordDeletes>
    <recordLookups>
        <name>Agent_Record_Type</name>
        <label>Agent Record Type</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Office_Record_Type</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Account</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Real_Estate_Agent</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Agent_Record_Type_copy</name>
        <label>Agent Record Type</label>
        <locationX>490</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Agents</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Account</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Real_Estate_Agent</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Agents</name>
        <label>Get Agents</label>
        <locationX>490</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Select_an_agent_from_the_list_below</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Agent_Record_Type_copy.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>PersonContactId</queriedFields>
        <queriedFields>Phone</queriedFields>
        <queriedFields>PersonEmail</queriedFields>
        <queriedFields>Name</queriedFields>
        <sortField>Name</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Offices</name>
        <label>Get Offices</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Select_an_office_from_the_list_below</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Office_Record_Type.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>Name</queriedFields>
        <queriedFields>ShippingStreet</queriedFields>
        <queriedFields>PersonContactId</queriedFields>
        <sortField>Name</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Office_Record_Type</name>
        <label>Office Record Type</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Offices</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Account</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Real_Estate_Offices</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Preexisting_records_1</name>
        <label>Preexisting records 1</label>
        <locationX>50</locationX>
        <locationY>782</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Any_preexisting_records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Roles</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Real Estate Agent</stringValue>
            </value>
        </filters>
        <filters>
            <field>ContactId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>AgentAccount.PersonContactId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>AccountContactRelation</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Preexisting_records_2</name>
        <label>Preexisting records 2</label>
        <locationX>490</locationX>
        <locationY>674</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Any_preexisting_records_copy</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Roles</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Real Estate Agent</stringValue>
            </value>
        </filters>
        <filters>
            <field>ContactId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>selectedAgent.PersonContactId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>AccountContactRelation</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Empty_preexisting_agent_field</name>
        <label>Empty preexisting agent field</label>
        <locationX>138</locationX>
        <locationY>998</locationY>
        <connector>
            <targetReference>Delete_preexisting_ACR_record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Agent_Record_Type.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Agent_Office__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>AgentAccount.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Agent_Office__c</field>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>Empty_preexisting_agent_field_copy</name>
        <label>Empty preexisting agent field</label>
        <locationX>578</locationX>
        <locationY>890</locationY>
        <connector>
            <targetReference>Delete_preexisting_ACR_recorda_copy</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Agent_Record_Type.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>PersonContactId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>selectedAgent.PersonContactId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Agent_Office__c</field>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Agent_Office_field</name>
        <label>Update Agent Office field</label>
        <locationX>50</locationX>
        <locationY>1298</locationY>
        <connector>
            <targetReference>Create_ACR_Record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>AgentAccount.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Agent_Office__c</field>
            <value>
                <elementReference>selectedOffice.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Agent_Office_field_copy</name>
        <label>Update Agent Office field</label>
        <locationX>490</locationX>
        <locationY>1190</locationY>
        <connector>
            <targetReference>Create_ACR_Record_copy</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>selectedAgent.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Agent_Office__c</field>
            <value>
                <elementReference>OfficeAccount.Id</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <screens>
        <name>Select_an_agent_from_the_list_below</name>
        <label>Select an agent from the list below</label>
        <locationX>490</locationX>
        <locationY>566</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Preexisting_records_2</targetReference>
        </connector>
        <fields>
            <name>Copy_1_of_Offices_Data_Table</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Account</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Select an Agent</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Agents</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-6f46&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Name&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Account Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;Phone&quot;,&quot;guid&quot;:&quot;column-ff95&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Account Phone&quot;,&quot;type&quot;:&quot;phone&quot;},{&quot;apiName&quot;:&quot;PersonEmail&quot;,&quot;guid&quot;:&quot;column-76c5&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Email&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Email&quot;,&quot;type&quot;:&quot;email&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <outputParameters>
                <assignToReference>selectedAgent</assignToReference>
                <name>firstSelectedRow</name>
            </outputParameters>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>Select_an_office_from_the_list_below</name>
        <label>Select an office from the list below</label>
        <locationX>50</locationX>
        <locationY>674</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Preexisting_records_1</targetReference>
        </connector>
        <fields>
            <name>Offices_Data_Table</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>Account</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Select an office</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>Get_Offices</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-693f&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Office Name&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Account Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;ShippingStreet&quot;,&quot;guid&quot;:&quot;column-bf70&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:true,&quot;customHeaderLabel&quot;:&quot;Street Address&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Shipping Street&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <outputParameters>
                <assignToReference>selectedOffice</assignToReference>
                <name>firstSelectedRow</name>
            </outputParameters>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>320</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Calling_Record</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>AgentAccount</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>OfficeAccount</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>selectedAgent</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>selectedOffice</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
</Flow>
