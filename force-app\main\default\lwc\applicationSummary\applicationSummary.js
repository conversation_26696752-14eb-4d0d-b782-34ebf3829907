import { LightningElement, wire } from "lwc";
import { CurrentPageReference } from "lightning/navigation";
import getApplicationDetails from "@salesforce/apex/ApplicationDetailsController.getApplicationDetails";
import getApplicationRoles from "@salesforce/apex/ApplicationDetailsController.getApplicationRoles";
import getApplicationProducts from "@salesforce/apex/ApplicationDetailsController.getApplicationProducts";

import { getPicklistValues, getObjectInfo } from "lightning/uiObjectInfoApi";
import DAO_Application from "@salesforce/schema/DAO_Application__c";
import Sources_of_funding_for_the_organization from "@salesforce/schema/DAO_Application__c.Sources_of_funding_for_the_organization__c";
import Methods_to_obtain_funding from "@salesforce/schema/DAO_Application__c.Methods_to_obtain_funding__c";

import { OmniscriptBaseMixin } from "omnistudio/omniscriptBaseMixin";

import pdfMakeLib from "@salesforce/resourceUrl/pdfmake"
import pdfFontsLib from "@salesforce/resourceUrl/vfs_fonts";
import { loadScript } from "lightning/platformResourceLoader";

export default class applicationSummary extends OmniscriptBaseMixin(LightningElement) {
    applicationRecordId = "";
    wireRecordId;
    acknowledged = false;

    @wire(CurrentPageReference)
    getStateParameters(currentPageReference) {
        if (currentPageReference) {
            //it gets executed before the connected callback and avilable to use
            this.wireRecordId = currentPageReference.attributes.recordId;
        }
    }

    pdfInitialized = false;

    renderedCallback() {
        if (this.pdfInitialized) return;
        this.pdfInitialized = true;

        // Load core pdfMake then its font definitions
        Promise.all([
            loadScript(this, pdfMakeLib), // loads pdfmake.min.js
            loadScript(this, pdfFontsLib) // loads vfs_fonts.js
        ])
            .then(() => {
                console.log("pdfMake and vfs_fonts loaded");
                // vfs_fonts.js automatically attaches window.pdfMake.vfs
            })
            .catch((err) => {
                console.error("Failed to load pdfMake or fonts", err);
            });
    }

    @wire(getObjectInfo, { objectApiName: DAO_Application })
    objectInfo;

    businessEssentialChecking = false;
    businessChoiceChecking = false;
    businessSavings = true;
    visaDebitCard = false;
    eStatements = false;
    onlineBanking = false;
    businessVisaCreditCard = false;

    overdraftSelection = "";
    overdraftSelectionPdf = '';
    overdraftOptions = [
        {
            label: "Checks and Electronic Transactions Only - RCU will pay overdrafts only on checks, electronic (ACH) and recurring debit card transactions. RCU will not pay everyday debit card transactions.",
            value: "Electronic"
        },
        {
            label: "All Overdraft Pay Advantage Service - RCU will pay overdrafts on checks, electronic (ACH) and recurring debit card transactions, and everyday debit card transactions.",
            value: "Overdraft"
        },
        {
            label: "No Overdraft Pay Advantage Services - RCU will not pay overdrafts on checks, electronic (ACH) and everyday debit card transactions. Non-sufficient funds (NSF) fees will apply.",
            value: "No Overdraft"
        }
    ];

    businessName = "Sample Business Name";
    fbaOrDba = false;

    businessTaxIdType = "SSN";
    businessTaxIdTypeOptions = [
        { label: "EIN", value: "EIN" },
        { label: "SSN", value: "SSN" }
    ];
    businessTaxId = "";
    businessStructure = "";

    get businessTaxIdLabel() {
        return this.businessTaxIdType === "SSN" ? "SSN" : "Business Tax ID";
    }

    yesNoOptions = [
        { label: "Yes", value: "Yes" },
        { label: "No", value: "No" }
    ];
    isPublicCompany = this.convertBooleanToYesNo(false);
    tradedOnStockExchange = this.convertBooleanToYesNo(false);
    ownedByListedEntity = this.convertBooleanToYesNo(false);

    isHeadquarteredInUS = this.convertBooleanToYesNo(false);
    headquarteredCountry = "";
    registeredInCalifornia = this.convertBooleanToYesNo(false);

    qualifiesForNonProfitStatus = this.convertBooleanToYesNo(false);
    charitableObjectives = "";
    organizationCharteredCountry = "";
    registeredAs501c = this.convertBooleanToYesNo(false);

    fundingSources = [];
    @wire(getPicklistValues, {
        recordTypeId: "$objectInfo.data.defaultRecordTypeId",
        fieldApiName: Sources_of_funding_for_the_organization
    })
    wiredPicklist({ error, data }) {
        if (data) {
            this.fundingSourcesOptions = data.values;
        } else if (error) {
            console.error("Error fetching picklist values:", error);
        }
    }
    fundingSourcesOptions;
    otherSourceDescription = "";

    fundingMethods = [];
    @wire(getPicklistValues, {
        recordTypeId: "$objectInfo.data.defaultRecordTypeId",
        fieldApiName: Methods_to_obtain_funding
    })
    wiredPicklist2({ error, data }) {
        if (data) {
            this.fundingMethodsOptions = data.values;
        } else if (error) {
            console.error("Error fetching picklist values:", error);
        }
    }
    fundingMethodsOptions;
    otherFundingDescription = "";

    annualFundingValue = "";

    nonUSDonorsOrVolunteers = this.convertBooleanToYesNo(false);
    donorsCountries = "";
    charitableServicesAbroad = this.convertBooleanToYesNo(false);
    charitableServiceCountries = "";
    foreignBeneficiariesCountries = "";

    establishDate = "";
    annualRevenue = "";
    numberOfEmployees = "";
    businessHeadquartersCounty = "";
    otherHeadquartersCounty = "";
    stateRegistered = "";
    preferredBranch = "";
    businessDescription = "";
    naicsCode = "";

    physicalStreet = "";
    physicalCity = "";
    physicalState = "";
    physicalPostalCode = "";
    physicalCountry = "";
    useDifferentMailingAddress = false;
    mailingStreet = "";
    mailingCity = "";
    mailingState = "";
    mailingPostalCode = "";
    mailingCountry = "";

    occupancyStatus = "";
    otheroccupancyStatus = "";
    occupancyDurationYears = "";
    occupancyDurationMonths = "";
    businessEmail = "";
    businessPhone = "";
    businessCellPhone = "";
    businessFax = "";

    businessRoles = [];

    generalOperationFunds = false;
    payroll = false;
    savings = false;
    creditCardProcessing = false;
    ioltaIola = false;
    lottery = false;
    msbActivity = false;
    privateBanking = false;
    otherDueDiligence = false;
    otherDueDiligenceNotes = "";

    privateLabelCardAccount = false;
    retailAccountDetails = "";

    postageRemittance = false;
    postagePurchaseUsage = "";
    thirdPartyPayments = "";
    cashRefundPossible = "";

    equipmentPurchaseOrLease = false;
    equipmentFinanceUsage = "";
    equipmentThirdPartyPayments = "";
    equipmentCashRefundPossible = "";

    pooledInvestmentVehicle = false;
    pooledInvestmentExcludedAdvisor = "";
    pooledPersonalUse = "";
    pooledMajorCustomerVendor = "";

    cashDepositsInPerson = false;
    cashDepositsInPersonAmount = "";
    atmCashDeposits = false;
    atmCashDepositsAmount = "";
    cashWithdrawalsInPerson = false;
    cashWithdrawalsInPersonAmount = "";
    atmCashWithdrawals = false;
    atmCashWithdrawalsAmount = "";
    checkDeposits = false;
    checkDepositsAmount = "";
    remoteDepositCapture = false;
    checkWithdrawals = false;
    checkWithdrawalsAmount = "";
    cashiersChecks = false;
    cashiersChecksAmount = "";

    incomingWireTransfers = false;
    incomingWireTransferAmount = "";
    receiveIntlTransfers = "";
    receiveIntlTransferCountries = "";

    outgoingWireTransfers = false;
    outgoingWireTransferAmount = "";
    sendIntlTransfers = "";
    sendIntlTransferCountries = "";

    incomingNonWireTransfers = false;
    incomingNonWireTransferAmount = "";
    receiveIntlNonWireTransfers = "";
    receiveIntlNonWireCountries = "";

    outgoingNonWireTransfers = false;
    outgoingNonWireTransferAmount = "";
    sendIntlNonWireTransfers = "";
    sendIntlNonWireCountries = "";

    internetGamblingIncome = "";
    illegalBusiness = "";

    marijuanaRelatedBusiness = "";
    marijuanaLicensedValue = "";
    marijuanaRevenuePercentageValue = "";
    marijuanaActivityTypeValue = "";

    professionalServiceProvider = "";
    professionalTypeValue = "";
    professionalTypeOtherTextValue = "";
    servicesTypesValue = "";
    professionalOthersUsingValue = "";

    courierServices = "";

    paymentProcessor = "";
    transactionsSendPaymentsChecked = "";
    transactionsReceivePaymentsChecked = "";
    paymentServicesValue = "";
    paymentServicesOtherTextValue = "";
    paymentsThroughAccountsValue = "";
    paymentsHowProcessedValue = "";
    createChecksRemotelyValue = "";
    businessTypeRestrictionsValue = "";
    businessTypeRestrictionsTextValue = "";

    atmOperator = "";
    numberOfAtmValue = "";
    replenishAtmCashValue = "";
    sourceOfAtmCashValue = "";
    atmMaxHoldingValue = "";
    atmDenominationValue = "";
    privateAtmTypeValue = "";

    nbfi = "";
    involveCasinosChecked = "";
    involveSecuritiesChecked = "";
    securitiesFinancialInstitutionValue = "";
    securitiesHowBusinessRegisteredValue = "";
    securitiesInvolveSecuritiesValue = "";
    securitiesProductTypesValue = "";
    securitiesInvestFundsInternationallyValue = "";
    securitiesCountriesTextValue = "";
    securitiesServiceTypesValue = "";
    involveInsuranceChecked = "";
    involveInsuranceStateRegInsValue = "";
    involveLoanFinanceChecked = "";
    involveCreditCardsChecked = "";
    involvePreciousMetalsChecked = "";
    involvePreciousMetalsBuy50kValue = "";
    involvePreciousMetalsSell50kValue = "";
    involvePawnBrokerageChecked = "";
    involveTravelAgencyChecked = "";
    involveTelegraphCompanyChecked = "";
    involveVehicleSalesChecked = "";
    involveVehicleTypesValue = "";
    involveRealEstateClosingChecked = "";
    involvePostalServiceChecked = "";
    involveGovAgencyChecked = "";
    docBsaAmlProgramValue = "";

    msb = "";
    involveCurrencyExchangeChecked = "";
    currencyExchangeAgentValue = "";
    currencyExchangePrincipalValue = "";
    involveCashChecksChecked = "";
    involveCheckTypesValue = "";
    involveMoneyOrdersChecked = "";
    moneyOrdersAgentValue = "";
    moneyOrdersPrincipalValue = "";
    involveTransmitMoneyChecked = "";
    transmitMoneyAgentValue = "";
    transmitMoneyPrincipalValue = "";
    transmitMoneyNonUsLocationsValue = "";
    transmitForeignCountriesTextValue = "";
    transmitMoneyTypesValue = "";
    transmitMoneyCvcValue = "";
    transmitMoneyActivitiesValue = "";
    involveVirtualCurrencyChecked = "";
    involveGiftCardsChecked = "";
    giftCardAgentValue = "";
    giftCardPrincipalValue = "";
    giftCardExceedDailyMaxValue = "";
    giftCardActivationProcessValue = "";
    giftCardNetworkBrandedValue = "";
    giftCardAccessDailyMaxValue = "";
    giftCardReloadedValue = "";
    giftCardTransferFundsValue = "";
    giftCardTransferFundsInternationallyValue = "";
    giftCardRequireCustomerIdValue = "";
    giftCardPreventSalesValue = "";
    giftCardBsaAmlProgValue = "";
    registeredFinCenValue = "";
    involveNoneChecked = "";

    businessSavingsDeposit = "";
    businessCheckingDeposit = "";
    totalDeposit = "";
    fundingType = "";
    fundingAccountType = "";
    nameOnAccount = "";
    accountNumber = "";
    routingNumber = "";
    financialInstitution = "";
    financialInstitutionState = "";

    previousStreet = "";
    previousCity = "";
    previousState = "";
    previousZipcode = "";

    checkedAck;
    useDifferentMailingAddressApplicant = false;
    applicant = false;
    primaryIsMobile = false;
    secondaryIsMobile = false;


    connectedCallback() {
       
        if (this.omniJsonData?.DRId_DAO_Application__c) {
            this.applicationRecordId = this.omniJsonData.DRId_DAO_Application__c;
        } else if (this.omniJsonData?.applicationId) {
            this.applicationRecordId = this.omniJsonData.applicationId;
            console.log("Using omniJsonData applicationId:", this.applicationRecordId);
        } else {
            this.applicationRecordId = this.wireRecordId;
        }

        getApplicationProducts({ applicationId: this.applicationRecordId })
            .then((result) => {

                // Loop through result array
                result.forEach((item) => {
                    if (item.Type_of_Account__c !== "Savings") {
                        this.businessEssentialChecking = item.Type_of_Account__c === "Essential Checking" ? true : false;
                        this.businessChoiceChecking = item.Type_of_Account__c === "Choice Checking" ? true : false;

                        this.visaDebitCard = this.convertYesNoToBoolean(item.Visa_Debit_Card__c);
                        this.eStatements = this.convertYesNoToBoolean(item.eStatements__c);
                        this.onlineBanking = this.convertYesNoToBoolean(item.Online_Banking__c);
                        this.businessVisaCreditCard = this.convertYesNoToBoolean(item.Want_Business_Visa_Credit_Card__c);

                        if(item.Checks_and_Electronic_Transactions_Only__c === 'Yes' ) {
                            this.overdraftSelection = "Electronic";
                            this.overdraftSelectionPdf = 'Checks and Electronic Transactions Only';
                        } else if(item.All_Overdraft_Pay_Advantage_Services__c === 'Yes') {
                            this.overdraftSelection = "Overdraft";
                            this.overdraftSelectionPdf = 'All Overdraft Pay Advantage Services';
                        } else if(item.No_Overdraft_Pay_Advantage_Services__c === 'Yes') {
                            this.overdraftSelection = "No Overdraft";
                            this.overdraftSelectionPdf = 'No Overdraft Pay Advantage Services';
                        }
                    } else {
                        this.businessSavings = true;
                    }
                });                
            })
            .catch((error) => {
                console.error("Error fetching application products:", error);
            });

        getApplicationDetails({ applicationId: this.applicationRecordId })
            .then((result) => {
                console.log('$application-result: ',result);
                this.businessName = result.DBA_Name__c;

                this.fbaOrDba = result.Fictitious_Business_Name_DBA__c;
                this.businessTaxIdType = result.Business_Tax_ID_Type__c;
                this.businessTaxId = this.businessTaxIdType === "EIN"
                     ? (result.Business_Tax_ID__c ? 'xx-xxx' + result.Business_Tax_ID__c.slice(-4) : '')
                     : (result.Applicant_SSN__c ? 'xx-xxx' + result.Applicant_SSN__c.slice(-4) : '');
                this.businessStructure = result.Business_Structure__c;
                this.establishDate = result.Establish_Date__c;
                this.annualRevenue = result.Business_Annual_Revenue__c; 
                this.numberOfEmployees = result.Number_of_Employees__c;
                this.businessHeadquartersCounty = result.Business_HQ_County__c;
                this.stateRegistered = result.State_Registered__c;
                this.otherHeadquartersCounty = result.Other_Headquarters_County__c;
                this.principalLocation = result.Business_Headquarters_County__c;
                this.preferredBranch = result.Preferred_Branch__c;
                this.physicalStreet = result.BusinessStreet__c;
                this.physicalCity = result.BusinessCity__c;
                this.physicalState = result.BusinessState__c;
                this.physicalPostalCode = result.BusinessZip__c;
                this.physicalCountry = "US";
                this.mailingStreet = result.MailingStreet__c;
                this.mailingCity = result.MailingCity__c;
                this.mailingState = result.MailingState__c;
                this.mailingPostalCode = result.MailingZip__c;
                this.mailingCountry = "US";
                this.occupancyStatus = result.Occupancy_Status__c;
                this.occupancyDurationYears = result.Occupancy_Duration_Years__c;
                this.occupancyDurationMonths = result.Occupancy_Duration_Months__c;
                this.businessEmail = result.Business_Email__c;
                this.businessCellPhone = result.Business_Cell_Phone__c;
                this.businessPhone = result.Business_Phone__c;
                this.businessFax = result.Business_Fax__c;

                this.isPublicCompany = result.Is_this_a_publicly_traded_company__c;
                this.tradedOnStockExchange = result.Traded_on_the_stock_exchange__c;
                this.ownedByListedEntity = result.Owned_by_an_entitiy_on_stock_exchange__c;
                this.isHeadquarteredInUS = result.Business_headquartered_in_US__c;
                this.headquarteredCountry = result.Country_business_headquartered__c;
                this.registeredInCalifornia = result.Registered_to_do_business_in_California__c;
                this.qualifiesForNonProfitStatus = result.Depend_on_Charitable_donations_voluntar__c;
                this.charitableObjectives = result.Objectives_Programs_Activities_Services__c;
                this.organizationCharteredCountry = result.Chartered_Organization_Country__c;
                this.registeredAs501c = result.Registered_as_a_Non_profit__c;

                this.fundingSources = result.Sources_of_funding_for_the_organization__c ? result.Sources_of_funding_for_the_organization__c.split(";") : [];
                this.fundingMethods = result.Methods_to_obtain_funding__c ? result.Methods_to_obtain_funding__c.split(";") : [];

                this.annualFundingValue = result.Value_of_annual_funding_or_gross_receipt__c;
                this.nonUSDonorsOrVolunteers = result.Volunteers_from_non_US_countries__c;
                this.donorsCountries = result.Donor_or_volunteer_countries__c;
                this.charitableServicesAbroad = result.Will_services_benefit_individuals_in_for__c;
                this.charitableServiceCountries = result.In_what_countries_do_you_provide_charita__c;
                this.foreignBeneficiariesCountries = result.Countries_your_foreign_beneficiaries__c;

                this.generalOperationFunds = result.General_Operation_Funds__c;
                this.payroll = result.Payroll__c;
                this.savings = result.Savings__c;
                this.creditCardProcessing = result.Credit_Card_Processing__c;
                this.ioltaIola = result.IOLTA_IOLA__c;
                this.lottery = result.Lottery__c;
                this.msbActivity = result.MSB_Activity__c;
                this.privateBanking = result.Private_Banking__c;
                this.otherDueDiligence = result.Other__c;
                this.otherDueDiligenceNotes = result.Other_Notes__c;

                this.privateLabelCardAccount = result.Private_Label_Credit_Card__c;
                this.retailAccountDetails = result.Private_Label_Credit_Card_Point_of_Sale__c;

                this.postageRemittance = result.Postage_Remittance__c;
                this.postagePurchaseUsage = result.Postage_Purchase__c;
                this.thirdPartyPayments = result.Postage_Third_Parties__c;
                this.cashRefundPossible = result.Postage_Cash_Refund__c;

                this.equipmentPurchaseOrLease = result.Equipment_or_Lease__c;
                this.equipmentFinanceUsage = result.Equipment_Purchase__c;
                this.equipmentThirdPartyPayments = result.Equipment_Third_Parties__c;
                this.equipmentCashRefundPossible = result.Equipment_Cash_Refund__c;

                this.pooledInvestmentVehicle = result.Pooled_Investment_Vehicle__c;
                this.pooledInvestmentExcludedAdvisor = result.Excluded_Financial_Institution__c;
                this.pooledPersonalUse = result.Personal_Banking_Activity__c;
                this.pooledMajorCustomerVendor = result.Major_Vendors_or_Customers__c;

                this.cashDepositsInPerson = result.Cash_Deposits__c;
                this.cashDepositsInPersonAmount = result.Cash_Deposits_Avg_Amount__c;

                this.atmCashDeposits = result.ATM_Cash_Deposits__c;
                this.atmCashDepositsAmount = result.ATM_Cash_Deposits_Avg_Amount__c;

                this.cashWithdrawalsInPerson = result.Cash_Withdrawals__c;
                this.cashWithdrawalsInPersonAmount = result.Cash_Withdrawals_Avg_Amount__c;

                this.atmCashWithdrawals = result.ATM_Cash_Withdrawals__c;
                this.atmCashWithdrawalsAmount = result.ATM_Cash_Withdrawals_Avg_Amount__c;

                this.checkDeposits = result.Check_Deposits__c;
                this.checkDepositsAmount = result.Check_Deposits_Avg_Amount__c;

                this.remoteDepositCapture = result.Remote_Deposit_Capture__c;

                this.checkWithdrawals = result.Check_Withdrawals__c;
                this.checkWithdrawalsAmount = result.Check_Withdrawals_Avg_Amount__c;

                this.cashiersChecks = result.Cash_Deposits_Courier__c;
                this.cashiersChecksAmount = result.Cash_Withdrawals_Courier__c;

                this.incomingWireTransfers = result.Incoming_Wire_Transfers__c;
                this.incomingWireTransferAmount = result.Incoming_Wire_Transfers_Avg_Amount__c;
                this.receiveIntlTransfers = result.Incoming_Wire_Transfers_Non_US__c;
                this.receiveIntlTransferCountries = result.Incoming_Wire_Transfers_Countries__c;

                this.outgoingWireTransfers = result.Outgoing_Wire_Transfers__c;
                this.outgoingWireTransferAmount = result.Outgoing_Wire_Transfers_Avg_Amount__c;
                this.sendIntlTransfers = result.Outgoing_Wire_Transfers_Non_US__c;
                this.sendIntlTransferCountries = result.Outgoing_Wire_Transfers_Countries__c;

                this.incomingNonWireTransfers = result.Incoming_Electronic_Transfers__c;
                this.incomingNonWireTransferAmount = result.Incoming_Electronic_Transfers_Avg_Amount__c;
                this.receiveIntlNonWireTransfers = result.Incoming_Electronic_Transfers_Non_US__c;
                this.receiveIntlNonWireCountries = result.Incoming_Electronic_Transfers_Countries__c;

                this.outgoingNonWireTransfers = result.Outgoing_Electronic_Transfers__c;
                this.outgoingNonWireTransferAmount = result.Outgoing_Electronic_Transfers_Avg_Amount__c;
                this.sendIntlNonWireTransfers = result.Outgoing_Electronic_Transfers_Non_US__c;
                this.sendIntlNonWireCountries = result.Outgoing_Electronic_Transfers_Countries__c;

                this.illegalBusiness = result.is_Illegal_Business__c;

                this.internetGamblingIncome = result.Internet_Gambling__c;

                this.marijuanaRelatedBusiness = result.Marijuana_Business__c;
                this.marijuanaLicensedValue = result.MarijuanaLicensed__c;
                this.marijuanaRevenuePercentageValue = result.MarijuanaPercentage__c;
                this.marijuanaActivityTypeValue = result.MarijuanaActivity__c;

                this.professionalServiceProvider = result.Intermediary__c;
                this.professionalTypeValue = result.ProfessionalType__c;
                this.professionalTypeOtherTextValue = result.ProfessionalTypeOtherText__c;
                this.servicesTypesValue = result.ServicesTypes__c;
                this.professionalOthersUsingValue = result.ProfessionalOthersUsing__c;

                this.courierServices = result.Courier_Services__c;

                this.paymentProcessor = result.Third_Party_Payment_Processor__c;
                this.transactionsSendPaymentsChecked = result.TransactionsSendPayments__c;
                this.transactionsReceivePaymentsChecked = result.TransactionsReceivePayments__c;
                this.paymentServicesValue = result.PaymentServices__c;
                this.paymentServicesOtherTextValue = result.PaymentServicesOtherText__c;
                this.paymentsThroughAccountsValue = result.PaymentsThroughAccounts__c;
                this.paymentsHowProcessedValue = result.PaymentsHowProcessed__c;
                this.createChecksRemotelyValue = result.CreateChecksRemotely__c;
                this.businessTypeRestrictionsValue = result.BusinessTypeRestrictions__c;
                this.businessTypeRestrictionsTextValue = result.BusinessTypeRestrictionsText__c;

                this.atmOperator = result.ATM_Business__c;
                this.numberOfAtmValue = result.NumberOfAtm__c;
                this.replenishAtmCashValue = result.ReplenishAtmCash__c;
                this.sourceOfAtmCashValue = result.SourceOfAtmCash__c;
                this.atmMaxHoldingValue = result.AtmMaxHolding__c;
                this.atmDenominationValue = result.AtmDenomination__c;
                this.privateAtmTypeValue = result.PrivateAtmType__c;

                this.nbfi = result.Non_Bank_Financial_Institution__c;
                this.involveCasinosChecked = result.InvolveCasinos__c;
                this.involveSecuritiesChecked = result.InvolveSecurities__c;
                this.securitiesFinancialInstitutionValue = result.SecuritiesFinancialInstitution__c;
                this.securitiesHowBusinessRegisteredValue = result.SecuritiesHowBusinessRegistered__c;
                this.securitiesInvolveSecuritiesValue = result.SecuritiesInvolveSecurities__c;
                this.securitiesProductTypesValue = result.SecuritiesProductTypes__c;
                this.securitiesInvestFundsInternationallyValue = result.SecuritiesInvestFundsInternationally__c;
                this.securitiesCountriesTextValue = result.SecuritiesCountriesText__c;
                this.securitiesServiceTypesValue = result.SecuritiesServiceTypes__c;
                this.involveInsuranceChecked = result.InvolveInsurance__c;
                this.involveInsuranceStateRegInsValue = result.InvolveInsuranceStateRegIns__c;
                this.involveLoanFinanceChecked = result.InvolveLoanFinance__c;
                this.involveCreditCardsChecked = result.InvolveCreditCards__c;
                this.involvePreciousMetalsChecked = result.InvolvePreciousMetals__c;
                this.involvePreciousMetalsBuy50kValue = result.InvolvePreciousMetalsBuy50k__c;
                this.involvePreciousMetalsSell50kValue = result.InvolvePreciousMetalsSell50k__c;
                this.involvePawnBrokerageChecked = result.InvolvePawnBrokerage__c;
                this.involveTravelAgencyChecked = result.InvolveTravelAgency__c;
                this.involveTelegraphCompanyChecked = result.InvolveTelegraphCompany__c;
                this.involveVehicleSalesChecked = result.InvolveVehicleSales__c;
                this.involveVehicleTypesValue = result.InvolveVehicleTypes__c;
                this.involveRealEstateClosingChecked = result.InvolveRealEstateClosing__c;
                this.involvePostalServiceChecked = result.InvolvePostalService__c;
                this.involveGovAgencyChecked = result.InvolveGovAgency__c;
                this.docBsaAmlProgramValue = result.DocBsaAmlProgram__c;

                this.moneyServicesBusiness = result.Money_Service_Business__c;
                this.involveCurrencyExchangeChecked = result.InvolveCurrencyExchange__c;
                this.currencyExchangeAgentValue = result.Involve_Curency_Exchange_Agent__c;
                this.currencyExchangePrincipalValue = result.Involve_Curency_Exchange_Principal__c;
                this.involveCashChecksChecked = result.InvolveCashChecks__c;
                this.involveCheckTypesValue = result.InvolveCheckTypes__c;
                this.involveMoneyOrdersChecked = result.InvolveMoneyOrders__c;
                this.moneyOrdersAgentValue = result.Involve_Money_Orders_Agent__c;
                this.moneyOrdersPrincipalValue = result.Involve_Money_Orders_Principal__c;
                this.involveTransmitMoneyChecked = result.InvolveTransmitMoney__c;
                this.transmitMoneyAgentValue = result.Transmit_Money_Agent__c;
                this.transmitMoneyPrincipalValue = result.Transmit_Money_Principal__c;
                this.transmitMoneyNonUsLocationsValue = result.TransmitMoneyNonUsLocations__c;
                this.transmitForeignCountriesTextValue = result.TransmitForeignCountriesText__c;
                this.transmitMoneyTypesValue = result.TransmitMoneyTypes__c;
                this.transmitMoneyCvcValue = result.TransmitMoneyCvc__c;
                this.transmitMoneyActivitiesValue = result.TransmitMoneyActivities__c;
                this.involveVirtualCurrencyChecked = result.InvolveVirtualCurrency__c;
                this.involveGiftCardsChecked = result.InvolveGiftCards__c;
                this.giftCardAgentValue = result.Gift_Card_Agent__c;
                this.giftCardPrincipalValue = result.Gift_Card_Principal__c;
                this.giftCardExceedDailyMaxValue = result.GiftCardAccessDailyMax__c;
                this.giftCardActivationProcessValue = result.GiftCardActivationProcess__c;
                this.giftCardNetworkBrandedValue = result.GiftCardNetworkBranded__c;
                this.giftCardAccessDailyMaxValue = result.GiftCardAccessDailyMax__c;
                this.giftCardReloadedValue = result.GiftCardReloaded__c;
                this.giftCardTransferFundsValue = result.GiftCardTransferFunds__c;
                this.giftCardTransferFundsInternationallyValue = result.GiftCardTransferFundsInternationally__c;
                this.giftCardRequireCustomerIdValue = result.GiftCardRequireCustomerId__c;
                this.giftCardPreventSalesValue = result.GiftCardPreventSales__c;
                this.giftCardBsaAmlProgValue = result.GiftCardBsaAmlProg__c;
                this.registeredFinCenValue = result.RegisteredFinCen__c;
                this.involveNoneChecked = result.InvolveNone__c;

                this.transportVehicle = result.InvolveVehicleTypes__c;
                this.realEstateSettle = result.InvolveRealEstateClosing__c;
                this.USPortalService = result.InvolvePostalService__c;
                this.governmentAgencyDuty = result.InvolveGovAgency__c;
                this.documentedBSAprogram = result.DocBsaAmlProgram__c;

    

                this.businessSavingsDeposit = result.Business_Savings_5_00_minimum__c;
                this.businessCheckingDeposit = result.Business_Essential_Checking__c;
                this.totalDeposit = result.Total_Deposit__c;

                this.fundingType = result.How_do_you_want_to_fund_your_account__c;
                this.fundingAccountType = result.Account_Type_Checking_or_Savings__c;
                this.nameOnAccount = result.Name_on_Account__c;
                this.accountNumber = result.External_Account_Number__c;
                this.routingNumber = result.External_Routing_Number__c;
                this.financialInstitution = result.Financial_Institution__c;
                this.financialInstitutionState = result.Financial_Institution_State__c;

                this.businessDescription = result.Business_Description__c;
                this.naicsCode = result.NAICS_Code_Text__c;
                this.otheroccupancyStatus = result.Other_Occupancy_Status__c;

                this.previousStreet = result.Previous_Street__c;
                this.previousCity = result.Previous_City__c;
                this.previousState = result.Previous_State__c;
                this.previousZipcode = result.Previous_Zip__c ;

                this.checkedAck = result.isAcknowledged__c;

                this.useDifferentMailingAddressApplicant = result.Different_Mailing_Address__c;

                
                // console.log('Business Tax Id type  -- ', this.businessTaxIdType);
            })
            .catch((error) => {
                console.error("Error fetching application details:", error);
            });

        getApplicationRoles({ applicationId: this.applicationRecordId })
            .then((result) => {
                this.businessRoles = (result || []).map((item) => ({
                    id: item.Id,
                    name: item.Name,
                    applicant: item.isApplicant__c,
                    role: item.Individual_Role__c || "",
                    isControling: item.Individual_Role__c === "Controlling Individual" || item.Individual_Role__c === "Authorized Signer" ? true : false,
                    ownerPercent: item.Business_Owned__c != null ? item.Business_Owned__c + "%" : "",
                    hasPercentage: item.Business_Owned__c != null,
                    hasControl: item.Has_Control__c || "",
                    email: item.Email__c || "",
                    idAttached: item.Is_ID_Attached__c || false,
                    completed: item.IsCompleted__c || false,
                //    applicant: false,  Assuming applicant data isn't provided yet
                    isExpanded: true,
                    detailKey: item.Id + "-details",

                    firstName: item.First_Name__c || "",
                    middleName: item.Middle_Name__c || "",
                    lastName: item.Last_Name__c || "",
                    suffix: item.Suffix__c || "",
                    ssn: item.SSN_TIN__c || "",
                    dateOfBirth: item.Date_of_Birth__c || "",

                    citizenshipStatus: item.Citizenship_Status__c || "",
                    frequentTraveler: item.Frequent_Traveler__c || "",
                    outsideUnitedStates: item.Outside_United_States__c || "",
                    whichCountries: item.Which_Countries__c || "",

                    physicalStreet: item.Street_Address__c || "",
                    physicalCity: item.City__c || "",
                    physicalState: item.State__c || "",
                    physicalPostalCode: item.Zipcode__c || "",
                    physicalCountry: "US", // item.Physcial_Country__c || 
                    useDifferentMailingAddress: item.Different_Mailing_Address__c || false,
                    mailingStreet: item.Mailing_Street_Address__c || "",
                    mailingCity: item.Mailing_City__c || "",
                    mailingState: item.Mailing_State__c || "",
                    mailingPostalCode: item.Mailing_Zip_code__c || "",
                    mailingCountry: "US", // item.Mailing_Country__c || 

                    preferredContactMethod: item.Preferred_Contact_Method__c || "",
                    contactEmail: item.Email__c || "",
                    primaryPhone: item.Primary_Home_Phone__c || "",
                    primaryIsMobile: item.isPrimaryMobile__c || false,
                    workPhone: item.Work_Phone__c || "",
                    secondaryPhone: item.Secondary_Mobile_Phone__c || "",
                    secondaryIsMobile: item.isSecondaryMobile__c ||  false,

                    employmentStatus: item.Employment_Status__c || "",
                    profession: item.Profession_Job_Title__c || item.Profession_MOS__c || item.Former_Profession_Job_Title__c || "",
                    employer: item.Employer__c || "",
                    employmentDuration:
                        (item.Employment_Duration_Years__c ?? item.Time_in_Service_Years__c ?? item.UnEmployment_Duration_Years__c) +
                            " Years and " +
                            (item.Employment_Duration_Months__c ?? item.Time_in_Services_Months__c ?? item.UnEmployment_Duration_Month__c) +
                            " Months" || "",
                    grossMonthlyIncome: item.Gross_Monthly_Income__c || "",
                    payGrade: item.Pay_Grade__c  || "",

                    idType: item.ID_Type__c || "",
                    idState: item.Id_State__c || item.ID_Country__c || "",
                    idNumber: item.ID_Number__c || "",
                    idExpirationDate: item.ID_Expiration_Date__c || "",
                    idDateIssued: item.ID_Issued_Date__c || ""
                }));
            })
            .catch((error) => {
                console.error("Error fetching application roles:", error);
            });
    }

    convertBooleanToYesNo(value) {
        return value ? "Yes" : "No";
    }

    convertYesNoToBoolean(value) {
        return value === "Yes" ? true : false;
    }

    get fundingSourcesWithChecked() {
        return (this.fundingSourcesOptions || []).map((opt) => {
            return {
                ...opt,
                checked: (this.fundingSources || []).includes(opt.value)
            };
        });
    }

    get fundingMethodsWithChecked() {
        return (this.fundingMethodsOptions || []).map((opt) => {
            return {
                ...opt,
                checked: (this.fundingMethods || []).includes(opt.value)
            };
        });
    }

    toggleRow(event) {
        const rowId = event.currentTarget.dataset.id;
        this.businessRoles = this.businessRoles.map((role) => {
            return {
                ...role,
                isExpanded: role.id === rowId ? !role.isExpanded : role.isExpanded
            };
        });
    }

    get isPublicCompanyValue() {
        return this.isPublicCompany === "Yes";
    }
    get tradedOnStockExchangeValue() {
        return this.tradedOnStockExchange === "Yes";
    }
    get ownedByListedEntityValue() {
        return this.ownedByListedEntity === "Yes";
    }

    get isHeadquarteredInUSValue() {
        return this.isHeadquarteredInUS === "Yes";
    }

    get qualifiesForNonProfitStatusValue() {
        return this.qualifiesForNonProfitStatus === "Yes";
    }

    get fundingSourcesWithCheckedOtherChecked() {
        return this.fundingSourcesWithChecked.find((option) => option.value === "Other" && option.checked) !== undefined;
    }

    get fundingMethodsWithCheckedOtherChecked() {
        return this.fundingMethodsWithChecked.find((option) => option.value === "Other" && option.checked) !== undefined;
    }

    get nonUSDonorsOrVolunteersValue() {
        return this.nonUSDonorsOrVolunteers === "Yes";
    }

    get charitableServicesAbroadValue() {
        return this.charitableServicesAbroad === "Yes";
    }

    get businessStructureCorpOrLLC() {
        return this.businessStructure === "Corporation" || this.businessStructure === "Limited Liability Company";
    }

    get businessStructureSoleProprietorship() {
        return this.businessStructure === "Sole Proprietorship";
    }

    get businessStructureUnincorpOrCorpOrLLC() {
        return this.businessStructure === "Unincorporated" || this.businessStructure === "Corporation" || this.businessStructure === "Limited Liability Company";
    }

    get otherDueDiligenceValue() {
        return this.otherDueDiligence;
    }

    get privateLabelCardAccountValue() {
        return this.privateLabelCardAccount;
    }

    get postageRemittanceValue() {
        return this.postageRemittance;
    }

    get postagePurchaseUsageValue() {
        return this.postagePurchaseUsage === "Yes";
    }

    get equipmentPurchaseOrLeaseValue() {
        return this.equipmentPurchaseOrLease;
    }

    get equipmentFinanceUsageValue() {
        return this.equipmentFinanceUsage === "Yes";
    }

    get pooledInvestmentVehicleValue() {
        return this.pooledInvestmentVehicle;
    }

    get cashDepositsInPersonValue() {
        return this.cashDepositsInPerson;
    }

    get atmCashDepositsValue() {
        return this.atmCashDeposits;
    }

    get cashWithdrawalsInPersonValue() {
        return this.cashWithdrawalsInPerson;
    }

    get atmCashWithdrawalsValue() {
        return this.atmCashWithdrawals;
    }

    get checkDepositsValue() {
        return this.checkDeposits;
    }

    get checkWithdrawalsValue() {
        return this.checkWithdrawals;
    }

    get cashiersChecksValue() {
        return this.cashiersChecks;
    }

    get incomingWireTransfersValue() {
        return this.incomingWireTransfers;
    }

    get outgoingWireTransfersValue() {
        return this.outgoingWireTransfers;
    }

    get incomingNonWireTransfersValue() {
        return this.incomingNonWireTransfers;
    }

    get outgoingNonWireTransfersValue() {
        return this.outgoingNonWireTransfers;
    }

    get internetGamblingIncomeValue() {
        return this.internetGamblingIncome === "Yes";
    }

    get illegalBusinessValue() {
        return this.illegalBusiness === "Yes";
    }

    get marijuanaRelatedBusinessValue() {
        return this.marijuanaRelatedBusiness === "Yes";
    }

    get professionalServiceProviderValue() {
        return this.professionalServiceProvider === "Yes";
    }

    get courierServicesValue() {
        return this.courierServices === "Yes";
    }

    get paymentProcessorValue() {
        return this.paymentProcessor === "Yes";
    }

    get atmOperatorValue() {
        return this.atmOperator === "Yes";
    }

    get nbfiValue() {
        return this.nbfi === "Yes";
    }

    get msbValue() {
        return this.msb === "Yes";
    }

    get fundingTypeCreditCard() {
        return this.fundingType === "Credit Card (max $510.00)";
    }

    get fundingTypeAccounts() {
        return (
            this.fundingType === "Transfer from Another Financial Institution (External ACH - max $1,000.00)" ||
            this.fundingType === "Internal Transfer (from another RCU account that belongs to the applicant)"
        );
    }

    get fundingTypeTransferAnotherAccount() {
        return this.fundingType === "Transfer from Another Financial Institution (External ACH - max $1,000.00)";
    }

    get fundingTypeInternalTransfer() {
        return this.fundingType === "Internal Transfer (from another RCU account that belongs to the applicant)";
    }

    get OccupancyYears() {
        return this.occupancyDurationYears == 1 || this.occupancyDurationYears == 0;
    }

    getQASections() {
        let items = [];
        return [
            {
                section: "Product Selection",
                
                items: [
                    { question: "Business Essential Checking", answer: this.businessEssentialChecking },
                    { question: "Business Choice Checking", answer: this.businessChoiceChecking },
                    { question: "Business Savings", answer: this.businessSavings }
                ]
            },
            {
                section: "Account Options",
                items: [
                    { question: "Visa® Debit Card", answer: this.visaDebitCard },
                    { question: "eStatements", answer: this.eStatements },
                    { question: "Digital Banking", answer: this.onlineBanking },
                    { question: "Interested in a Business Visa® Credit Card", answer: this.businessVisaCreditCard }
                ]
            },
            {
                section: "Overdraft Pay Advantage",
                items: [{ question: "Overdraft Pay Advantage Selection for your Checking Account:", answer: this.overdraftSelectionPdf }]
            },
            {
                section: "Business Information",
                items: [
                    { question: "Business Name", answer: this.businessName },
                    { question: "Fictitious Business Name(FBN)/Doing Business As(DBA)", answer: this.fbaOrDba },
                    { question: "Business Tax ID Type", answer: this.businessTaxIdType },
                    { question: "Business Tax ID", answer: this.businessTaxId },
                    { question: "Principal Location", answer: this.principalLocation },
                    { question: "Preferred Branch", answer: this.preferredBranch },
                    { question: "Business Structure", answer: this.businessStructure }
                ]
            },
            {
                section: "Structure & Non-Profit",
                items: [
                    { question: "Is this a publicly traded company?", answer: this.isPublicCompany },
                    { question: "Traded on the New York, American, or NASDAQ stock exchange?", answer: this.tradedOnStockExchange },
                    { question: "Is the company at least 51% owned by an entity listed on the New York, American, or NASDAQ stock exchange?", answer: this.ownedByListedEntity },
                    { question: "Is your business headquartered in the United States?", answer: this.isHeadquarteredInUS },
                    { question: "In what country is your business headquartered?", answer: this.headquarteredCountry },
                    { question: "Are you registered to do business in California?", answer: this.registeredInCalifornia },
                    {
                        question: "Does your organization qualify for non-profit status and depend on charitable donations or voluntary service?",
                        answer: this.qualifiesForNonProfitStatus
                    },
                    {
                        question: "What are the objectives, programs, activities, and services that your organization provides related to its charitable function?",
                        answer: this.charitableObjectives
                    },
                    { question: "In what country is your organization chartered?", answer: this.organizationCharteredCountry },
                    { question: "Is your organization registered as a non-profit with the IRS, for example, as a 501(c) organization?", answer: this.registeredAs501c }
                ]
            },
            {
                section: "Funding",
                items: [
                    // Funding sources
                    ...this.fundingSourcesWithChecked.map((opt) => ({
                        question: opt.label,
                        answer: opt.checked
                    })),
                    { question: "Other Source Description", answer: this.otherSourceDescription }
                ]
            },
            {
                section: "Funding Methods",
                items: [
                    ...this.fundingMethodsWithChecked.map((opt) => ({
                        question: opt.label,
                        answer: opt.checked
                    })),
                    { question: "Other Funding Description", answer: this.otherFundingDescription },
                    { question: "What is the value of your organization's annual funding or gross receipts for the most recent year?", answer: this.annualFundingValue },
                ]
            },
            {
                section: "Financial & Organization",
                items: [
                    { question: "Business Description", answer: this.businessDescription },
                    { question: "NAICS Code", answer: this.naicsCode },
                    { question: "Establish Date", answer: this.establishDate },
                    { question: "Annual Revenue", answer: this.annualRevenue },
                    { question: "State Registered", answer: this.stateRegistered },
                    { question: "Number of Employees", answer: this.numberOfEmployees }
                ]
            },
            {
                section: "Addresses",
                items: [
                    { question: "Physical Street", answer: this.physicalStreet },
                    { question: "Physical City", answer: this.physicalCity },
                    { question: "Physical State", answer: this.physicalState },
                    { question: "Physical Postal Code", answer: this.physicalPostalCode },
                    { question: "Physical Country", answer: this.physicalCountry },
                    { question: "Use different address for mailing", answer: this.useDifferentMailingAddressApplicant },
                    { question: "Mailing Street", answer: this.mailingStreet },
                    { question: "Mailing City", answer: this.mailingCity },
                    { question: "Mailing State", answer: this.mailingState },
                    { question: "Mailing Postal Code", answer: this.mailingPostalCode },
                    { question: "Mailing Country", answer: this.mailingCountry }
                ]
            },
            {
                section: "Occupancy & Contact",
                   
                        items: [                    
                        { question: "Occupancy Status", answer: this.occupancyStatus },
                        { question: "Other Description", answer: this.otheroccupancyStatus },
                        { question: "Occupancy Duration - Years", answer: this.occupancyDurationYears },
                        { question: "Occupancy Duration - Months", answer: this.occupancyDurationMonths },
                        ...(this.occupancyDurationYears == 1 || this.occupancyDurationYears == 0
                            ? [
                                { question: "Previous Street", answer: this.previousStreet },
                                { question: "Previous City", answer: this.previousCity },
                                { question: "Previous State", answer: this.previousState },
                                { question: "Previous Zipcode", answer: this.previousZipcode }
                            ]
                            : []
                        ),
                        { question: "Business Email", answer: this.businessEmail },
                        { question: "Business Cell Phone", answer: this.businessCellPhone },
                        { question: "Business Phone", answer: this.businessPhone },
                        { question: "Business Fax", answer: this.businessFax }
                    ]
                
                
            },
            {
                section: "Due Diligence Activities",
                items: [
                    { question: "General Operation Funds", answer: this.generalOperationFunds },
                    { question: "Payroll", answer: this.payroll },
                    { question: "Savings", answer: this.savings },
                    { question: "Credit Card Processing", answer: this.creditCardProcessing },
                    { question: "IOLTA/IOLA", answer: this.ioltaIola },
                    { question: "Lottery", answer: this.lottery },
                    { question: "MSB Activity", answer: this.msbActivity },
                    { question: "Private Banking", answer: this.privateBanking },
                    { question: "Other", answer: this.otherDueDiligence },
                    { question: "Other Notes", answer: this.otherDueDiligenceNotes },
                    { question: "Private-label Credit Card Account", answer: this.privateLabelCardAccount },
                    { question: "Retail Account Details", answer: this.retailAccountDetails },
                    { question: "Postage Remittance", answer: this.postageRemittance },
                    { question: "Postage Purchase Usage", answer: this.postagePurchaseUsage },
                    { question: "Third-Party Payments", answer: this.thirdPartyPayments },
                    { question: "Is there a possibility of a cash refund on the account activity?", answer: this.cashRefundPossible },
                    { question: "Equipment Purchase or Lease", answer: this.equipmentPurchaseOrLease },
                    { question: "Equipment Finance Usage", answer: this.equipmentFinanceUsage },
                    { question: "Equipment Third-Party Payments", answer: this.equipmentThirdPartyPayments },
                    { question: "Equipment Cash Refund Possible", answer: this.equipmentCashRefundPossible },
                    { question: "Pooled Investment Vehicle", answer: this.pooledInvestmentVehicle },
                    { question: "Is the pooled investment vehicle operated or advised by an excluded financial institution?", answer: this.pooledInvestmentExcludedAdvisor },
                    { question: "Account(s) also be used for your personal banking activity?", answer: this.pooledPersonalUse },
                    { question: "Major customers or Vendors", answer: this.pooledMajorCustomerVendor }
                ]
            },
            {
                section: "Due Diligence Transactions",
                items: [
                    { question: "Cash Deposits in Person", answer: this.cashDepositsInPerson },
                    { question: "Average Monthly Amount: (Cash Deposits in Person)", answer: this.cashDepositsInPersonAmount },
                    { question: "ATM Cash Deposits", answer: this.atmCashDeposits },
                    { question: "Average Monthly Amount: (ATM Cash Deposits)", answer: this.atmCashDepositsAmount },
                    { question: "Cash Withdrawals in Person", answer: this.cashWithdrawalsInPerson },
                    { question: "Average Monthly Amount: (Cash Withdrawals in Person)", answer: this.cashWithdrawalsInPersonAmount },
                    { question: "ATM Cash Withdrawals", answer: this.atmCashWithdrawals },
                    { question: "Average Monthly Amount: (ATM Cash Withdrawals)", answer: this.atmCashWithdrawalsAmount },
                    { question: "Check Deposits", answer: this.checkDeposits },
                    { question: "Average Monthly Amount: (Check Deposits)", answer: this.checkDepositsAmount },
                    { question: "Remote Deposit Capture", answer: this.remoteDepositCapture },
                    { question: "Check Withdrawals", answer: this.checkWithdrawals },
                    { question: "Average Monthly Amount: (Check Withdrawals)", answer: this.checkWithdrawalsAmount },
                    { question: "Official Checks/Cashier's Checks", answer: this.cashiersChecks },
                    { question: "Average Monthly Amount: (Official Checks/Cashier's Checks)", answer: this.cashiersChecksAmount },
                    { question: "Incoming Wire Transfers", answer: this.incomingWireTransfers },
                    { question: "Average Monthly Amount: (Incoming Wire Transfers)", answer: this.incomingWireTransferAmount },
                    { question: "Will you receive transfers from non-US locations?", answer: this.receiveIntlTransfers },
                    { question: "Which countries (receive)?", answer: this.receiveIntlTransferCountries },
                    { question: "Outgoing Wire Transfers", answer: this.outgoingWireTransfers },
                    { question: "Average Monthly Amount: (Outgoing Wire Transfers)", answer: this.outgoingWireTransferAmount },
                    { question: "Will you send transfers to non-US locations?", answer: this.sendIntlTransfers },
                    { question: "Which countries (send)?", answer: this.sendIntlTransferCountries },
                    { question: "Incoming (Non-Wire) Electronic Transfers", answer: this.incomingNonWireTransfers },
                    { question: "Average Monthly Amount: (Incoming Non-Wire)", answer: this.incomingNonWireTransferAmount },
                    { question: "Will you receive non-wire transfers from non-US locations?", answer: this.receiveIntlNonWireTransfers },
                    { question: "Which countries (receive non-wire)?", answer: this.receiveIntlNonWireCountries },
                    { question: "Outgoing (Non-Wire) Electronic Transfers", answer: this.outgoingNonWireTransfers },
                    { question: "Average Monthly Amount: (Outgoing Non-Wire)", answer: this.outgoingNonWireTransferAmount },
                    { question: "Will you send non-wire transfers to non-US locations?", answer: this.sendIntlNonWireTransfers },
                    { question: "Which countries (send non-wire)?", answer: this.sendIntlNonWireCountries }
                ]
            },
            {
                section: "Due Diligence Questions",
                items: [
                    { question: "Does your business engage in any illegal activities under federal or state law?", answer: this.illegalBusiness },
                    { question: "Does a portion of your income come from Internet Gambling?", answer: this.internetGamblingIncome },
                    { question: "Is this a marijuana related business?", answer: this.marijuanaRelatedBusiness },
                    { question: "Are you licensed by the state?", answer: this.marijuanaLicensedValue },
                    { question: "What percentage of your revenue is derived from marijuana-related activity?", answer: this.marijuanaRevenuePercentageValue },
                    { question: "What type of marijuana-related activity does your business engage in?", answer: this.marijuanaActivityTypeValue },
                    { question: "Do you act as a Professional Service Provider?", answer: this.professionalServiceProvider },
                    { question: "What is the primary type of Professional Service Provider that best matches your business type?", answer: this.professionalTypeValue },
                    { question: "Description (if Other selected)", answer: this.professionalTypeOtherTextValue },
                    { question: "What services do you provide?", answer: this.servicesTypesValue },
                    { question: "Will other professionals be using this account?", answer: this.professionalOthersUsingValue },
                    { question: "Does your company offer courier or armored car services?", answer: this.courierServices },
                    { question: "Will you be processing transactions that benefit a third party as a payment processor?", answer: this.paymentProcessor },
                    { question: "Do you send payments on behalf of your clients?", answer: this.transactionsSendPaymentsChecked },
                    { question: "Do you receive payments on behalf of your clients?", answer: this.transactionsReceivePaymentsChecked },
                    { question: "What types of payment services do you offer?", answer: this.paymentServicesValue },
                    { question: "Description of payment services", answer: this.paymentServicesValue },
                    { question: "Will client payments run through your accounts with us?", answer: this.paymentsThroughAccountsValue },
                    { question: "How are client transactions processed through your account?", answer: this.paymentsHowProcessedValue },
                    { question: "Do you create checks remotely on behalf of your clients?", answer: this.createChecksRemotelyValue },
                    { question: "Do you have restrictions on the types of businesses you accept?", answer: this.businessTypeRestrictionsValue },
                    { question: "What are your restrictions?", answer: this.businessTypeRestrictionsTextValue },
                    { question: "Do you own, operate, or replenish an ATM?", answer: this.atmOperator },
                    { question: "How many ATMs do you own, operate, or replenish?", answer: this.numberOfAtmValue },
                    { question: "Do you have access to replenish the cash within the ATM?", answer: this.replenishAtmCashValue },
                    { question: "What is the source of cash?", answer: this.sourceOfAtmCashValue },
                    { question: "What is the maximum amount any ATM can hold?", answer: this.atmMaxHoldingValue },
                    { question: "What denominations are dispensed?", answer: this.atmDenominationValue },
                    { question: "What is the type of your private ATM?", answer: this.privateAtmTypeValue },
                    { question: "Could your business be considered a Non-Bank Financial Institution?", answer: this.nbfi },
                    { question: "Does your business involve casinos, card clubs or gaming establishments?", answer: this.involveCasinosChecked },
                    { question: "Does your business involve securities, futures commissions or commodity trading?", answer: this.involveSecuritiesChecked },
                    { question: "Is the Nonbank financial institution one of the following?", answer: this.securitiesFinancialInstitutionValue },
                    { question: "How is your business registered?", answer: this.securitiesHowBusinessRegisteredValue },
                    { question: "Do you invest your clients' funds?", answer: this.securitiesInvolveSecuritiesValue },
                    { question: "In what types of products do you invest?", answer: this.securitiesProductTypesValue },
                    { question: "Do you invest your clients' funds internationally?", answer: this.securitiesInvestFundsInternationallyValue },
                    { question: "In what countries do you invest internationally?", answer: this.securitiesCountriesTextValue },
                    { question: "What types of services apply to your business?", answer: this.securitiesServiceTypesValue },
                    { question: "Insurance", answer: this.involveInsuranceChecked },
                    { question: "Is this a State-regulated insurance company?", answer: this.involveInsuranceStateRegInsValue },
                    { question: "Loan/Finance", answer: this.involveLoanFinanceChecked },
                    { question: "Credit cards system operation", answer: this.involveCreditCardsChecked },
                    { question: "Precious metals, stones, or jewels?", answer: this.involvePreciousMetalsChecked },
                    { question: "Did you buy > $50k of precious metals?", answer: this.involvePreciousMetalsBuy50kValue },
                    { question: "Did you sell > $50k of precious metals?", answer: this.involvePreciousMetalsSell50kValue },
                    { question: "Pawn brokerage", answer: this.involvePawnBrokerageChecked },
                    { question: "Travel agency", answer: this.involveTravelAgencyChecked },
                    { question: "Telegraph company", answer: this.involveTelegraphCompanyChecked },
                    { question: "Vehicle sales", answer: this.involveVehicleSalesChecked },
                    { question: "What types of transportation vehicles do you sell?", answer: this.transportVehicle },
                    { question: "Real estate closing and settlement", answer: this.realEstateSettle },
                    { question: "U.S. Postal Service", answer: this.USPortalService },
                    { question: "Federal, state or local government agency carrying out a duty", answer: this.governmentAgencyDuty },
                    { question: "Do you have a documented BSA/AML program?", answer: this.documentedBSAprogram },
                    { question: "Could your business be considered a Money Service Business?", answer: this.moneyServicesBusiness },
                    { question: "Foreign currency exchange > $1,000 for person/day", answer: this.involveCurrencyExchangeChecked },
                    { question: "Do you act as an Agent?", answer: this.currencyExchangeAgentValue },
                    { question: "Do you act as a Principal?", answer: this.currencyExchangePrincipalValue },
                    { question: "Cash checks > than $1,000 for person/day", answer: this.involveCashChecksChecked },
                    { question: "What type of checks does your business cash?", answer: this.involveCheckTypesValue },
                    { question: "Issue or sell money orders > $1000 to person/day", answer: this.involveMoneyOrdersChecked },
                    { question: "Do you act as an Agent?", answer: this.moneyOrdersAgentValue },
                    { question: "Do you act as a Principal? ", answer: this.moneyOrdersPrincipalValue },
                    { question: "Transmit money on your customer's behalf", answer: this.involveTransmitMoneyChecked },
                    { question: "Do you act as an Agent?", answer: this.transmitMoneyAgentValue },
                    { question: "Do you act as a Principal?", answer: this.transmitMoneyPrincipalValue },
                    { question: "Will you transmit to non-US locations?", answer: this.transmitMoneyNonUsLocationsValue },
                    { question: "To which foreign countries will transactions be sent?", answer: this.transmitForeignCountriesTextValue },
                    { question: "What type of money will be transmitted?", answer: this.transmitMoneyTypesValue },
                    { question: "Does your business involve convertible virtual currency (CVC)?", answer: this.transmitMoneyCvcValue },
                    { question: "Which CVC-related activities does your business include?", answer: this.transmitMoneyActivitiesValue },
                    { question: "Administer or exchange virtual currency", answer: this.involveVirtualCurrencyChecked },
                    { question: "Provide or sell prepaid access to funds,gift cards", answer: this.involveGiftCardsChecked },
                    { question: "Do you act as an Agent?", answer: this.giftCardAgentValue },
                    { question: "Do you act as a Principal?", answer: this.giftCardPrincipalValue },
                    { question: "Sell non-network-branded prepaid access funds > $2,000", answer: this.sellNonNetwork },
                    { question: "Funds require activation process by customer identification?", answer: this.prepaidFundsActivation },
                    { question: "Sell network-branded funds (use Mastercard or Visa networks)", answer: this.MastercardSell },
                    { question: "devices permit access funds > $1000 device/day", answer: this.giftCardAccessDailyMaxValue },
                    { question: "Can devices be reloaded from non-depository sources?", answer: this.giftCardReloadedValue },
                    { question: "Can devices used to transfer funds from one person to another?", answer: this.giftCardTransferFundsValue },
                    { question: "Can devices be used to transmit funds internationally?", answer: this.giftCardTransferFundsInternationallyValue },
                    { question: "Funds require activation process by customer identification?", answer: this.giftCardRequireCustomerIdValue },
                    { question: "Prepaid Policies and procedures prevent the sale > $10,000", answer: this.giftCardPreventSalesValue },
                    { question: "Do you have a documented BSA/AML program?", answer: this.giftCardBsaAmlProgValue },
                    { question: "Are you registered with FinCEN", answer: this.registeredFinCenValue },
                    { question: "None of the above", answer: this.involveNoneChecked }
                ]
            },
            {
                section: "Business Roles",
                items: [{}]
            },
            ...this.businessRoles.map((role) => ({
                section: role.name,
                items: [
                    { question: "Role", answer: role.role },
                    { question: "Beneficial Owner %", answer: role.ownerPercent },
                    { question: "Email", answer: role.email },
                    { question: "Has Control", answer: role.hasControl },
                    { question: "ID Attached?", answer: role.idAttached },
                    { question: "Completed?", answer: role.completed },
                    { question: "Applicant?", answer: role.applicant },
                    { question: "First Name", answer: role.firstName },
                    { question: "Middle Name", answer: role.middleName },
                    { question: "Last Name", answer: role.lastName },
                    { question: "Suffix", answer: role.suffix },
                    { question: "SSN", answer: role.ssn },
                    { question: "Date of Birth", answer: role.dateOfBirth },
                    { question: "Citizenship Status", answer: role.citizenshipStatus },
                    { question: "Frequent Traveler", answer: role.frequentTraveler },
                    { question: "Outside United States", answer: role.outsideUnitedStates },
                    { question: "Which Countries", answer: role.whichCountries },
                    { question: "Physical Street", answer: role.physicalStreet },
                    { question: "Physical City", answer: role.physicalCity },
                    { question: "Physical State", answer: role.physicalState },
                    { question: "Physical Postal Code", answer: role.physicalPostalCode },
                    { question: "Physical Country", answer: role.physicalCountry },
                    { question: "Use Different Mailing Address", answer: role.useDifferentMailingAddress },
                    { question: "Mailing Street", answer: role.mailingStreet },
                    { question: "Mailing City", answer: role.mailingCity },
                    { question: "Mailing State", answer: role.mailingState },
                    { question: "Mailing Postal Code", answer: role.mailingPostalCode },
                    { question: "Mailing Country", answer: role.mailingCountry },
                    { question: "Preferred Contact Method", answer: role.preferredContactMethod },
                    { question: "Contact Email", answer: role.contactEmail },
                    { question: "Primary Phone", answer: role.primaryPhone },
                    { question: "Primary Is Mobile", answer: role.primaryIsMobile },
                    { question: "Work Phone", answer: role.workPhone },
                    { question: "Secondary Phone", answer: role.secondaryPhone },
                    { question: "Secondary Is Mobile", answer: role.secondaryIsMobile },
                    { question: "Employment Status", answer: role.employmentStatus },
                    { question: "Profession", answer: role.profession },
                    { question: "Employer", answer: role.employer },
                    { question: "Employment Duration", answer: role.employmentDuration },
                    { question: "Gross Monthly Income", answer: role.grossMonthlyIncome },
                    { question: "Pay Grade", answer: role.payGrade },
                    { question: "ID Type", answer: role.idType },
                    { question: "ID State", answer: role.idState },
                    { question: "ID Number", answer: role.idNumber },
                    { question: "ID Expiration Date", answer: role.idExpirationDate },
                    { question: "ID Date Issued", answer: role.idDateIssued }
                ]
            })),
            {
                section: "Funding Source",
                items: [
                    { question: "Business Savings ($5.00 minimum)", answer: this.businessSavingsDeposit },
                    { question: "Business Essential Checking ($100.00 minimum)", answer: this.businessCheckingDeposit },
                    { question: "Total Deposit", answer: this.totalDeposit },
                    { question: "Funding Type", answer: this.fundingType },
                    { question: "Account Type", answer: this.fundingAccountType },
                    { question: "Name On Account", answer: this.nameOnAccount },
                    { question: "Account Number", answer: this.accountNumber },
                    { question: "Routing Number", answer: this.routingNumber },
                    { question: "Financial Institution", answer: this.financialInstitution },
                    { question: "Financial Institution State", answer: this.financialInstitutionState }                
                ]
            },{
                section: "Acknowledge",
                items: [
                    { question: "By checking this box and clicking the 'I Agree' button below, I affirm that I am an authorized officer of the company, with sufficient authority to bind the business. I authorize RCU to verify the provided information, obtain a credit report to determine membership eligibility and manage any loans the company may have or establish with RCU. Upon request, we will disclose if a credit report was obtained and provide the reporting agency's details.", answer: acknowledged? 'Yes' : 'No' },
                                  
                ]
            },
        ];
    }

    /**
     * Build and download a styled PDF of all questions & answers by section.
     */
    generatePdf(specificSectionName = "Business Roles") {
    // First check if Checkbox5 is checked
    let checkbox5Value = false;
    
    // Get the latest Checkbox5 value from OmniScript data
    if (this.omniJsonData?.Required?.Checkbox5 !== undefined) {
        checkbox5Value = this.omniJsonData.Required.Checkbox5;
        this.acknowledged = checkbox5Value;
    } else if (this.omniJsonData?.Checkbox5 !== undefined) {
        checkbox5Value = this.omniJsonData.Checkbox5;
        this.acknowledged = checkbox5Value;
    } else {
        // Search through the entire data structure for Checkbox5
        const searchForCheckbox5 = (obj) => {
            if (obj && typeof obj === 'object') {
                for (const [key, value] of Object.entries(obj)) {
                    if (key === 'Checkbox5') {
                        return value;
                    }
                    if (typeof value === 'object' && value !== null) {
                        const result = searchForCheckbox5(value);
                        if (result !== undefined) return result;
                    }
                }
            }
            return undefined;
        };
        
        const foundValue = searchForCheckbox5(this.omniJsonData);
        if (foundValue !== undefined) {
            checkbox5Value = foundValue;
            this.acknowledged = checkbox5Value;
        }
    }
    
    
    // If checkbox5 is not checked, show error message and return
    if (!checkbox5Value) {
        alert('Please complete the Ackknowledgment before downloading the PDF.');
        return;
    }

        // Reference your sectioned QA data
        const sections = this.getQASections();

        // Find the index of our specific section that will trigger page breaks
        const specificSectionIndex = sections.findIndex((section) => section.section === specificSectionName);

        // Build the pdfMake content array
        const content = [{ text: "Application Summary", style: "title", margin: [0, 0, 0, 20] }];

        sections.forEach((section, index) => {
            // For specific section, add page break before
            if (index === specificSectionIndex) {
                content.push({ text: "", pageBreak: "before" });
            }
            // For sections that are 2+ after the specific section, add page break before each one
            else if (specificSectionIndex !== -1 && index > specificSectionIndex + 1) {
                content.push({ text: "", pageBreak: "before" });
            }

            // Section header
            content.push({ text: section.section, style: "sectionHeader", margin: [0, 10, 0, 5] });

            // Build a two-column table for this section
            const tableBody = section.items.map((item) => {
                const answerText = item.answer === true ? "Yes" : item.answer === false ? "No" : (item.answer ?? "");
                return [
                    { text: item.question, style: "questionCell" },
                    { text: String(answerText), style: "answerCell" }
                ];
            });

            // Add table, no borders, 50/50 split
            content.push({
                table: {
                    widths: ["50%", "50%"],
                    body: tableBody
                },
                layout: "noBorders"
            });
        });

        /* content.unshift({
            image: 'data:image/png;base64,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',
            width: 48
        }); */

        // Define styles and page settings
        const docDefinition = {
            pageSize: "A4",
            pageMargins: [40, 60, 40, 60],
            content,
            styles: {
                title: {
                    fontSize: 18,
                    bold: true,
                    alignment: "center"
                },
                sectionHeader: {
                    fontSize: 14,
                    bold: true
                },
                questionCell: {
                    fontSize: 10,
                    bold: false
                },
                answerCell: {
                    fontSize: 10
                }
            }
        };

        // Generate and download
        window.pdfMake.createPdf(docDefinition).download("ApplicationSummary.pdf");
    }
}