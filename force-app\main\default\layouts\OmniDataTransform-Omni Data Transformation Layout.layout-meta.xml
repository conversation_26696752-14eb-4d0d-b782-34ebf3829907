<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Omni Data Transformation</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BatchSize</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsFieldLevelSecurityEnabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsDeletedOnSuccess</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsErrorIgnored</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InputParsingClass</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpectedInputJson</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InputType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpectedInputXml</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpectedInputOtherData</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SourceObject</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsSourceObjectDefault</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsProcessSuperBulk</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OutputParsingClass</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OutputType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsNullInputsIncludedInOutput</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PreprocessorClassName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SynchronousProcessThreshold</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RequiredPermission</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsRollbackOnError</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ResponseCacheType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PreviewJsonData</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PreviewSourceObjectData</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PreviewXmlData</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PreviewOtherData</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TargetOutputDocumentIdentifier</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpectedOutputJson</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TargetOutputFileName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpectedOutputXml</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpectedOutputOtherData</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ResponseCacheTtlMinutes</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsAssignmentRulesUsed</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>XmlOutputTagsOrder</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsXmlDeclarationRemoved</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>GlobalKey</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>UniqueName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Namespace</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>VersionNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsActive</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OverrideKey</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
