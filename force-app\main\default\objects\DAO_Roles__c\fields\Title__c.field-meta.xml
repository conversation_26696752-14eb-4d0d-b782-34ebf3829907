<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Title__c</fullName>
    <label>Title</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Agent</fullName>
                <default>false</default>
                <label>Agent</label>
            </value>
            <value>
                <fullName>Authorized Signer</fullName>
                <default>false</default>
                <label>Authorized Signer</label>
            </value>
            <value>
                <fullName>Director</fullName>
                <default>false</default>
                <label>Director</label>
            </value>
            <value>
                <fullName>Corporate Representative</fullName>
                <default>false</default>
                <label>Corporate Representative</label>
            </value>
            <value>
                <fullName>Manager</fullName>
                <default>false</default>
                <label>Manager</label>
            </value>
            <value>
                <fullName>Member</fullName>
                <default>false</default>
                <label>Member</label>
            </value>
            <value>
                <fullName>Owner</fullName>
                <default>false</default>
                <label>Owner</label>
            </value>
            <value>
                <fullName>Partner</fullName>
                <default>false</default>
                <label>Partner</label>
            </value>
            <value>
                <fullName>President</fullName>
                <default>false</default>
                <label>President</label>
            </value>
            <value>
                <fullName>Secretary</fullName>
                <default>false</default>
                <label>Secretary</label>
            </value>
            <value>
                <fullName>Treasurer</fullName>
                <default>false</default>
                <label>Treasurer</label>
            </value>
            <value>
                <fullName>Vice President</fullName>
                <default>false</default>
                <label>Vice President</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
