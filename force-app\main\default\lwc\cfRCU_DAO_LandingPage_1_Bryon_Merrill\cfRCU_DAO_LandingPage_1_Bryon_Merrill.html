<template>
            <div class="slds-grid slds-wrap slds-is-relative ">
            <div style="position: fixed;z-index: 999999;top: 0;right: 0;">
  <omnistudio-toast class="flexcard-toast-element" theme="slds" title="" message="" styletype=""> </omnistudio-toast>
</div>
            <div if:true={showLoader} class="slds-is-absolute vloc-loader_override" style="height: 100%; width: 100%; min-height:50px; background: transparent; z-index: 99;">
  <div>
   <omnistudio-spinner
      variant="brand"
      alternative-text="Loading content..."
      size="medium"
      theme="slds"
      ></omnistudio-spinner>
  </div>
</div>
            <template if:false={hasPermission}>
              You don't have required permissions to view this card.
            </template>
            <template if:true={hasPermission}>
              <template if:true={hasRecords}>
                        <template for:each={_records} for:item="record" for:index="rindex">
                            <omnistudio-flex-card-state if:true={record}  key={record._flex.uniqueKey} data-recordid={record.Id} record={record} data-statue="true"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12"  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-bottom_x-small" style="">
          <div data-style-id="state0element0" class="slds-col   slds-large-size_12-of-12  slds-medium-size_1-of-12  slds-small-size_1-of-12  slds-size_1-of-12  " data-rindex={rindex} style="      
     height:70px;    " ><omnistudio-flex-img data-style-id="state0element0_child"   card={card}  record={record}  size=""  extraclass="slds-align_absolute-center"  state-img='\{"imgsrc":"/sfc/servlet.shepherd/version/download/068U8000001pETNIA2","title":"","alternativeText":"RCU Logo","document":{"label":"RCUlogo (Version:1)","value":"/sfc/servlet.shepherd/version/download/068U8000001pETNIA2","title":"RCUlogo","Id":"068U8000001pETNIA2","attachmentType":"ContentVersion"}}'  theme="slds"  ></omnistudio-flex-img>
      </div><div data-style-id="state0element1" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element1_child"   card={card}  record={record}  merge-field="%3Cdiv%20class=%22slds-text-heading_small%20slds-text-align_center%22%3E%3Cspan%20style=%22font-size:%2024pt;%22%3EWelcome%20to%20the%20%3Cspan%20style=%22color:%20#0f6b38;%22%3E%3Cstrong%3EDigital%20Account%20Opening%3C/strong%3E%3C/span%3E%20page.%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element2" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element2_child"   card={card}  record={record}  merge-field="%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cspan%20style=%22font-size:%2018pt;%22%3EIf%20you%20are%20here%20to%20create%20a%20new%20business%20account%20with%20%3Cstrong%3E%3Cspan%20style=%22color:%20#0f6b38;%22%3ERedwood%20Credit%20Union%3C/span%3E%3C/strong%3E%20please%20continue.%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element3" class="slds-col  slds-text-align_left slds-p-right_small slds-p-left_small  slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12  " data-rindex={rindex} style="      
         " ><omnistudio-output-field data-style-id="state0element3_child"   card={card}  record={record}  merge-field="%3Cdiv%20class=%22slds-text-body_regular%20slds-text-color_default%22%3E%0A%3Cdiv%20class=%22slds-text-body_regular%22%3E&nbsp;%3C/div%3E%0A%3Cdiv%20class=%22slds-text-body_regular%22%3E%3Cspan%20style=%22font-size:%2012pt;%22%3ELorem%20ipsum%20dolor%20sit%20amet,%20consectetur%20adipiscing%20elit.%20Proin%20vel%20orci%20vel%20sapien%20dignissim%20vehicula.%20Nam%20euismod%20est%20et%20ex%20tincidunt,%20nec%20varius%20mi%20lacinia.%20Fusce%20ultrices,%20libero%20id%20sollicitudin%20placerat,%20lorem%20eros%20pellentesque%20odio,%20at%20dignissim%20urna%20nisl%20sed%20ex.%20Vestibulum%20sit%20amet%20erat%20sapien.%20Vivamus%20condimentum%20auctor%20ligula,%20sed%20accumsan%20felis%20feugiat%20a.%20Suspendisse%20potenti.%20Nulla%20facilisi.%20Integer%20semper,%20mauris%20a%20venenatis%20bibendum,%20erat%20odio%20vulputate%20nisi,%20a%20pharetra%20elit%20nunc%20ut%20ex.%20Donec%20ac%20felis%20a%20lorem%20volutpat%20pulvinar.%20Mauris%20dapibus,%20magna%20vel%20dignissim%20ultrices,%20nulla%20erat%20dignissim%20nisi,%20non%20cursus%20neque%20ex%20nec%20enim.%3C/span%3E%3C/div%3E%0A%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div>
      </omnistudio-flex-card-state>
                        </template>
                      </template>
                      <template if:false={hasRecords}>
                            <omnistudio-flex-card-state  record={record} data-statue="false"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12"  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-bottom_x-small" style="">
          <div data-style-id="state0element0" class="slds-col   slds-large-size_12-of-12  slds-medium-size_1-of-12  slds-small-size_1-of-12  slds-size_1-of-12  " data-rindex={rindex} style="      
     height:70px;    " ><omnistudio-flex-img data-style-id="state0element0_child"   card={card}  record={record}  size=""  extraclass="slds-align_absolute-center"  state-img='\{"imgsrc":"/sfc/servlet.shepherd/version/download/068U8000001pETNIA2","title":"","alternativeText":"RCU Logo","document":{"label":"RCUlogo (Version:1)","value":"/sfc/servlet.shepherd/version/download/068U8000001pETNIA2","title":"RCUlogo","Id":"068U8000001pETNIA2","attachmentType":"ContentVersion"}}'  theme="slds"  ></omnistudio-flex-img>
      </div><div data-style-id="state0element1" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element1_child"   card={card}  record={record}  merge-field="%3Cdiv%20class=%22slds-text-heading_small%20slds-text-align_center%22%3E%3Cspan%20style=%22font-size:%2024pt;%22%3EWelcome%20to%20the%20%3Cspan%20style=%22color:%20#0f6b38;%22%3E%3Cstrong%3EDigital%20Account%20Opening%3C/strong%3E%3C/span%3E%20page.%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element2" class="slds-col   slds-size_12-of-12 " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element2_child"   card={card}  record={record}  merge-field="%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cspan%20style=%22font-size:%2018pt;%22%3EIf%20you%20are%20here%20to%20create%20a%20new%20business%20account%20with%20%3Cstrong%3E%3Cspan%20style=%22color:%20#0f6b38;%22%3ERedwood%20Credit%20Union%3C/span%3E%3C/strong%3E%20please%20continue.%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element3" class="slds-col  slds-text-align_left slds-p-right_small slds-p-left_small  slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12  " data-rindex={rindex} style="      
         " ><omnistudio-output-field data-style-id="state0element3_child"   card={card}  record={record}  merge-field="%3Cdiv%20class=%22slds-text-body_regular%20slds-text-color_default%22%3E%0A%3Cdiv%20class=%22slds-text-body_regular%22%3E&nbsp;%3C/div%3E%0A%3Cdiv%20class=%22slds-text-body_regular%22%3E%3Cspan%20style=%22font-size:%2012pt;%22%3ELorem%20ipsum%20dolor%20sit%20amet,%20consectetur%20adipiscing%20elit.%20Proin%20vel%20orci%20vel%20sapien%20dignissim%20vehicula.%20Nam%20euismod%20est%20et%20ex%20tincidunt,%20nec%20varius%20mi%20lacinia.%20Fusce%20ultrices,%20libero%20id%20sollicitudin%20placerat,%20lorem%20eros%20pellentesque%20odio,%20at%20dignissim%20urna%20nisl%20sed%20ex.%20Vestibulum%20sit%20amet%20erat%20sapien.%20Vivamus%20condimentum%20auctor%20ligula,%20sed%20accumsan%20felis%20feugiat%20a.%20Suspendisse%20potenti.%20Nulla%20facilisi.%20Integer%20semper,%20mauris%20a%20venenatis%20bibendum,%20erat%20odio%20vulputate%20nisi,%20a%20pharetra%20elit%20nunc%20ut%20ex.%20Donec%20ac%20felis%20a%20lorem%20volutpat%20pulvinar.%20Mauris%20dapibus,%20magna%20vel%20dignissim%20ultrices,%20nulla%20erat%20dignissim%20nisi,%20non%20cursus%20neque%20ex%20nec%20enim.%3C/span%3E%3C/div%3E%0A%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div>
      </omnistudio-flex-card-state>
                      </template>
            </template>
            <template if:true={hasError}>
            {error}
            </template>
            
      <omnistudio-action action-wrapperclass="slds-hide" re-render-flyout class="action-trigger slds-col" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
      
      </omnistudio-action>
  
            <omnistudio-action class="execute-action" re-render-flyout action-wrapperclass="slds-hide" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
            
            </omnistudio-action>
            </div>
          </template>