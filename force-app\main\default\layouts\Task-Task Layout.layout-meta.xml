<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Task Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Subject</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>WhoId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ActivityDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>WhatId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Related To</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Description Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Additional Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Priority</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Status</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Other Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsReminderSet</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsRecurrence</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Follow_Up</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Task.EditDescription</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Task.Defer</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Task.UpdateStatus</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Task.UpdatePriority</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList>
        <quickActionListItems>
            <quickActionName>Task.Defer_3</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus_3</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority_3</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription_2</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.Defer_2</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus_2</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority_2</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription_1</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.Defer_1</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus_1</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority_1</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>NewEvent</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription_5</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription_11</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.Defer_11</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus_11</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority_11</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription_10</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.Defer_10</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus_10</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority_10</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.Defer</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription_0</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.Defer_0</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus_0</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority_0</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.TextPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.ContentPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.LinkPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.PollPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>NewTask</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>LogACall</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription_6</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.Defer_6</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus_6</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority_6</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus_5</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority_5</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription_9</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.Defer_9</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus_9</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority_9</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription_8</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.Defer_8</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus_8</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority_8</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.Defer_5</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription_7</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.Defer_7</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus_7</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority_7</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription_4</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.Defer_4</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdateStatus_4</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.UpdatePriority_4</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>Task.EditDescription_3</quickActionName>
        </quickActionListItems>
    </quickActionList>
    <relatedContent>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>WhoId</field>
            </layoutItem>
        </relatedContentItems>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>WhatId</field>
            </layoutItem>
        </relatedContentItems>
    </relatedContent>
    <relatedLists>
        <relatedList>RelatedActivityAttachmentList</relatedList>
    </relatedLists>
    <relatedObjects>WhoId</relatedObjects>
    <relatedObjects>WhatId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h36000003jARj</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
