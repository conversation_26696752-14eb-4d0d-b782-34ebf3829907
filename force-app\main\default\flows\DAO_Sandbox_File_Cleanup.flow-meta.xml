<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Application_Id_Collection</name>
        <label>Application Id Collection</label>
        <locationX>264</locationX>
        <locationY>684</locationY>
        <assignmentItems>
            <assignToReference>AppIdCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>applicationLoop.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>applicationLoop</targetReference>
        </connector>
    </assignments>
    <description>Adjusted DateTimeLimit var to a shorter window.</description>
    <environments>Default</environments>
    <formulas>
        <name>Attachment_5_Days</name>
        <dataType>Date</dataType>
        <expression>Now() - 5</expression>
    </formulas>
    <formulas>
        <name>dateTimeLimit</name>
        <dataType>DateTime</dataType>
        <expression>Now() - 1</expression>
    </formulas>
    <interviewLabel>Sandbox File Cleanup {!$Flow.CurrentDateTime}</interviewLabel>
    <label>DAO Sandbox File Cleanup</label>
    <loops>
        <name>applicationLoop</name>
        <label>applicationLoop</label>
        <locationX>176</locationX>
        <locationY>576</locationY>
        <collectionReference>Applications</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Application_Id_Collection</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Roles</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordDeletes>
        <name>Delete_Applications</name>
        <label>Delete Applications</label>
        <locationX>176</locationX>
        <locationY>1524</locationY>
        <inputReference>Applications</inputReference>
    </recordDeletes>
    <recordDeletes>
        <name>Delete_Approvals</name>
        <label>Delete Approvals</label>
        <locationX>176</locationX>
        <locationY>1416</locationY>
        <connector>
            <targetReference>Delete_Applications</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Delete_Applications</targetReference>
        </faultConnector>
        <inputReference>Approvals</inputReference>
    </recordDeletes>
    <recordDeletes>
        <name>Delete_Attachments</name>
        <label>Delete Attachments</label>
        <locationX>176</locationX>
        <locationY>360</locationY>
        <connector>
            <targetReference>Applications</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Applications</targetReference>
        </faultConnector>
        <inputReference>Get_Attachments</inputReference>
    </recordDeletes>
    <recordDeletes>
        <name>Delete_Products</name>
        <label>Delete Products</label>
        <locationX>176</locationX>
        <locationY>1200</locationY>
        <connector>
            <targetReference>Approvals</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Approvals</targetReference>
        </faultConnector>
        <inputReference>Products</inputReference>
    </recordDeletes>
    <recordDeletes>
        <name>Delete_Roles</name>
        <label>Delete Roles</label>
        <locationX>176</locationX>
        <locationY>984</locationY>
        <connector>
            <targetReference>Products</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Products</targetReference>
        </faultConnector>
        <inputReference>Roles</inputReference>
    </recordDeletes>
    <recordLookups>
        <name>Applications</name>
        <label>Applications</label>
        <locationX>176</locationX>
        <locationY>468</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>applicationLoop</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Roles</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CreatedDate</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>dateTimeLimit</elementReference>
            </value>
        </filters>
        <filters>
            <field>Persistent__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>a1IU800000WtnX7MAJ</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>DAO_Application__c</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Approvals</name>
        <label>Approvals</label>
        <locationX>176</locationX>
        <locationY>1308</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Delete_Approvals</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Delete_Applications</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DAO_Application__c</field>
            <operator>In</operator>
            <value>
                <elementReference>AppIdCollection</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>DAO_Approvals__c</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Attachments</name>
        <label>Get Attachments</label>
        <locationX>176</locationX>
        <locationY>252</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Delete_Attachments</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Applications</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CreatedDate</field>
            <operator>LessThanOrEqualTo</operator>
            <value>
                <elementReference>Attachment_5_Days</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Attachment</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Products</name>
        <label>Products</label>
        <locationX>176</locationX>
        <locationY>1092</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Delete_Products</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Approvals</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DAO_Application__c</field>
            <operator>In</operator>
            <value>
                <elementReference>AppIdCollection</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>DAO_Products__c</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Roles</name>
        <label>Roles</label>
        <locationX>176</locationX>
        <locationY>876</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Delete_Roles</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Products</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DAO_Application__c</field>
            <operator>In</operator>
            <value>
                <elementReference>AppIdCollection</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>DAO_Roles__c</object>
        <queriedFields>Id</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Attachments</targetReference>
        </connector>
        <schedule>
            <frequency>Daily</frequency>
            <startDate>2025-04-04</startDate>
            <startTime>07:00:00.000Z</startTime>
        </schedule>
        <triggerType>Scheduled</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>AppIdCollection</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>idToDelete</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>profileId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>roleCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>DAO_Roles__c</objectType>
    </variables>
    <variables>
        <name>UpdateCommunityUsers</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
</Flow>
