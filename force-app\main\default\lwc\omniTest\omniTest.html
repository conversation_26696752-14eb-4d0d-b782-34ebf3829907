<template>
    <lightning-card>
        <lightning-record-edit-form object-api-name="DAO_Application__c">
            <div class="main-question">
                
                <div class="question">
                    <div class="ques-width required-label">Does a portion of your income come from Internet Gambling?</div>
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="Internet_Gambling__c"
                            value="Yes"
                            label="Yes"
                            checked={questions.Internet_Gambling__c.yesChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="Internet_Gambling__c"
                            value="No"
                            label="No"
                            checked={questions.Internet_Gambling__c.noChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
            <div class="main-question">
                <div class="question">
                    <div class="ques-width required-label">Is this a marijuana related business?</div>
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="Marijuana_Business__c"
                            value="Yes"
                            label="Yes"
                            checked={questions.Marijuana_Business__c.yesChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="Marijuana_Business__c"
                            value="No"
                            label="No"
                            checked={questions.Marijuana_Business__c.noChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
            <!-- Is this a marijuana related business? -->
             <div>
                 <div class="sub-question" if:true={questions.Marijuana_Business__c.showSubquestions}>
                     <div class="question required-label">
                         Are you licensed by the state?
                     </div>
                     <div class="input">
                         <div>
                             <lightning-input
                                 type="radio"
                                 name="MarijuanaLicensed__c"
                                 value="Yes"
                                 label="Yes"
                                 data-parent={questions.Marijuana_Business__c.subquestions.MarijuanaLicensed__c.parent}
                                 checked={questions.Marijuana_Business__c.subquestions.MarijuanaLicensed__c.yesChecked}
                                 onchange={handleSubRadioChange}
                             >
                             </lightning-input>
                         </div>
                         <div>
                             <lightning-input
                                 type="radio"
                                 name="MarijuanaLicensed__c"
                                 value="No"
                                 label="No"
                                 data-parent={questions.Marijuana_Business__c.subquestions.MarijuanaLicensed__c.parent}
                                 checked={questions.Marijuana_Business__c.subquestions.MarijuanaLicensed__c.noChecked}
                                 onchange={handleSubRadioChange}
                             >
                             </lightning-input>
                         </div>
                     </div>
                 </div>
                 <div class="sub-question" if:true={questions.Marijuana_Business__c.showSubquestions}>
                     <div class="question required-label">
                         What percentage of your revenue is derived from marijuana-related activity?
                     </div>
                     <div class="input-picklist">
                         <div>
                             <lightning-input-field field-name="MarijuanaPercentage__c" data-field="MarijuanaPercentage__c" data-parent={questions.Marijuana_Business__c.subquestions.MarijuanaPercentage__c.parent} variant="label-hidden" onchange={handlePicklistChange} value={questions.Marijuana_Business__c.subquestions.MarijuanaPercentage__c.value}></lightning-input-field>
                         </div>
                     </div>
                 </div>
                 <div class="sub-question" if:true={questions.Marijuana_Business__c.showSubquestions}>
                     <div class="question required-label">
                         What type of marijuana-related activity does your business engage in?
                     </div>
                     <div class="input-multi-picklist">
                         <div>
                            <lightning-input-field field-name="MarijuanaActivity__c" data-field="MarijuanaActivity__c" data-parent={questions.Marijuana_Business__c.subquestions.MarijuanaActivity__c.parent} variant="label-hidden" onchange={handlePicklistChange} value={questions.Marijuana_Business__c.subquestions.MarijuanaActivity__c.value}></lightning-input-field>
                         </div>
                     </div>
                 </div>
             </div>
            <!-- Is this a marijuana related business? -->

            <div class="main-question">
                <div class="question">
                    <div class="ques-width required-label">Do you act as a Professional Service Provider - i.e. act as an intermediary between your clients and the bank, performing services or arranging for services to be performed on your client's behalf?</div>
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="Intermediary__c"
                            value="Yes"
                            label="Yes"
                            checked={questions.Intermediary__c.yesChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="Intermediary__c"
                            value="No"
                            label="No"
                            checked={questions.Intermediary__c.noChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
            <!-- Do you act as a Professional Service Provider - i.e. act as an intermediary between your clients and the bank, performing services or arranging for services to be performed on your client's behalf? -->
            <div>
                <div class="sub-question" if:true={questions.Intermediary__c.showSubquestions}>
                    <div class="question required-label">
                        What is the primary type of Professional Service Provider that best matches your business type?
                    </div>
                    <div class="input-picklist">
                        <div>
                            <lightning-input-field field-name="ProfessionalType__c" data-field="ProfessionalType__c" data-parent={questions.Intermediary__c.subquestions.ProfessionalType__c.parent} variant="label-hidden" onchange={handlePicklistChange} value={questions.Intermediary__c.subquestions.ProfessionalType__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
                <div if:true={questions.Intermediary__c.showSubquestions}>
                    <div class="sub-sub-question" if:true={questions.Intermediary__c.subquestions.ProfessionalType__c.showSubSubquestions}>
                        <div class="question required-label">
                            Description
                        </div>
                        <div class="input">
                            <div>
                                <lightning-input-field field-name="ProfessionalTypeOtherText__c" data-field="ProfessionalTypeOtherText__c" data-parent={questions.Intermediary__c.subquestions.ProfessionalType__c.subsubquestions.ProfessionalTypeOtherText__c.parent} data-grandparent={questions.Intermediary__c.subquestions.ProfessionalType__c.subsubquestions.ProfessionalTypeOtherText__c.grandParent} variant="label-hidden" onchange={handleSubSubPicklistChange} value={questions.Intermediary__c.subquestions.ProfessionalType__c.subsubquestions.ProfessionalTypeOtherText__c.value}></lightning-input-field>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sub-question" if:true={questions.Intermediary__c.showSubquestions}>
                    <div class="question required-label">
                        What services do you provide?
                    </div>
                    <div class="input-multi-picklist">
                        <div>
                            <lightning-input-field field-name="ServicesTypes__c" data-field="ServicesTypes__c" data-parent={questions.Intermediary__c.subquestions.ServicesTypes__c.parent} variant="label-hidden" onchange={handlePicklistChange} value={questions.Intermediary__c.subquestions.ServicesTypes__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
                <div class="sub-question" if:true={questions.Intermediary__c.showSubquestions}>
                    <div class="question required-label">
                        Will other professionals be using this account?
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input
                                type="radio"
                                name="ProfessionalOthersUsing__c"
                                value="Yes"
                                label="Yes"
                                data-parent={questions.Intermediary__c.subquestions.ProfessionalOthersUsing__c.parent}
                                checked={questions.Intermediary__c.subquestions.ProfessionalOthersUsing__c.yesChecked}
                                onchange={handleSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                        <div>
                            <lightning-input
                                type="radio"
                                name="ProfessionalOthersUsing__c"
                                value="No"
                                label="No"
                                data-parent={questions.Intermediary__c.subquestions.ProfessionalOthersUsing__c.parent}
                                checked={questions.Intermediary__c.subquestions.ProfessionalOthersUsing__c.noChecked}
                                onchange={handleSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Do you act as a Professional Service Provider - i.e. act as an intermediary between your clients and the bank, performing services or arranging for services to be performed on your client's behalf? -->
            <div class="main-question">
                <div class="question">
                    <div class="ques-width required-label">Does your company offer courier services or armored car services to ship currency on your customer’s behalf?</div>
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="Courier_Services__c"
                            value="Yes"
                            label="Yes"
                            checked={questions.Courier_Services__c.yesChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="Courier_Services__c"
                            value="No"
                            label="No"
                            checked={questions.Courier_Services__c.noChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
            <div class="main-question">
                <div class="question">
                    <div class="ques-width required-label">Will you be processing transactions that benefit a third party as a payment processor?</div>
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="Third_Party_Payment_Processor__c"
                            value="Yes"
                            label="Yes"
                            checked={questions.Third_Party_Payment_Processor__c.yesChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="Third_Party_Payment_Processor__c"
                            value="No"
                            label="No"
                            checked={questions.Third_Party_Payment_Processor__c.noChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
            <!-- Will you be processing transactions that benefit a third party as a payment processor ? -->
             <div>
                <div class="sub-question" if:true={questions.Third_Party_Payment_Processor__c.showSubquestions}>
                    <div class="question">
                        Do you send payments on behalf of your clients?
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input-field field-name="TransactionsSendPayments__c" data-field="TransactionsSendPayments__c" data-parent={questions.Third_Party_Payment_Processor__c.subquestions.TransactionsSendPayments__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Third_Party_Payment_Processor__c.subquestions.TransactionsSendPayments__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
                <div class="sub-question" if:true={questions.Third_Party_Payment_Processor__c.showSubquestions}>
                    <div class="question">
                        Do you receive payments on behalf of your clients?
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input-field field-name="TransactionsReceivePayments__c" data-field="TransactionsReceivePayments__c" data-parent={questions.Third_Party_Payment_Processor__c.subquestions.TransactionsReceivePayments__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Third_Party_Payment_Processor__c.subquestions.TransactionsReceivePayments__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
                <div class="sub-question" if:true={questions.Third_Party_Payment_Processor__c.showSubquestions}>
                    <div class="question required-label">
                        What types of payment services do you offer?
                    </div>
                    <div class="input-multi-picklist">
                        <div>
                            <lightning-input-field field-name="PaymentServices__c" data-field="PaymentServices__c" data-parent={questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.parent} variant="label-hidden" onchange={handlePicklistChange} value={questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
                <div if:true={questions.Third_Party_Payment_Processor__c.showSubquestions}>
                    <div class="sub-sub-question" if:true={questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.showSubSubquestions}>
                        <div class="question required-label">
                            Description
                        </div>
                        <div class="input">
                            <div>
                                <lightning-input-field field-name="PaymentServicesOtherText__c" data-field="PaymentServicesOtherText__c" data-parent={questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.subsubquestions.PaymentServicesOtherText__c.parent} data-grandparent={questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.subsubquestions.PaymentServicesOtherText__c.grandParent} variant="label-hidden" onchange={handleSubSubPicklistChange} value={questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.subsubquestions.PaymentServicesOtherText__c.value}></lightning-input-field>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="sub-question" if:true={questions.Third_Party_Payment_Processor__c.showSubquestions}>
                    <div class="question required-label">
                        Will client payments run through your accounts with us?
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input
                                type="radio"
                                name="PaymentsThroughAccounts__c"
                                value="Yes"
                                label="Yes"
                                data-parent={questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.parent}
                                checked={questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.yesChecked}
                                onchange={handleSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                        <div>
                            <lightning-input
                                type="radio"
                                name="PaymentsThroughAccounts__c"
                                value="No"
                                label="No"
                                data-parent={questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.parent}
                                checked={questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.noChecked}
                                onchange={handleSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                    </div>
                </div>
             </div>
             <div>
                <!-- Will client payments run through your accounts with us ? -->
                <div if:true={questions.Third_Party_Payment_Processor__c.showSubquestions}>
                    <div class="sub-sub-question" if:true={questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.showSubSubquestions}>
                        <div class="question required-label">
                            How are client transactions processed through your account?
                        </div>
                        <div class="input-multi-picklist">
                            <div>
                                <lightning-input-field field-name="PaymentsHowProcessed__c" data-field="PaymentsHowProcessed__c" data-parent={questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.subsubquestions.PaymentsHowProcessed__c.parent} data-grandparent={questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.subsubquestions.PaymentsHowProcessed__c.grandParent} variant="label-hidden" onchange={handleSubSubPicklistChange} value={questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.subsubquestions.PaymentsHowProcessed__c.value}></lightning-input-field>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Will client payments run through your accounts with us ? -->
             </div>
             <div class="sub-question" if:true={questions.Third_Party_Payment_Processor__c.showSubquestions}>
                <div class="question required-label">
                    Do you create checks remotely on behalf of your clients?
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="CreateChecksRemotely__c"
                            value="Yes"
                            label="Yes"
                            data-parent={questions.Third_Party_Payment_Processor__c.subquestions.CreateChecksRemotely__c.parent}
                            checked={questions.Third_Party_Payment_Processor__c.subquestions.CreateChecksRemotely__c.yesChecked}
                            onchange={handleSubRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="CreateChecksRemotely__c"
                            value="No"
                            label="No"
                            data-parent={questions.Third_Party_Payment_Processor__c.subquestions.CreateChecksRemotely__c.parent}
                            checked={questions.Third_Party_Payment_Processor__c.subquestions.CreateChecksRemotely__c.noChecked}
                            onchange={handleSubRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Third_Party_Payment_Processor__c.showSubquestions}>
                <div class="question required-label">
                    Do you have any restrictions on the types of businesses you accept as a customers?
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="BusinessTypeRestrictions__c"
                            value="Yes"
                            label="Yes"
                            data-parent={questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.parent}
                            checked={questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.yesChecked}
                            onchange={handleSubRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="BusinessTypeRestrictions__c"
                            value="No"
                            label="No"
                            data-parent={questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.parent}
                            checked={questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.noChecked}
                            onchange={handleSubRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
            <div>
                <!-- Do you have any restrictions on the types of businesses you accept as acustomers ? -->
                <div if:true={questions.Third_Party_Payment_Processor__c.showSubquestions}>
                    <div class="sub-sub-question" if:true={questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.showSubSubquestions}>
                        <div class="question required-label">
                            What are your restrictions?
                        </div>
                        <div class="input-picklist">
                            <div>
                                <lightning-input-field field-name="BusinessTypeRestrictionsText__c" data-field="BusinessTypeRestrictionsText__c" data-parent={questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.subsubquestions.BusinessTypeRestrictionsText__c.parent} data-grandparent={questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.subsubquestions.BusinessTypeRestrictionsText__c.grandParent} variant="label-hidden" onchange={handleSubSubTextChange} value={questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.subsubquestions.BusinessTypeRestrictionsText__c.value}></lightning-input-field>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Do you have any restrictions on the types of businesses you accept as acustomers ? -->
             </div>
            <!-- Will you be processing transactions that benefit a third party as a payment processor ? -->
            <div class="main-question">
                <div class="question">
                    <div class="ques-width required-label">Do you own, operate, or replenish an ATM?</div>
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="ATM_Business__c"
                            value="Yes"
                            label="Yes"
                            checked={questions.ATM_Business__c.yesChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="ATM_Business__c"
                            value="No"
                            label="No"
                            checked={questions.ATM_Business__c.noChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
            <!-- Do you own, operate, or replenish an ATM ? -->
            <div class="sub-question" if:true={questions.ATM_Business__c.showSubquestions}>
                <div class="question required-label">
                    How many ATMs do you own, operate, or replenish?
                </div>
                <div class="input-picklist">
                    <div>
                        <lightning-input-field field-name="NumberOfAtm__c" data-field="NumberOfAtm__c" data-parent={questions.ATM_Business__c.subquestions.NumberOfAtm__c.parent} variant="label-hidden" onchange={handlePicklistChange} value={questions.ATM_Business__c.subquestions.NumberOfAtm__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.ATM_Business__c.showSubquestions}>
                <div class="question required-label">
                    Do you have access to replenish the cash within the ATM?
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="ReplenishAtmCash__c"
                            value="Yes"
                            label="Yes"
                            data-parent={questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.parent}
                            checked={questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.yesChecked}
                            onchange={handleSubRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="ReplenishAtmCash__c"
                            value="No"
                            label="No"
                            data-parent={questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.parent}
                            checked={questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.noChecked}
                            onchange={handleSubRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
            <div>
                <!-- Do you have access to replenish the cash within the ATM ? -->
                <div if:true={questions.ATM_Business__c.showSubquestions}>
                    <div class="sub-sub-question" if:true={questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.showSubSubquestions}>
                        <div class="question required-label">
                            What is the source of cash?
                        </div>
                        <div class="input-multi-picklist">
                            <div>
                                <lightning-input-field field-name="SourceOfAtmCash__c" data-field="SourceOfAtmCash__c" data-parent={questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.subsubquestions.SourceOfAtmCash__c.parent} data-grandparent={questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.subsubquestions.SourceOfAtmCash__c.grandParent} variant="label-hidden" onchange={handleSubSubTextChange} value={questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.subsubquestions.SourceOfAtmCash__c.value}></lightning-input-field>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Do you have access to replenish the cash within the ATM ? -->
            </div>
            <div class="sub-question" if:true={questions.ATM_Business__c.showSubquestions}>
                <div class="question required-label">
                    What is the maximum amount that any one of your ATM(s) can hold?
                </div>
                <div class="input-picklist">
                    <div>
                        <lightning-input-field field-name="AtmMaxHolding__c" data-field="AtmMaxHolding__c" data-parent={questions.ATM_Business__c.subquestions.AtmMaxHolding__c.parent} variant="label-hidden" onchange={handlePicklistChange} value={questions.ATM_Business__c.subquestions.AtmMaxHolding__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.ATM_Business__c.showSubquestions}>
                <div class="question required-label">
                    What denominations are dispensed from your machines?
                </div>
                <div class="input-multi-picklist">
                    <div>
                        <lightning-input-field field-name="AtmDenomination__c" data-field="AtmDenomination__c" data-parent={questions.ATM_Business__c.subquestions.AtmDenomination__c.parent} variant="label-hidden" onchange={handlePicklistChange} value={questions.ATM_Business__c.subquestions.AtmDenomination__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.ATM_Business__c.showSubquestions}>
                <div class="question required-label">
                    What is the type of your private ATM?
                </div>
                <div class="input-multi-picklist">
                    <div>
                        <lightning-input-field field-name="PrivateAtmType__c" data-field="PrivateAtmType__c" data-parent={questions.ATM_Business__c.subquestions.PrivateAtmType__c.parent} variant="label-hidden" onchange={handlePicklistChange} value={questions.ATM_Business__c.subquestions.PrivateAtmType__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <!-- Do you own, operate, or replenish an ATM ? -->
            <div class="main-question">
                <div class="question">
                    <div class="ques-width required-label">Could your business be considered a Non-Bank Financial Institution?</div>
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="Non_Bank_Financial_Institution__c"
                            value="Yes"
                            label="Yes"
                            checked={questions.Non_Bank_Financial_Institution__c.yesChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="Non_Bank_Financial_Institution__c"
                            value="No"
                            label="No"
                            checked={questions.Non_Bank_Financial_Institution__c.noChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
            <!-- Could your business be considered a Non-Bank Financial Institution ? -->
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question">
                    Does your business involve casinos, card clubs or gaming establishments (with annual revenues greater than one million dollars)?
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveCasinos__c" data-field="InvolveCasinos__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveCasinos__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveCasinos__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question">
                    Does your business involve securities, futures commissions or commodity trading?
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveSecurities__c" data-field="InvolveSecurities__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div>
                <!-- Does your business involve Securities, futures commissions or commodity trading ? -->
                <div if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                    <div class="sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.showSubSubquestions}>
                        <div class="question required-label">
                            Is the Nonbank financial institution one of the following?
                        </div>
                        <div class="input-multi-picklist">
                            <div>
                                <lightning-input-field field-name="SecuritiesFinancialInstitution__c" data-field="SecuritiesFinancialInstitution__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesFinancialInstitution__c.parent} data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesFinancialInstitution__c.grandParent} variant="label-hidden" onchange={handleSubSubTextChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesFinancialInstitution__c.value}></lightning-input-field>
                            </div>
                        </div>
                    </div>
                    <div class="sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.showSubSubquestions}>
                        <div class="question required-label">
                            How is your business registered?
                        </div>
                        <div class="input-multi-picklist">
                            <div>
                                <lightning-input-field field-name="SecuritiesHowBusinessRegistered__c" data-field="SecuritiesHowBusinessRegistered__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesHowBusinessRegistered__c.parent} data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesHowBusinessRegistered__c.grandParent} variant="label-hidden" onchange={handleSubSubTextChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesHowBusinessRegistered__c.value}></lightning-input-field>
                            </div>
                        </div>
                    </div>
                    <div class="sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.showSubSubquestions}>
                        <div class="question required-label">
                            Do you invest your clients' funds?
                        </div>
                        <div class="input">
                            <div>
                                <lightning-input
                                    type="radio"
                                    name="SecuritiesInvolveSecurities__c"
                                    value="Yes"
                                    label="Yes"
                                    data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.parent}
                                    data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.grandParent}
                                    checked={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.yesChecked}
                                    onchange={handleSubSubRadioChange}
                                >
                                </lightning-input>
                            </div>
                            <div>
                                <lightning-input
                                    type="radio"
                                    name="SecuritiesInvolveSecurities__c"
                                    value="No"
                                    label="No"
                                    data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.parent}
                                    data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.grandParent}
                                    checked={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.noChecked}
                                    onchange={handleSubSubRadioChange}
                                >
                                </lightning-input>
                            </div>
                        </div>
                    </div>
                </div>
                <div if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                    <div class="sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.showSubSubquestions}>
                        <div class="sub-sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.showSubSubSubquestions}>
                            <div class="question required-label">
                                In what types of products do you invest?
                            </div>
                            <div class="input-multi-picklist">
                                <div>
                                    <lightning-input-field field-name="SecuritiesProductTypes__c" 
                                    data-field="SecuritiesProductTypes__c" 
                                    data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesProductTypes__c.parent}
                                    data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesProductTypes__c.grandParent}
                                    data-greatgrandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesProductTypes__c.greatgrandParent}
                                    variant="label-hidden" 
                                    onchange={handleSubSubSubPicklistChange} 
                                    value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesProductTypes__c.value}
                                    >
                                </lightning-input-field>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                    <div class="sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.showSubSubquestions}>
                        <div class="sub-sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.showSubSubSubquestions}>
                            <div class="question required-label">
                                Do you invest your clients' funds internationally?
                            </div>
                            <div class="input">
                                <div>
                                    <lightning-input
                                        type="radio"
                                        name="SecuritiesInvestFundsInternationally__c"
                                        value="Yes"
                                        label="Yes"
                                        data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.parent}
                                        data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.grandParent}
                                        data-greatgrandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.greatgrandParent}
                                        checked={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.yesChecked}
                                        onchange={handleSubSubSubRadioChange}
                                    >
                                    </lightning-input>
                                </div>
                                <div>
                                    <lightning-input
                                        type="radio"
                                        name="SecuritiesInvestFundsInternationally__c"
                                        value="No"
                                        label="No"
                                        data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.parent}
                                        data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.grandParent}
                                        data-greatgrandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.greatgrandParent}
                                        checked={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.noChecked}
                                        onchange={handleSubSubSubRadioChange}
                                    >
                                    </lightning-input>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                    <div class="sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.showSubSubquestions}>
                        <div class="sub-sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.showSubSubSubquestions}>
                            <div class="sub-sub-sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.showSubSubSubSubquestions}>
                                <div class="question required-label">
                                    In what countries do you invest your clients' funds internationally?
                                </div>
                                <div class="input">
                                    <div>
                                        <lightning-input-field field-name="SecuritiesCountriesText__c" 
                                            data-field="SecuritiesCountriesText__c" 
                                            data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.subsubsubsubquestions.SecuritiesCountriesText__c.parent}
                                            data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.subsubsubsubquestions.SecuritiesCountriesText__c.grandParent}
                                            data-greatgrandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.subsubsubsubquestions.SecuritiesCountriesText__c.greatgrandParent}
                                            data-greatgreatgrandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.subsubsubsubquestions.SecuritiesCountriesText__c.greatGreategrandParent}
                                            value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.subsubsubsubquestions.SecuritiesCountriesText__c.value}
                                            variant="label-hidden"
                                            onchange={handleSubSubSubSubTextChange}
                                            >
                                        </lightning-input-field>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Does your business involve Securities, futures commissions or commodity trading ? -->
             </div>
             <div if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.showSubSubquestions}>
                    <div class="sub-sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.showSubSubSubquestions}>
                        <div class="question required-label">
                            What types of services apply to your business?
                        </div>
                        <div class="input-multi-picklist">
                            <div>
                                <lightning-input-field field-name="SecuritiesServiceTypes__c" 
                                data-field="SecuritiesServiceTypes__c" 
                                data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesServiceTypes__c.parent}
                                data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesServiceTypes__c.grandParent}
                                data-greatgrandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesServiceTypes__c.greatgrandParent}
                                variant="label-hidden" 
                                onchange={handleSubSubSubPicklistChange} 
                                value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesServiceTypes__c.value}
                                >
                            </lightning-input-field>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question">
                    Insurance
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveInsurance__c" data-field="InvolveInsurance__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.showSubSubquestions}>
                    <div class="question required-label">
                        Is this a State-regulated insurance company?
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input
                                type="radio"
                                name="InvolveInsuranceStateRegIns__c"
                                value="Yes"
                                label="Yes"
                                data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.subsubquestions.InvolveInsuranceStateRegIns__c.parent}
                                data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.subsubquestions.InvolveInsuranceStateRegIns__c.grandParent}
                                checked={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.subsubquestions.InvolveInsuranceStateRegIns__c.yesChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                        <div>
                            <lightning-input
                                type="radio"
                                name="InvolveInsuranceStateRegIns__c"
                                value="No"
                                label="No"
                                data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.subsubquestions.InvolveInsuranceStateRegIns__c.parent}
                                data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.subsubquestions.InvolveInsuranceStateRegIns__c.grandParent}
                                checked={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.subsubquestions.InvolveInsuranceStateRegIns__c.noChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question">
                    Loan/Finance
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveLoanFinance__c" data-field="InvolveLoanFinance__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveLoanFinance__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveLoanFinance__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question">
                    Credit cards system operation
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveCreditCards__c" data-field="InvolveCreditCards__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveCreditCards__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveCreditCards__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question">
                    Precious metals, stones, or jewels (with purchases or sales of more than $50,000 per year)
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolvePreciousMetals__c" data-field="InvolvePreciousMetals__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.showSubSubquestions}>
                    <div class="question ques-width required-label">
                        In the previous year, did your business purchase more than $50,000 of jewels, precious metals, or precious stones from anyone other than US based dealers or other retailers? This does not include items taken in on trade to offset the price of a purchased item.
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input
                                type="radio"
                                name="InvolvePreciousMetalsBuy50k__c"
                                value="Yes"
                                label="Yes"
                                data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsBuy50k__c.parent}
                                data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsBuy50k__c.grandParent}
                                checked={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsBuy50k__c.yesChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                        <div>
                            <lightning-input
                                type="radio"
                                name="InvolvePreciousMetalsBuy50k__c"
                                value="No"
                                label="No"
                                data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsBuy50k__c.parent}
                                data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsBuy50k__c.grandParent}
                                checked={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsBuy50k__c.noChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.showSubSubquestions}>
                    <div class="question ques-width required-label">
                        Did your business sell more than $50,000 of jewels, precious metals, or precious stones in the previous year
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input
                                type="radio"
                                name="InvolvePreciousMetalsSell50k__c"
                                value="Yes"
                                label="Yes"
                                data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsSell50k__c.parent}
                                data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsSell50k__c.grandParent}
                                checked={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsSell50k__c.yesChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                        <div>
                            <lightning-input
                                type="radio"
                                name="InvolvePreciousMetalsSell50k__c"
                                value="No"
                                label="No"
                                data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsSell50k__c.parent}
                                data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsSell50k__c.grandParent}
                                checked={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsSell50k__c.noChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question">
                    Pawn brokerage
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolvePawnBrokerage__c" data-field="InvolvePawnBrokerage__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePawnBrokerage__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePawnBrokerage__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question">
                    Travel agency
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveTravelAgency__c" data-field="InvolveTravelAgency__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveTravelAgency__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveTravelAgency__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question">
                    Telegraph company
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveTelegraphCompany__c" data-field="InvolveTelegraphCompany__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveTelegraphCompany__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveTelegraphCompany__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question">
                    Vehicle sales
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveVehicleSales__c" data-field="InvolveVehicleSales__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveVehicleSales__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveVehicleSales__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveVehicleSales__c.showSubSubquestions}>
                    <div class="question required-label">
                        What types of transportation vehicles do you sell?
                    </div>
                    <div class="input-multi-picklist">
                        <div>
                            <lightning-input-field field-name="InvolveVehicleTypes__c" 
                            data-field="InvolveVehicleTypes__c" 
                            data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveVehicleSales__c.subsubquestions.InvolveVehicleTypes__c.parent}
                            data-grandparent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveVehicleSales__c.subsubquestions.InvolveVehicleTypes__c.grandParent}
                            variant="label-hidden" 
                            onchange={handleSubSubPicklistChange} 
                            value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveVehicleSales__c.subsubquestions.InvolveVehicleTypes__c.value}
                            >
                        </lightning-input-field>
                        </div>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question">
                    Real estate closing and settlement
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveRealEstateClosing__c" data-field="InvolveRealEstateClosing__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveRealEstateClosing__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveRealEstateClosing__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question">
                    U.S. Postal Service
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolvePostalService__c" data-field="InvolvePostalService__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePostalService__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePostalService__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question">
                    Federal, state or local government agency carrying out a duty or power of a business described above
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveGovAgency__c" data-field="InvolveGovAgency__c" data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveGovAgency__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Non_Bank_Financial_Institution__c.subquestions.InvolveGovAgency__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
           
            <div if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions} class="independent-text">
                (If you selected "Casinos, card clubs or gaming establishments (with annual revenues greater than one million dollars)", "Securities, futures commissions or commodity trading", "Insurance", "Loan/Finance", "Credit cards system operation", or "Precious metals, stones, or jewels (with purchases or sales of more than $50,000 per year)" above, complete the following.)
            </div>
            <div class="sub-question" if:true={questions.Non_Bank_Financial_Institution__c.showSubquestions}>
                <div class="question required-label">
                    Do you have a documented BSA/AML program?
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="DocBsaAmlProgram__c"
                            value="Yes"
                            label="Yes"
                            data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.DocBsaAmlProgram__c.parent}
                            checked={questions.Non_Bank_Financial_Institution__c.subquestions.DocBsaAmlProgram__c.yesChecked}
                            onchange={handleSubRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="DocBsaAmlProgram__c"
                            value="No"
                            label="No"
                            data-parent={questions.Non_Bank_Financial_Institution__c.subquestions.DocBsaAmlProgram__c.parent}
                            checked={questions.Non_Bank_Financial_Institution__c.subquestions.DocBsaAmlProgram__c.noChecked}
                            onchange={handleSubRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
            <div class="main-question">
                <div class="question">
                    <div class="ques-width required-label">Could your business be considered a Money Service Business?</div>
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="Money_Service_Business__c"
                            value="Yes"
                            label="Yes"
                            checked={questions.Money_Service_Business__c.yesChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="Money_Service_Business__c"
                            value="No"
                            label="No"
                            checked={questions.Money_Service_Business__c.noChecked}
                            onchange={handleRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions} class="independent-text">
                Does your business involve any of the following?
            </div>
            <div class="sub-question" if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="question">
                    Foreign currency exchange in amounts greater than $1,000 for any one person in any one day
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveCurrencyExchange__c" data-field="InvolveCurrencyExchange__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.showSubSubquestions}>
                    <div class="question required-label">
                        Do you act as an Agent? 
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input-field field-name="Involve_Curency_Exchange_Agent__c" data-field="Involve_Curency_Exchange_Agent__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.subsubquestions.Involve_Curency_Exchange_Agent__c.parent} data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.subsubquestions.Involve_Curency_Exchange_Agent__c.grandParent} variant="label-hidden" onchange={handleSubSubTextChange} value={questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.subsubquestions.Involve_Curency_Exchange_Agent__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.showSubSubquestions}>
                    <div class="question required-label">
                        Do you act as a Principal? 
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input-field field-name="Involve_Curency_Exchange_Principal__c" data-field="Involve_Curency_Exchange_Principal__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.subsubquestions.Involve_Curency_Exchange_Principal__c.parent} data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.subsubquestions.Involve_Curency_Exchange_Principal__c.grandParent} variant="label-hidden" onchange={handleSubSubTextChange} value={questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.subsubquestions.Involve_Curency_Exchange_Principal__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="question">
                    Cash checks in amounts greater than $1,000 for any one person in any one day
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveCashChecks__c" data-field="InvolveCashChecks__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveCashChecks__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Money_Service_Business__c.subquestions.InvolveCashChecks__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveCashChecks__c.showSubSubquestions}>
                    <div class="question required-label">
                        What type of checks does your business cash?
                    </div>
                    <div class="input-multi-picklist">
                        <div>
                            <lightning-input-field field-name="InvolveCheckTypes__c" data-field="InvolveCheckTypes__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveCashChecks__c.subsubquestions.InvolveCheckTypes__c.parent} data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveCashChecks__c.subsubquestions.InvolveCheckTypes__c.grandParent} variant="label-hidden" onchange={handleSubSubPicklistChange} value={questions.Money_Service_Business__c.subquestions.InvolveCashChecks__c.subsubquestions.InvolveCheckTypes__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="question">
                    Issue or sell money orders in amounts greater than $1,000 to any one person in any one day
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveMoneyOrders__c" data-field="InvolveMoneyOrders__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.showSubSubquestions}>
                    <div class="question required-label">
                        Do you act as an Agent? 
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input-field field-name="Involve_Money_Orders_Agent__c" data-field="Involve_Money_Orders_Agent__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.subsubquestions.Involve_Money_Orders_Agent__c.parent} data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.subsubquestions.Involve_Money_Orders_Agent__c.grandParent} variant="label-hidden" onchange={handleSubSubTextChange} value={questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.subsubquestions.Involve_Money_Orders_Agent__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.showSubSubquestions}>
                    <div class="question required-label">
                        Do you act as a Principal? 
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input-field field-name="Involve_Money_Orders_Principal__c" data-field="Involve_Money_Orders_Principal__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.subsubquestions.Involve_Money_Orders_Principal__c.parent} data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.subsubquestions.Involve_Money_Orders_Principal__c.grandParent} variant="label-hidden" onchange={handleSubSubTextChange} value={questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.subsubquestions.Involve_Money_Orders_Principal__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="question">
                    Transmit money on your customer's behalf electronically from one location to another
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveTransmitMoney__c" data-field="InvolveTransmitMoney__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.showSubSubquestions}>
                    <div class="question required-label">
                        Do you act as an Agent? 
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input-field field-name="Transmit_Money_Agent__c" data-field="Transmit_Money_Agent__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.Transmit_Money_Agent__c.parent} data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.Transmit_Money_Agent__c.grandParent} variant="label-hidden" onchange={handleSubSubTextChange} value={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.Transmit_Money_Agent__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.showSubSubquestions}>
                    <div class="question required-label">
                        Do you act as a Principal? 
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input-field field-name="Transmit_Money_Principal__c" data-field="Transmit_Money_Principal__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.Transmit_Money_Principal__c.parent} data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.Transmit_Money_Principal__c.grandParent} variant="label-hidden" onchange={handleSubSubTextChange} value={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.Transmit_Money_Principal__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.showSubSubquestions}>
                    <div class="question required-label">
                        Will you transmit to non-US locations?
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input
                                type="radio"
                                name="TransmitMoneyNonUsLocations__c"
                                value="Yes"
                                label="Yes"
                                data-parent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.parent}
                                data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.grandParent}
                                checked={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.yesChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                        <div>
                            <lightning-input
                                type="radio"
                                name="TransmitMoneyNonUsLocations__c"
                                value="No"
                                label="No"
                                data-parent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.parent}
                                data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.grandParent}
                                checked={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.noChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.showSubSubquestions}>
                    <div class="sub-sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.showSubSubSubquestions}>
                        <div class="question required-label">
                            To which foreign countries will transactions be sent?
                        </div>
                        <div class="input-multi-picklist">
                            <div>
                                <lightning-input-field field-name="TransmitForeignCountriesText__c" 
                                data-field="TransmitForeignCountriesText__c" 
                                data-parent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.subsubsubquestions.TransmitForeignCountriesText__c.parent}
                                data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.subsubsubquestions.TransmitForeignCountriesText__c.grandParent}
                                data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.subsubsubquestions.TransmitForeignCountriesText__c.greatgrandParent}
                                variant="label-hidden" 
                                onchange={handleSubSubSubPicklistChange} 
                                value={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.subsubsubquestions.TransmitForeignCountriesText__c.value}
                                >
                            </lightning-input-field>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.showSubSubquestions}>
                    <div class="question required-label">
                        What type of money will be transmitted?
                    </div>
                    <div class="input-multi-picklist">
                        <div>
                            <lightning-input-field field-name="TransmitMoneyTypes__c" data-field="TransmitMoneyTypes__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyTypes__c.parent} data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyTypes__c.grandParent} variant="label-hidden" onchange={handleSubSubPicklistChange} value={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyTypes__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.showSubSubquestions}>
                    <div class="question required-label">
                        Does your business involve convertible virtual currency (CVC)?
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input
                                type="radio"
                                name="TransmitMoneyCvc__c"
                                value="Yes"
                                label="Yes"
                                data-parent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.parent}
                                data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.grandParent}
                                checked={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.yesChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                        <div>
                            <lightning-input
                                type="radio"
                                name="TransmitMoneyCvc__c"
                                value="No"
                                label="No"
                                data-parent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.parent}
                                data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.grandParent}
                                checked={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.noChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.showSubSubquestions}>
                    <div class="sub-sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.showSubSubSubquestions}>
                        <div class="question required-label">
                            Which CVC-related activities does your business include?
                        </div>
                        <div class="input-multi-picklist">
                            <div>
                                <lightning-input-field field-name="TransmitMoneyActivities__c" 
                                data-field="TransmitMoneyActivities__c" 
                                data-parent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.subsubsubquestions.TransmitMoneyActivities__c.parent}
                                data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.subsubsubquestions.TransmitMoneyActivities__c.grandParent}
                                data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.subsubsubquestions.TransmitMoneyActivities__c.greatgrandParent}
                                variant="label-hidden" 
                                onchange={handleSubSubSubPicklistChange} 
                                value={questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.subsubsubquestions.TransmitMoneyActivities__c.value}
                                >
                            </lightning-input-field>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-question" if:true={questions.Money_Service_Business__c.showSubquestions}>
                    <div class="question">
                        Administer or exchange virtual currency
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input-field field-name="InvolveVirtualCurrency__c" data-field="InvolveVirtualCurrency__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveVirtualCurrency__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Money_Service_Business__c.subquestions.InvolveVirtualCurrency__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
                <div class="sub-question" if:true={questions.Money_Service_Business__c.showSubquestions}>
                    <div class="question">
                        Provide or sell prepaid access to funds, such as gift cards or other devices used to transfer value
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input-field field-name="InvolveGiftCards__c" data-field="InvolveGiftCards__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
            </div>
           <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.showSubSubquestions}>
                    <div class="question required-label">
                        Do you act as an Agent? 
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input-field field-name="Gift_Card_Agent__c" data-field="Gift_Card_Agent__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.Gift_Card_Agent__c.parent} data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.Gift_Card_Agent__c.grandParent} variant="label-hidden" onchange={handleSubSubTextChange} value={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.Gift_Card_Agent__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.showSubSubquestions}>
                    <div class="question required-label">
                        Do you act as a Principal? 
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input-field field-name="Gift_Card_Principal__c" data-field="Gift_Card_Principal__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.Gift_Card_Principal__c.parent} data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.Gift_Card_Principal__c.grandParent} variant="label-hidden" onchange={handleSubSubTextChange} value={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.Gift_Card_Principal__c.value}></lightning-input-field>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.showSubSubquestions}>
                    <div class="question ques-width required-label">
                        Do you sell non-network-branded prepaid access to funds (for example, store gift cards) that exceed $2,000 maximum value per device during any one day?
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input
                                type="radio"
                                name="GiftCardExceedDailyMax__c"
                                value="Yes"
                                label="Yes"
                                data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.parent}
                                data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.grandParent}
                                checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.yesChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                        <div>
                            <lightning-input
                                type="radio"
                                name="GiftCardExceedDailyMax__c"
                                value="No"
                                label="No"
                                data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.parent}
                                data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.grandParent}
                                checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.noChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.showSubSubquestions}>
                    <div class="sub-sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.showSubSubSubquestions}>
                        <div class="question required-label">
                            Does access to funds require an activation process that includes customer identification?
                        </div>
                        <div class="input">
                            <div>
                                <lightning-input
                                    type="radio"
                                    name="GiftCardActivationProcess__c"
                                    value="Yes"
                                    label="Yes"
                                    data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.parent}
                                    data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.grandParent}
                                    data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.greatgrandParent}
                                    checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.yesChecked}
                                    onchange={handleSubSubSubRadioChange}
                                >
                                </lightning-input>
                            </div>
                            <div>
                                <lightning-input
                                    type="radio"
                                    name="GiftCardActivationProcess__c"
                                    value="No"
                                    label="No"
                                    data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.parent}
                                    data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.grandParent}
                                    data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.greatgrandParent}
                                    checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.noChecked}
                                    onchange={handleSubSubSubRadioChange}
                                >
                                </lightning-input>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.showSubSubquestions}>
                    <div class="question ques-width required-label">
                        Do you sell network-branded prepaid access to funds (for example, gift cards that use the Mastercard or Visa networks)
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input
                                type="radio"
                                name="GiftCardNetworkBranded__c"
                                value="Yes"
                                label="Yes"
                                data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.parent}
                                data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.grandParent}
                                checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.yesChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                        <div>
                            <lightning-input
                                type="radio"
                                name="GiftCardNetworkBranded__c"
                                value="No"
                                label="No"
                                data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.parent}
                                data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.grandParent}
                                checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.noChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.showSubSubquestions}>
                    <div class="sub-sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.showSubSubSubquestions}>
                        <div class="question required-label">
                            Do devices permit access to funds that exceed $1,000 maximum value per device during any one day
                        </div>
                        <div class="input">
                            <div>
                                <lightning-input
                                    type="radio"
                                    name="GiftCardAccessDailyMax__c"
                                    value="Yes"
                                    label="Yes"
                                    data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.parent}
                                    data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.grandParent}
                                    data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.greatgrandParent}
                                    checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.yesChecked}
                                    onchange={handleSubSubSubRadioChange}
                                >
                                </lightning-input>
                            </div>
                            <div>
                                <lightning-input
                                    type="radio"
                                    name="GiftCardAccessDailyMax__c"
                                    value="No"
                                    label="No"
                                    data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.parent}
                                    data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.grandParent}
                                    data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.greatgrandParent}
                                    checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.noChecked}
                                    onchange={handleSubSubSubRadioChange}
                                >
                                </lightning-input>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.showSubSubquestions}>
                    <div class="sub-sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.showSubSubSubquestions}>
                        <div class="sub-sub-sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestions}>
                            <div class="question required-label">
                                Can devices be reloaded from non-depository sources?
                            </div>
                            <div class="input">
                                <div>
                                    <lightning-input
                                        type="radio"
                                        name="GiftCardReloaded__c"
                                        value="Yes"
                                        label="Yes"
                                        data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.parent}
                                        data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.grandParent}
                                        data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.greatgrandParent}
                                        data-greatgreatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.greatGreategrandParent}
                                        checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.yesChecked}
                                        onchange={handleSubSubSubSubRadioChange}
                                    >
                                    </lightning-input>
                                </div>
                                <div>
                                    <lightning-input
                                        type="radio"
                                        name="GiftCardReloaded__c"
                                        value="No"
                                        label="No"
                                        data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.parent}
                                        data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.grandParent}
                                        data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.greatgrandParent}
                                        data-greatgreatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.greatGreategrandParent}
                                        checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.noChecked}
                                        onchange={handleSubSubSubSubRadioChange}
                                    >
                                    </lightning-input>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.showSubSubquestions}>
                    <div class="sub-sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.showSubSubSubquestions}>
                        <div class="sub-sub-sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestions}>
                            <div class="question required-label">
                                Can devices be used to transfer funds from one person to another?
                            </div>
                            <div class="input">
                                <div>
                                    <lightning-input
                                        type="radio"
                                        name="GiftCardTransferFunds__c"
                                        value="Yes"
                                        label="Yes"
                                        data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.parent}
                                        data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.grandParent}
                                        data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.greatgrandParent}
                                        data-greatgreatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.greatGreategrandParent}
                                        checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.yesChecked}
                                        onchange={handleSubSubSubSubRadioChange}
                                    >
                                    </lightning-input>
                                </div>
                                <div>
                                    <lightning-input
                                        type="radio"
                                        name="GiftCardTransferFunds__c"
                                        value="No"
                                        label="No"
                                        data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.parent}
                                        data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.grandParent}
                                        data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.greatgrandParent}
                                        data-greatgreatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.greatGreategrandParent}
                                        checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.noChecked}
                                        onchange={handleSubSubSubSubRadioChange}
                                    >
                                    </lightning-input>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.showSubSubquestions}>
                    <div class="sub-sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.showSubSubSubquestions}>
                        <div class="sub-sub-sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestionsYesNo}>
                            <div class="question required-label">
                                Can devices be used to transmit funds internationally?
                            </div>
                            <div class="input">
                                <div>
                                    <lightning-input
                                        type="radio"
                                        name="GiftCardTransferFundsInternationally__c"
                                        value="Yes"
                                        label="Yes"
                                        data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.parent}
                                        data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.grandParent}
                                        data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.greatgrandParent}
                                        data-greatgreatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.greatGreategrandParent}
                                        checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.yesChecked}
                                        onchange={handleSubSubSubSubRadioChange}
                                    >
                                    </lightning-input>
                                </div>
                                <div>
                                    <lightning-input
                                        type="radio"
                                        name="GiftCardTransferFundsInternationally__c"
                                        value="No"
                                        label="No"
                                        data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.parent}
                                        data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.grandParent}
                                        data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.greatgrandParent}
                                        data-greatgreatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.greatGreategrandParent}
                                        checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.noChecked}
                                        onchange={handleSubSubSubSubRadioChange}
                                    >
                                    </lightning-input>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.showSubSubquestions}>
                    <div class="sub-sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.showSubSubSubquestions}>
                        <div class="sub-sub-sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestionsYesNo}>
                            <div class="question required-label">
                                Does access to funds require an activation process that includes customer identification?
                            </div>
                            <div class="input">
                                <div>
                                    <lightning-input
                                        type="radio"
                                        name="GiftCardRequireCustomerId__c"
                                        value="Yes"
                                        label="Yes"
                                        data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.parent}
                                        data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.grandParent}
                                        data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.greatgrandParent}
                                        data-greatgreatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.greatGreategrandParent}
                                        checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.yesChecked}
                                        onchange={handleSubSubSubSubRadioChange}
                                    >
                                    </lightning-input>
                                </div>
                                <div>
                                    <lightning-input
                                        type="radio"
                                        name="GiftCardRequireCustomerId__c"
                                        value="No"
                                        label="No"
                                        data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.parent}
                                        data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.grandParent}
                                        data-greatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.greatgrandParent}
                                        data-greatgreatgrandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.greatGreategrandParent}
                                        checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.noChecked}
                                        onchange={handleSubSubSubSubRadioChange}
                                    >
                                    </lightning-input>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-sub-question" if:true={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.showSubSubquestions}>
                    <div class="question ques-width required-label">
                        Are there policies and procedures in place to reasonably prevent the sale of more than $10,000 of any type of prepaid access to any one person during any one day?
                    </div>
                    <div class="input">
                        <div>
                            <lightning-input
                                type="radio"
                                name="GiftCardPreventSales__c"
                                value="Yes"
                                label="Yes"
                                data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardPreventSales__c.parent}
                                data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardPreventSales__c.grandParent}
                                checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardPreventSales__c.yesChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                        <div>
                            <lightning-input
                                type="radio"
                                name="GiftCardPreventSales__c"
                                value="No"
                                label="No"
                                data-parent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardPreventSales__c.parent}
                                data-grandparent={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardPreventSales__c.grandParent}
                                checked={questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardPreventSales__c.noChecked}
                                onchange={handleSubSubRadioChange}
                            >
                            </lightning-input>
                        </div>
                    </div>
                </div>
            </div>
            <div if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="sub-question">
                    (If you selected any of the options above except "None of the above", complete the following.)
                </div>
            </div>
            <div class="sub-question" if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="question required-label">
                    Do you have a documented BSA/AML program?
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="GiftCardBsaAmlProg__c"
                            value="Yes"
                            label="Yes"
                            data-parent={questions.Money_Service_Business__c.subquestions.GiftCardBsaAmlProg__c.parent}
                            checked={questions.Money_Service_Business__c.subquestions.GiftCardBsaAmlProg__c.yesChecked}
                            onchange={handleSubRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="GiftCardBsaAmlProg__c"
                            value="No"
                            label="No"
                            data-parent={questions.Money_Service_Business__c.subquestions.GiftCardBsaAmlProg__c.parent}
                            checked={questions.Money_Service_Business__c.subquestions.GiftCardBsaAmlProg__c.noChecked}
                            onchange={handleSubRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
            <div class="sub-question" if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="question required-label">
                    Are you registered with FinCEN and (if required) by the State?
                </div>
                <div class="input">
                    <div>
                        <lightning-input
                            type="radio"
                            name="RegisteredFinCen__c"
                            value="Yes"
                            label="Yes"
                            data-parent={questions.Money_Service_Business__c.subquestions.RegisteredFinCen__c.parent}
                            checked={questions.Money_Service_Business__c.subquestions.RegisteredFinCen__c.yesChecked}
                            onchange={handleSubRadioChange}
                        >
                        </lightning-input>
                    </div>
                    <div>
                        <lightning-input
                            type="radio"
                            name="RegisteredFinCen__c"
                            value="No"
                            label="No"
                            data-parent={questions.Money_Service_Business__c.subquestions.RegisteredFinCen__c.parent}
                            checked={questions.Money_Service_Business__c.subquestions.RegisteredFinCen__c.noChecked}
                            onchange={handleSubRadioChange}
                        >
                        </lightning-input>
                    </div>
                </div>
            </div>
             <div class="sub-question" if:true={questions.Money_Service_Business__c.showSubquestions}>
                <div class="question">
                    None of the above
                </div>
                <div class="input">
                    <div>
                        <lightning-input-field field-name="InvolveNone__c" data-field="InvolveNone__c" data-parent={questions.Money_Service_Business__c.subquestions.InvolveNone__c.parent} variant="label-hidden" onchange={handleCheckboxChange} value={questions.Money_Service_Business__c.subquestions.InvolveNone__c.value}></lightning-input-field>
                    </div>
                </div>
            </div>
        </lightning-record-edit-form>
    </lightning-card>
    <div class="button-container">
        <button class="previous-btn" onclick={handlePreviousPage}>Previous</button>
        <button class="save-continue-btn" onclick={handleNextPage}>Save and Continue</button>
    </div>
</template>