<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Branch_Update</name>
        <label>Branch Update</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>daoApp.Branch_Review_Completed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_DAO_Application</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Compliance_Update</name>
        <label>Compliance Update</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>daoApp.BSAComplianceReviewCompleted__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_DAO_Application</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Document_Update</name>
        <label>Document Update</label>
        <locationX>578</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>daoApp.DocumentReviewCompleted__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_DAO_Application</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Fraud_Update</name>
        <label>Fraud Update</label>
        <locationX>842</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>daoApp.FraudReviewCompleted__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_DAO_Application</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Stage_Update</name>
        <label>Stage Update</label>
        <locationX>578</locationX>
        <locationY>134</locationY>
        <assignmentItems>
            <assignToReference>daoApp.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>daoApp.DAO_Application_Status__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ApplicationStatus</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Type_of_Review</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Type_of_Review</name>
        <label>Type of Review</label>
        <locationX>578</locationX>
        <locationY>242</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Branch</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ApprovalType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Branch</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Branch_Update</targetReference>
            </connector>
            <label>Branch</label>
        </rules>
        <rules>
            <name>Compliance</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ApprovalType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>BSA Compliance</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Compliance_Update</targetReference>
            </connector>
            <label>Compliance</label>
        </rules>
        <rules>
            <name>Document</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ApprovalType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Document Review</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Document_Update</targetReference>
            </connector>
            <label>Document</label>
        </rules>
        <rules>
            <name>Fraud</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ApprovalType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fraud</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Fraud_Update</targetReference>
            </connector>
            <label>Fraud</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>[workflow] Update Application Subflow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[workflow] Update Application Subflow</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Update_DAO_Application</name>
        <label>Update DAO Application</label>
        <locationX>578</locationX>
        <locationY>542</locationY>
        <inputReference>daoApp</inputReference>
    </recordUpdates>
    <start>
        <locationX>452</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Stage_Update</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>ApplicationStatus</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ApprovalType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>daoApp</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>DAO_Application__c</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
