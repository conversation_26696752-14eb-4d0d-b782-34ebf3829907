<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>GroupClassId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Status</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ContactId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>GroupCensusId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Personal Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>FirstName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Gender</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>JoinDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Address</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AnnualEligibleSalary</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Email</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Lastname</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SmokerStatus</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FullTimeEquivalent</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PhoneNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Birthdate</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Other Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RelationshipToPrimaryMember</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsOptOutAllPlans</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsPortalUser</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MemberType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PrimaryMemberCategory</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OptOutPlanTypes</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DependentCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PrimaryGroupCensusMemberId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
