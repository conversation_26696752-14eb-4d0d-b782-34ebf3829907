<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Approve_Assignment</name>
        <label>Approve Assignment</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>ApprovalDecisionValue</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ApproveChoice</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>workflow_Update_Application_Subflow_Flow_1</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Decline_Assignment</name>
        <label>Decline Assignment</label>
        <locationX>314</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>ApprovalDecisionValue</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>DeclineChoice</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>ApplicationStatus</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Declined</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>workflow_Update_Application_Subflow_Flow_1</targetReference>
        </connector>
    </assignments>
    <choices>
        <description>Approve the request</description>
        <name>ApproveChoice</name>
        <choiceText>Approve</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Approve</stringValue>
        </value>
    </choices>
    <choices>
        <description>Decline the request</description>
        <name>DeclineChoice</name>
        <choiceText>Decline</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Decline</stringValue>
        </value>
    </choices>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>Approval_Assignment</name>
        <label>Approval Assignment</label>
        <locationX>314</locationX>
        <locationY>242</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>BranchApprovalDecisionRadioButtons</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>ApproveChoice</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Approve_Assignment</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
        <rules>
            <name>No</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>BranchApprovalDecisionRadioButtons</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>DeclineChoice</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Decline_Assignment</targetReference>
            </connector>
            <label>No</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>[workflow] Doc Review Approval {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[workflow] Doc Review Approval</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Approval_Log</name>
        <label>Approval Log</label>
        <locationX>314</locationX>
        <locationY>650</locationY>
        <inputAssignments>
            <field>ApprovalType__c</field>
            <value>
                <elementReference>ApprovalType</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Approver__c</field>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Comments__c</field>
            <value>
                <elementReference>ApprovalDecisionComments</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>DAO_Application__c</field>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>DateTime__c</field>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Decision__c</field>
            <value>
                <elementReference>ApprovalDecisionValue</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>ApprovalType</elementReference>
            </value>
        </inputAssignments>
        <object>DAO_Approvals__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <screens>
        <description>Allows the flow user to approve or reject an approval request, and add comments to the decision.</description>
        <name>ScreenEvaluateApproval</name>
        <label>Evaluate Approval Request</label>
        <locationX>314</locationX>
        <locationY>134</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Approval_Assignment</targetReference>
        </connector>
        <fields>
            <name>BranchApprovalDecisionRadioButtons</name>
            <choiceReferences>ApproveChoice</choiceReferences>
            <choiceReferences>DeclineChoice</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Branch Approval Decision</fieldText>
            <fieldType>RadioButtons</fieldType>
            <helpText>&lt;p&gt;Displays radio buttons to approve or decline a request. You can also add comments for your decision.&amp;nbsp;&lt;/p&gt;</helpText>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>ApprovalDecisionComments</name>
            <fieldText>Decision Comments</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <helpText>&lt;p&gt;&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 10pt; font-family: Arial;&quot;&gt;Enter comments on your decision&lt;/span&gt;&lt;/p&gt;</helpText>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <sourceTemplate>standard_approvals__EvaluateApproval</sourceTemplate>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>ScreenEvaluateApproval</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>workflow_Update_Application_Subflow_Flow_1</name>
        <label>[workflow] Update Application Subflow Flow 1</label>
        <locationX>314</locationX>
        <locationY>542</locationY>
        <connector>
            <targetReference>Approval_Log</targetReference>
        </connector>
        <flowName>workflow_Update_Application_Subflow</flowName>
        <inputAssignments>
            <name>ApplicationStatus</name>
            <value>
                <elementReference>ApplicationStatus</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>ApprovalType</name>
            <value>
                <elementReference>ApprovalType</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>recordId</name>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <variables>
        <name>ApplicationStatus</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>$Label.EvaluateApprovalRequestFlow.OutputCommentDescription</description>
        <name>approvalComments</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <value>
            <elementReference>ApprovalDecisionComments</elementReference>
        </value>
    </variables>
    <variables>
        <description>$Label.EvaluateApprovalRequestFlow.OutputDecisionDescription</description>
        <name>approvalDecision</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <value>
            <elementReference>BranchApprovalDecisionRadioButtons</elementReference>
        </value>
    </variables>
    <variables>
        <name>ApprovalDecisionValue</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ApprovalType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>Approved</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>BranchQueueDevName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>PreferredBranch</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
