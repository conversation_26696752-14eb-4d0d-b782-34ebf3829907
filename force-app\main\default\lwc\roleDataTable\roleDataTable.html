<template>
    <lightning-card>
        <div class="slds-grid slds-wrap">
            <!-- First grid for Add Role section -->
            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-3 slds-p-around_medium">
                <div class="slds-card slds-card_boundary">
                    <div class="slds-card__header">
                        <h2 class="slds-text-heading_medium">Add Role</h2>
                    </div>
                    <div class="slds-card__body slds-p-around_small">
                        <lightning-input label="First Name" value={firstName} onchange={handleFieldChange} data-field="firstName" required></lightning-input>
                        <lightning-input label="Middle Name" value={middleName} onchange={handleFieldChange} data-field="middleName"></lightning-input>
                        <lightning-input label="Last Name" value={lastName} onchange={handleFieldChange} data-field="lastName" required></lightning-input>
                        <lightning-input label="Email" type="email" value={email} onchange={handleFieldChange} data-field="email" required></lightning-input>
                        <!-- <lightning-input label="Ownership Percentage" value={ownership} onchange={handleFieldChange} data-field="ownership" type= "Percent" required></lightning-input> -->
                        <lightning-radio-group label="Role:" options={roleOptions} value={selectedRoleType} onchange={handleRoleTypeChange} required title="sfdfsfd"></lightning-radio-group>
                        <div style="margin-left: 41%;margin-top: -67px;padding-bottom: 3%;">
                          <div><lightning-helptext content="Controlling Individual* - The Owners, Officers, Directors, or other individuals that have the ability to enter into a contract on behalf of the business. Examples: CEO,CFO,Treasurer,Manager,Owner,etc."></lightning-helptext></div>
                          <div style="margin-top: 1px;margin-left: -21px;"><lightning-helptext content="Beneficial Owner (Shareholder)* - A person who owns 25% or more of the business. Example: Investor,Partners,Owners,Managers,etc."></lightning-helptext></div>
                          <div style="margin-top: -1px;margin-left: -18px;"><lightning-helptext content="Authorized Signer - All persons who are authorized to conduct transactions on the account and appointed by the Controlling Individual(s) and are optional for accounts. Examples: Secretary,Bookkeper,Assistant, etc."></lightning-helptext></div>
                          </div>
                          <button onclick={handleAddRole} class="addRoleButton">Add Role</button>
                        <!-- <lightning-button label="Add Role"  variant="brand" ="slds-m-top_small " ></lightning-button> -->
                    </div>
                </div>
            </div>
            
            <!-- Table section -->
            <div class="slds-col slds-size_1-of-1 slds-medium-size_2-of-3">
                <table class="slds-table slds-table_bordered slds-table_cell-buffer" style="background-color: #ebf1e9;Font-size: 13px; !important">
                    <thead>
                        <tr class="slds-line-height_reset heading">
                            <th scope="col" colspan="8" ><center>Business Roles</center></th>
                        </tr>
                        <tr class="slds-line-height_reset sub-heading">
                            <th scope="col" style="background-color: #d5e3cf !important">Update</th>
                            <th scope="col" style="background-color: #d5e3cf !important">Name</th>
                            <th scope="col" style="background-color: #d5e3cf !important">Role</th>
                            <th scope="col" style="background-color: #d5e3cf !important">Beneficial Owner %</th>
                            <th scope="col" style="background-color: #d5e3cf !important">Email</th>
                            <th scope="col" style="background-color: #d5e3cf !important">ID Attached</th>
                            <th scope="col" style="background-color: #d5e3cf !important">Completed</th>
                            <th scope="col" style="background-color: #d5e3cf !important">Applicant</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template for:each={roles} for:item="role" for:index="index">
                            <tr key={role.Id} class="evenodd">
                                <td>
                                    <!--<template if:true={role.isEditing}>
                                        <lightning-button-icon icon-name="utility:save" alternative-text="Save" onclick={handleSave} data-id={role.Id}></lightning-button-icon>
                                    </template>
                                    <template if:false={role.isEditing}>
                                        <lightning-button-icon icon-name="utility:edit" alternative-text="Edit" onclick={handleEdit} data-id={role.Id}></lightning-button-icon>
                                    </template>-->
                                    <lightning-button-icon icon-name="utility:edit" alternative-text="Update Role" onclick={handleOpenRole} data-id={role.Id}></lightning-button-icon>
                                    <lightning-button-icon icon-name="utility:email" alternative-text="Send link to complete role information" onclick={handleSendEmail} data-id={role.Id}></lightning-button-icon>
                                    <lightning-button-icon icon-name="utility:delete" alternative-text="Delete" onclick={handleDelete} data-id={role.Id} disabled={role.disableButton}></lightning-button-icon>
                                </td>
                                <td>
                                    <template if:true={role.isEditing}>
                                        <lightning-input data-id={role.Id} value={role.Name} onchange={handleInputChange} data-field="Name"></lightning-input>
                                    </template>
                                    <template if:false={role.isEditing}>{role.Name}</template>
                                </td>
                                <td>
                                    <template if:true={role.isEditing}>
                                        <lightning-combobox
                                            name="roleType"
                                            label="Role"
                                            value={role.Individual_Role__c}
                                            placeholder="Select Role"
                                            options={roleOptions}
                                            data-id={role.Id}
                                            data-field="Individual_Role__c"
                                            onchange={handleInputChange}>
                                        </lightning-combobox>
                                    </template>
                                    <template if:false={role.isEditing}>{role.Individual_Role__c}</template>
                                </td>
                                <td>
                                    <template if:true={role.isEditing}>
                                        <lightning-input type="number" data-id={role.Id} value={role.BusinessOwned} onchange={handleInputChange} data-field="Business_Owned__c"></lightning-input>
                                    </template>
                                    <template if:false={role.isEditing} >{role.BusinessOwned}</template>
                                </td>
                                <td>
                                    <template if:true={role.isEditing}>
                                        <lightning-input type="text" data-id={role.Id} value={role.Email__c} onchange={handleInputChange} data-field="Email__c"></lightning-input>
                                    </template>
                                    <template if:false={role.isEditing}>{role.Email__c}</template>
                                </td>
                                <td>
                                    <template if:true={role.Is_ID_Attached__c}>
                                        <lightning-input type="checkbox" checked="true" data-id={role.Id} onchange={handleInputChange} data-field="id_attached__c" disabled= "true"></lightning-input>
                                    </template>
                                    <template if:false={role.Is_ID_Attached__c}><lightning-input type="checkbox" data-id={role.Id} onchange={handleInputChange} data-field="id_attached__c" disabled= "true"></lightning-input></template>
                                </td>
                                <td>
                                    <template lwc:if={role.IsCompleted__c}>
                                        <lightning-input type="checkbox"  checked="true"  data-id={role.Id} onchange={handleInputChange} disabled= "true" data-field="Completed__c"></lightning-input>
                                        
                                    </template>
                                    <template lwc:else><lightning-input type="checkbox"  data-id={role.Id} onchange={handleInputChange} data-field="Completed__c" disabled= "true"></lightning-input></template>
                                </td>
                                <td>
                                    <lightning-input type="checkbox" checked="true" disabled={role.disableButton} if:true={role.disableButton}></lightning-input>
                                    <lightning-input type="checkbox" disabled= "true" if:false={role.disableButton}></lightning-input>
                                </td>

                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
    </lightning-card>
</template>