<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <environments>Default</environments>
    <interviewLabel>DAO Business Approval Process {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[workflow] DAO Business Approval Process</label>
    <orchestratedStages>
        <name>Fraud_Review</name>
        <label>Fraud Review</label>
        <locationX>176</locationX>
        <locationY>395</locationY>
        <exitConditionLogic>and</exitConditionLogic>
        <stageSteps>
            <name>Fraud_Team_Approval</name>
            <actionName>workflow_Branch_Approval</actionName>
            <actionType>stepInteractive</actionType>
            <assignees>
                <assignee>
                    <stringValue>Fraud_Team</stringValue>
                </assignee>
                <assigneeType>Queue</assigneeType>
            </assignees>
            <canAssigneeEdit>false</canAssigneeEdit>
            <entryConditionLogic>and</entryConditionLogic>
            <entryConditions>
                <conditionType>EntryCondition</conditionType>
                <leftValueReference>$Record.Fraud_Review_Needed__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </entryConditions>
            <exitConditionLogic>and</exitConditionLogic>
            <inputParameters>
                <name>ApprovalType</name>
                <value>
                    <stringValue>Fraud</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>$Record.Id</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>ActionInput__RecordId</name>
                <value>
                    <elementReference>$Record.Id</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>ActionInput__CustomEmailSubject</name>
            </inputParameters>
            <inputParameters>
                <name>ActionInput__CustomEmailBody</name>
            </inputParameters>
            <label>Fraud Team Approval</label>
            <requiresAsyncProcessing>false</requiresAsyncProcessing>
            <runAsUser>false</runAsUser>
            <shouldLock>false</shouldLock>
            <stepSubtype>InteractiveStep</stepSubtype>
        </stageSteps>
    </orchestratedStages>
    <orchestratedStages>
        <name>Standard_Review</name>
        <label>Standard Review</label>
        <locationX>176</locationX>
        <locationY>287</locationY>
        <connector>
            <targetReference>Fraud_Review</targetReference>
        </connector>
        <exitConditionLogic>and</exitConditionLogic>
        <stageSteps>
            <description>This gets the queue dev name for the Branch Approval</description>
            <name>Get_Queue_Dev_Name</name>
            <actionName>workflow_Get_Queue</actionName>
            <actionType>stepBackground</actionType>
            <canAssigneeEdit>false</canAssigneeEdit>
            <entryConditionLogic>and</entryConditionLogic>
            <exitConditionLogic>and</exitConditionLogic>
            <inputParameters>
                <name>PreferredBranch</name>
                <value>
                    <elementReference>$Record.Preferred_Branch__c</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>sObjectType</name>
                <value>
                    <stringValue>DAO_Application__c</stringValue>
                </value>
            </inputParameters>
            <label>Get Queue Dev Name</label>
            <requiresAsyncProcessing>false</requiresAsyncProcessing>
            <runAsUser>false</runAsUser>
            <shouldLock>false</shouldLock>
            <stepSubtype>BackgroundStep</stepSubtype>
        </stageSteps>
        <stageSteps>
            <name>Branch_Approval</name>
            <actionName>workflow_Branch_Approval</actionName>
            <actionType>stepInteractive</actionType>
            <assignees>
                <assignee>
                    <elementReference>Get_Queue_Dev_Name.Outputs.QueueDevName</elementReference>
                </assignee>
                <assigneeType>Queue</assigneeType>
            </assignees>
            <canAssigneeEdit>false</canAssigneeEdit>
            <entryConditionLogic>and</entryConditionLogic>
            <entryConditions>
                <conditionType>EntryCondition</conditionType>
                <leftValueReference>Get_Queue_Dev_Name.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </entryConditions>
            <exitConditionLogic>and</exitConditionLogic>
            <inputParameters>
                <name>ApprovalType</name>
                <value>
                    <stringValue>Branch</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>PreferredBranch</name>
                <value>
                    <elementReference>$Record.Preferred_Branch__c</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>$Record.Id</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>ActionInput__RecordId</name>
                <value>
                    <elementReference>$Record.Id</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>ActionInput__CustomEmailSubject</name>
            </inputParameters>
            <inputParameters>
                <name>ActionInput__CustomEmailBody</name>
            </inputParameters>
            <label>Branch Approval</label>
            <requiresAsyncProcessing>false</requiresAsyncProcessing>
            <runAsUser>false</runAsUser>
            <shouldLock>false</shouldLock>
            <stepSubtype>InteractiveStep</stepSubtype>
        </stageSteps>
        <stageSteps>
            <name>BSA_Compliance_Approval</name>
            <actionName>workflow_BSA_Compliance_Approval</actionName>
            <actionType>stepInteractive</actionType>
            <assignees>
                <assignee>
                    <stringValue>BSA_Compliance</stringValue>
                </assignee>
                <assigneeType>Queue</assigneeType>
            </assignees>
            <canAssigneeEdit>false</canAssigneeEdit>
            <entryConditionLogic>and</entryConditionLogic>
            <exitConditionLogic>and</exitConditionLogic>
            <inputParameters>
                <name>ApprovalType</name>
                <value>
                    <stringValue>BSA Compliance</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>$Record.Id</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>ActionInput__RecordId</name>
                <value>
                    <elementReference>$Record.Id</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>ActionInput__CustomEmailSubject</name>
            </inputParameters>
            <inputParameters>
                <name>ActionInput__CustomEmailBody</name>
            </inputParameters>
            <label>BSA Compliance Approval</label>
            <requiresAsyncProcessing>false</requiresAsyncProcessing>
            <runAsUser>false</runAsUser>
            <shouldLock>false</shouldLock>
            <stepSubtype>InteractiveStep</stepSubtype>
        </stageSteps>
        <stageSteps>
            <name>Doc_Review_Approval</name>
            <actionName>workflow_Branch_Approval</actionName>
            <actionType>stepInteractive</actionType>
            <assignees>
                <assignee>
                    <stringValue>Business_Document_Review</stringValue>
                </assignee>
                <assigneeType>Queue</assigneeType>
            </assignees>
            <canAssigneeEdit>false</canAssigneeEdit>
            <entryConditionLogic>and</entryConditionLogic>
            <entryConditions>
                <conditionType>EntryCondition</conditionType>
                <leftValueReference>BSA_Compliance_Approval.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Completed</stringValue>
                </rightValue>
            </entryConditions>
            <exitConditionLogic>and</exitConditionLogic>
            <inputParameters>
                <name>ApprovalType</name>
                <value>
                    <stringValue>Document Review</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>$Record.Id</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>ActionInput__RecordId</name>
                <value>
                    <elementReference>$Record.Id</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>ActionInput__CustomEmailSubject</name>
            </inputParameters>
            <inputParameters>
                <name>ActionInput__CustomEmailBody</name>
            </inputParameters>
            <label>Doc Review Approval</label>
            <requiresAsyncProcessing>false</requiresAsyncProcessing>
            <runAsUser>false</runAsUser>
            <shouldLock>false</shouldLock>
            <stepSubtype>InteractiveStep</stepSubtype>
        </stageSteps>
    </orchestratedStages>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Orchestrator</processType>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Standard_Review</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Application_Submitted__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Application_Submitted__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>DAO_Application__c</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
