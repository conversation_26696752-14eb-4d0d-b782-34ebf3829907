<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>PolicyDetails</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PolicyName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PolicyType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EffectiveDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>UniversalPolicyNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PolicyDescription</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OriginalPolicyId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PriorPolicyId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SourceQuoteId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ContractGroupPlanId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>NameInsuredId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>UnderwritingEntityId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ProductId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpirationDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PolicyTerm</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PlanTier</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>IsActive</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ReferencePolicyNumber</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Premiums</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>GrossWrittenPremium</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalStandardAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>StandardPremiumAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>StandardTaxAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>StandardFeeAmount</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PremiumAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TermPremiumAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TermTaxAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TermFeeAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TaxesSurcharges</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>PaymentDetails</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PaidToDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PremiumFrequency</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PaymentDueDate</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>RenewalDetails</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SaleDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RenewalDate</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PreviousRenewalDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DateRenewed</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>AdditionalDetails</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ProducerId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AuditTerm</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>Name</fields>
        <fields>InitiationDate</fields>
        <fields>Status</fields>
        <fields>ClaimReason</fields>
        <relatedList>Claims</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>CoverageName</fields>
        <fields>Category</fields>
        <fields>CategoryGroup</fields>
        <relatedList>InsurancePolicyCoverages</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>PrimaryParticipantAccount</fields>
        <fields>Role</fields>
        <fields>RelationshipToInsured</fields>
        <relatedList>InsurancePolicyParticipants</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>CustomerProperty</fields>
        <relatedList>InsurancePolicyAssets</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>TASK.PRIORITY</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <relatedList>RelatedActivityList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>WorkerClass</fields>
        <fields>Premium</fields>
        <fields>WorkerCount</fields>
        <relatedList>WorkerCompCoverageClasses</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>ActionPlanState</fields>
        <fields>Owner</fields>
        <fields>StartDate</fields>
        <relatedList>ActionPlan</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>PolicyType</fields>
        <fields>PremiumAmount</fields>
        <fields>EffectiveDate</fields>
        <fields>ExpirationDate</fields>
        <relatedList>ChildInsurancePolicies</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
