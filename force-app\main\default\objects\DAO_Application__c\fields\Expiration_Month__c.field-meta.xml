<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Expiration_Month__c</fullName>
    <label>Expiration Month</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>01</fullName>
                <default>false</default>
                <label>01</label>
            </value>
            <value>
                <fullName>02</fullName>
                <default>false</default>
                <label>02</label>
            </value>
            <value>
                <fullName>03</fullName>
                <default>false</default>
                <label>03</label>
            </value>
            <value>
                <fullName>04</fullName>
                <default>false</default>
                <label>04</label>
            </value>
            <value>
                <fullName>05</fullName>
                <default>false</default>
                <label>05</label>
            </value>
            <value>
                <fullName>06</fullName>
                <default>false</default>
                <label>06</label>
            </value>
            <value>
                <fullName>07</fullName>
                <default>false</default>
                <label>07</label>
            </value>
            <value>
                <fullName>08</fullName>
                <default>false</default>
                <label>08</label>
            </value>
            <value>
                <fullName>09</fullName>
                <default>false</default>
                <label>09</label>
            </value>
            <value>
                <fullName>10</fullName>
                <default>false</default>
                <label>10</label>
            </value>
            <value>
                <fullName>11</fullName>
                <default>false</default>
                <label>11</label>
            </value>
            <value>
                <fullName>12</fullName>
                <default>false</default>
                <label>12</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
