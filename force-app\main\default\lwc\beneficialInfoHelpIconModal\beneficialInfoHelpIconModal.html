<template>
    <div class="label-container">
        <div class="label-text">
            {label}
        </div>
        <svg focusable="false" aria-hidden="true" viewBox="0 0 520 520" part="icon" lwc-2l77tt0cac6="" data-key="question_mark" onclick={toggleHelp} class="slds-icon slds-icon-text-default slds-icon_x-small questionMark"><g lwc-2l77tt0cac6=""><path d="M267 428c8 0 15 7 15 15v32c0 8-7 15-15 15h-32c-8 0-15-7-15-15v-32c0-8 7-15 15-15zm15-77c0-21 13-40 31-48h1a142.5 142.5 0 00-54-274c-72 0-132 53-142 122v1c-1 9 6 16 15 16h32c8 0 14-5 15-11v-2c7-37 40-65 79-65 45 0 81 36 81 81 0 21-8 40-21 55l-1 1c-9 10-21 16-33 20-40 14-67 52-67 94v15c0 8 6 14 14 14h32c8 0 16-6 16-15z" lwc-2l77tt0cac6=""></path></g></svg>
    </div>

    <template if:true={showHelp}>
        <div class="help-popup">
            <!-- <p>{helpText}</p> -->
            <lightning-icon
            icon-name="action:close"
            alternative-text="Close"
            size="x-small"
            class="close-icon"
            onclick={toggleHelp}>
        </lightning-icon><br/>

            <p>
                Due to Financial Crimes Enforcement Network (FInCEN) regulations businesses are required
                to identify beneficial owners.</p><br/>
             <p>  A beneficial owner meats at least one of the following criteria: </p><br/>
                
            
            <ul style="list-style: disc;margin-left: 15%;">

                <li><strong>Ownership</strong> - has 25% or more ownership in the business.
                </li>
                <li><strong>Control</strong> - has significant responsibility to control, manage, or direct the business.</li>

                
            </ul>
           <!-- <div class="slds-align_absolute-center">
    <button class="slds-button slds-button_success custom-ok-button" onclick={toggleHelp}>OK</button>
</div> -->
<div class="required-center">
<p> <span class="requiredMark">*</span>Required Field(s)</p>
</div>

<div class="slds-align_absolute-center">
    <button class="slds-button slds-button_success custom-ok-button" onclick={toggleHelp}>OK</button>
</div>

        </div>
    </template>
</template>