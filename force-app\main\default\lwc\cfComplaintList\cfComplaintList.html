<template>
            <div class="slds-grid slds-wrap slds-is-relative ">
            <div style="position: fixed;z-index: 999999;top: 0;right: 0;">
  <omnistudio-toast class="flexcard-toast-element" theme="slds" title="" message="" styletype=""> </omnistudio-toast>
</div>
            <div if:true={showLoader} class="slds-is-absolute vloc-loader_override" style="height: 100%; width: 100%; min-height:50px; background: transparent; z-index: 99;">
  <div>
   <omnistudio-spinner
      variant="brand"
      alternative-text="Loading content..."
      size="medium"
      theme="slds"
      ></omnistudio-spinner>
  </div>
</div>
            <template if:false={hasPermission}>
              You don't have required permissions to view this card.
            </template>
            <template if:true={hasPermission}>
              <template if:true={hasRecords}>
                        <template for:each={_records} for:item="record" for:index="rindex">
                            <omnistudio-flex-card-state if:true={record}  key={record._flex.uniqueKey} data-recordid={record.Id} record={record} data-statue="true"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12 "  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-m-around_xxx-small " style="      
         ">
          <div data-style-id="state0element0" class="slds-col   slds-text-link_reset  slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small slds-scrollable_y slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12  " data-rindex={rindex} style="     border-top: #E5E5E5 0.2px solid;border-right: #E5E5E5 0.2px solid;border-bottom: #E5E5E5 0.2px solid;border-left: #E5E5E5 0.2px solid; 
    border-radius:4px;     " >
    <omnistudio-block data-style-id="state0element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  action='\{"label":"Action","iconName":"standard-default","eventType":"onclick","actionList":[{"key":"1675671675260-u64e7wqym","label":"Action","draggable":false,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-1681365634805","type":"Custom","displayName":"Action","vlocityIcon":"standard-default","targetType":"Web Page","openUrlIn":"New Tab/Window","Web Page":{"targetName":"{caseLink}"}},"actionIndex":0}],"showSpinner":"false"}'  extraclass=" slds-text-link_reset"  theme="slds"  onclick={executeAction} data-element-label="block0" data-action-key="state0element0" onkeydown={executeActionWithKeyboard} tabindex="0"><div class="slds-grid slds-wrap">
          <div data-style-id="state0element0block_element0" class="slds-col   slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12  " data-rindex={rindex} style="      
         " ><omnistudio-output-field data-style-id="state0element0block_element0_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22color:%20#3598db;%22%3E&nbsp;%20&nbsp;%20&nbsp;%7BcaseNumber%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element1" class="slds-col    slds-large-size_5-of-12  slds-medium-size_12-of-12  slds-small-size_12-of-12  slds-size_12-of-12  " data-rindex={rindex} style="      
         " ><omnistudio-output-field data-style-id="state0element0block_element1_child"   card={card}  record={record}  merge-field="%3Cdiv%3E&nbsp;%7Bsubject%7D&nbsp;%20&nbsp;%20&nbsp;%20%3Cstrong%3E&nbsp;%20&nbsp;&nbsp;%3C/strong%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element2" class="slds-col   slds-large-size_2-of-12  slds-medium-size_12-of-12  slds-small-size_12-of-12  slds-size_12-of-12  " data-rindex={rindex} style="      
         " ><omnistudio-output-field data-style-id="state0element0block_element2_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22background-color:%20initial;%20font-size:%200.8125rem;%22%3E&nbsp;%20%7BcreatedDate%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element3" class="slds-col   slds-size_2-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element3_child"   card={card}  record={record}  merge-field="%3Cdiv%3E&nbsp;%20&nbsp;%20&nbsp;%7Bstatus%7D%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element4" class="slds-col    slds-large-size_1-of-12  slds-medium-size_12-of-12  slds-small-size_12-of-12  slds-size_12-of-12  " data-rindex={rindex} style="      
         " ><omnistudio-output-field data-style-id="state0element0block_element4_child"   card={card}  record={record}  merge-field="%3Cdiv%3E&nbsp;%20&nbsp;%20&nbsp;%20&nbsp;%7Bpriority%7D&nbsp;%20&nbsp;%20&nbsp;%20&nbsp;%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div>
        </div>
      </omnistudio-flex-card-state>
                        </template>
                      </template>
                      <template if:false={hasRecords}>
                            <omnistudio-flex-card-state  record={record} data-statue="false"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12 "  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-m-around_xxx-small " style="      
         ">
          <div data-style-id="state0element0" class="slds-col   slds-text-link_reset  slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small slds-scrollable_y slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12  " data-rindex={rindex} style="     border-top: #E5E5E5 0.2px solid;border-right: #E5E5E5 0.2px solid;border-bottom: #E5E5E5 0.2px solid;border-left: #E5E5E5 0.2px solid; 
    border-radius:4px;     " >
    <omnistudio-block data-style-id="state0element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  action='\{"label":"Action","iconName":"standard-default","eventType":"onclick","actionList":[{"key":"1675671675260-u64e7wqym","label":"Action","draggable":false,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-1681365634805","type":"Custom","displayName":"Action","vlocityIcon":"standard-default","targetType":"Web Page","openUrlIn":"New Tab/Window","Web Page":{"targetName":"{caseLink}"}},"actionIndex":0}],"showSpinner":"false"}'  extraclass=" slds-text-link_reset"  theme="slds"  onclick={executeAction} data-element-label="block0" data-action-key="state0element0" onkeydown={executeActionWithKeyboard} tabindex="0"><div class="slds-grid slds-wrap">
          <div data-style-id="state0element0block_element0" class="slds-col   slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12  " data-rindex={rindex} style="      
         " ><omnistudio-output-field data-style-id="state0element0block_element0_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22color:%20#3598db;%22%3E&nbsp;%20&nbsp;%20&nbsp;%7BcaseNumber%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element1" class="slds-col    slds-large-size_5-of-12  slds-medium-size_12-of-12  slds-small-size_12-of-12  slds-size_12-of-12  " data-rindex={rindex} style="      
         " ><omnistudio-output-field data-style-id="state0element0block_element1_child"   card={card}  record={record}  merge-field="%3Cdiv%3E&nbsp;%7Bsubject%7D&nbsp;%20&nbsp;%20&nbsp;%20%3Cstrong%3E&nbsp;%20&nbsp;&nbsp;%3C/strong%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element2" class="slds-col   slds-large-size_2-of-12  slds-medium-size_12-of-12  slds-small-size_12-of-12  slds-size_12-of-12  " data-rindex={rindex} style="      
         " ><omnistudio-output-field data-style-id="state0element0block_element2_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%3Cspan%20style=%22background-color:%20initial;%20font-size:%200.8125rem;%22%3E&nbsp;%20%7BcreatedDate%7D%3C/span%3E%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element3" class="slds-col   slds-size_2-of-12  " data-rindex={rindex} style="" ><omnistudio-output-field data-style-id="state0element0block_element3_child"   card={card}  record={record}  merge-field="%3Cdiv%3E&nbsp;%20&nbsp;%20&nbsp;%7Bstatus%7D%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div><div data-style-id="state0element0block_element4" class="slds-col    slds-large-size_1-of-12  slds-medium-size_12-of-12  slds-small-size_12-of-12  slds-size_12-of-12  " data-rindex={rindex} style="      
         " ><omnistudio-output-field data-style-id="state0element0block_element4_child"   card={card}  record={record}  merge-field="%3Cdiv%3E&nbsp;%20&nbsp;%20&nbsp;%20&nbsp;%7Bpriority%7D&nbsp;%20&nbsp;%20&nbsp;%20&nbsp;%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div>
        </div>
      </omnistudio-flex-card-state>
                      </template>
            </template>
            <template if:true={hasError}>
            {error}
            </template>
            
      <omnistudio-action action-wrapperclass="slds-hide" re-render-flyout class="action-trigger slds-col" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
      
      </omnistudio-action>
  
            <omnistudio-action class="execute-action" re-render-flyout action-wrapperclass="slds-hide" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
            
            </omnistudio-action>
            </div>
          </template>