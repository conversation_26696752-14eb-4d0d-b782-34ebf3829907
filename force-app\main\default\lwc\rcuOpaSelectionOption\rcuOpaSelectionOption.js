import { LightningElement, track } from 'lwc';
import {OmniscriptBaseMixin} from 'omnistudio/omniscriptBaseMixin';

export default class RcuOpaSelectionOption extends OmniscriptBaseMixin(LightningElement) {
@track opaSelected = '';
@track opaSelected1 = true;
@track opaSelected2 = '';
@track selectedOpaValue = null;
@track selectedOpaLabel = null;

connectedCallback() {
    console.log('connectedCallback 123456',this.omniJsonData.selectedOPAValue);
    console.log('connectedCallback selectedLabel',this.omniJsonData.selectedLabel);
    if (!this.omniJsonData.selectedOPAValue || !this.omniJsonData.selectedOPALabel) {
        let OpaOption = {
            selectedValue:this.selectedOpaValue,
            selectedLabel:this.selectedOpaLabel
        };
        this.omniApplyCallResp(OpaOption);
    }
    let selectedType = this.omniJsonData.selectedOPAValue;
    let selectedLabel = this.omniJsonData.selectedOPALabel;
    if(selectedType === 'Overdraft'){
        this.opaSelected = true;
        this.opaSelected1 = false;
        this.opaSelected2 = false;
    }else if(selectedType === 'electronic'){
        this.opaSelected1 = true;
        this.opaSelected = false;
        this.opaSelected2 = false;
    }else if(selectedType === 'No Overdraft'){
        this.opaSelected2 = true;
        this.opaSelected1 = false;
        this.opaSelected = false;
    }
    else
    {
        this.opaSelected2 = false;
        this.opaSelected1 = true;
        this.opaSelected = false;   
        //Rudra: added the code to show section bydefault.

        let value = "electronic-Checks and Electronic Transactions Only";

        let OpaOption = {
        selectedValue: value.split("-")[0],
        selectedLabel: value.split("-")[1]
    };

this.omniApplyCallResp(OpaOption);
    }
}

// Handle change in the custom radio group
handleChange(event) {
    console.log('value ==> ', event.target.value);
    let value = event.target.value;
    let OpaOption = {
        selectedValue:value.split("-")[0],
        selectedLabel:value.split("-")[1]
    };
    this.omniApplyCallResp(OpaOption);          // Send to OmniScript
}
}