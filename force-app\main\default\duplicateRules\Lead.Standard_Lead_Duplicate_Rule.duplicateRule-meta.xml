<?xml version="1.0" encoding="UTF-8"?>
<DuplicateRule xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <actionOnInsert>Allow</actionOnInsert>
    <actionOnUpdate>Allow</actionOnUpdate>
    <alertText>You&apos;re creating a duplicate record. We recommend you use an existing record instead.</alertText>
    <description>Duplicate Rule for Leads using the Standard Lead Matching Rule</description>
    <duplicateRuleFilter xsi:nil="true"/>
    <duplicateRuleMatchRules>
        <matchRuleSObjectType>Lead</matchRuleSObjectType>
        <matchingRule>Standard_Lead_Match_Rule_v1_0</matchingRule>
        <objectMapping xsi:nil="true"/>
    </duplicateRuleMatchRules>
    <duplicateRuleMatchRules>
        <matchRuleSObjectType>Account</matchRuleSObjectType>
        <matchingRule>Standard_Account_Match_Rule_v1_0</matchingRule>
        <objectMapping>
            <inputObject>Lead</inputObject>
            <mappingFields>
                <inputField>Company</inputField>
                <outputField>Name</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>City</inputField>
                <outputField>BillingCity</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>Country</inputField>
                <outputField>BillingCountry</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>State</inputField>
                <outputField>BillingState</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>Street</inputField>
                <outputField>BillingStreet</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>PostalCode</inputField>
                <outputField>BillingPostalCode</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>Phone</inputField>
                <outputField>Phone</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>Website</inputField>
                <outputField>Website</outputField>
            </mappingFields>
            <outputObject>Account</outputObject>
        </objectMapping>
    </duplicateRuleMatchRules>
    <duplicateRuleMatchRules>
        <matchRuleSObjectType>Contact</matchRuleSObjectType>
        <matchingRule>Standard_Contact_Match_Rule_v1_1</matchingRule>
        <objectMapping>
            <inputObject>Lead</inputObject>
            <mappingFields>
                <inputField>Company</inputField>
                <outputField>AccountId</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>Email</inputField>
                <outputField>Email</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>FirstName</inputField>
                <outputField>FirstName</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>LastName</inputField>
                <outputField>LastName</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>City</inputField>
                <outputField>MailingCity</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>Street</inputField>
                <outputField>MailingStreet</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>PostalCode</inputField>
                <outputField>MailingPostalCode</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>Phone</inputField>
                <outputField>Phone</outputField>
            </mappingFields>
            <mappingFields>
                <inputField>Title</inputField>
                <outputField>Title</outputField>
            </mappingFields>
            <outputObject>Contact</outputObject>
        </objectMapping>
    </duplicateRuleMatchRules>
    <isActive>true</isActive>
    <masterLabel>Standard Lead Duplicate Rule</masterLabel>
    <operationsOnInsert>Alert</operationsOnInsert>
    <operationsOnInsert>Report</operationsOnInsert>
    <operationsOnUpdate>Report</operationsOnUpdate>
    <securityOption>EnforceSharingRules</securityOption>
    <sortOrder>1</sortOrder>
</DuplicateRule>
