<template>
            <div class="slds-grid slds-wrap slds-is-relative containersize">
            <div style="position: fixed;z-index: 999999;top: 0;right: 0;">
  <omnistudio-toast class="flexcard-toast-element" theme="slds" title="" message="" styletype=""> </omnistudio-toast>
</div>
            <div if:true={showLoader} class="slds-is-absolute vloc-loader_override" style="height: 100%; width: 100%; min-height:50px; background: transparent; z-index: 99;">
  <div>
   <omnistudio-spinner
      variant="brand"
      alternative-text="Loading content..."
      size="medium"
      theme="slds"
      ></omnistudio-spinner>
  </div>
</div>
            <template if:false={hasPermission}>
              You don't have required permissions to view this card.
            </template>
            <template if:true={hasPermission}>
              <template if:true={hasRecords}>
                        <template for:each={_records} for:item="record" for:index="rindex">
                            <omnistudio-flex-card-state if:true={record}  key={record._flex.uniqueKey} data-recordid={record.Id} record={record} data-statue="true"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12 "  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-p-around_x-small slds-m-bottom_x-small " style="background-color:#FFFFFF;      
         ">
          <div data-style-id="state0element0" class="slds-col   slds-text-link_reset  slds-border_top slds-border_right slds-border_bottom slds-border_left  slds-size_12-of-12  " data-rindex={rindex} style="     border-top: #cccccc 1px solid;border-right: #cccccc 1px solid;border-bottom: #cccccc 1px solid;border-left: #cccccc 1px solid; 
    border-radius:.25em;     max-width: 400px;
margin: auto;
position: relative;
padding: 0 !important;" >
    <omnistudio-block data-style-id="state0element0_child"  card={card}  record={record}  label="Card"  collapsible="false"  collapsed-by-default="false"  action='\{"label":"Action","iconName":"standard-default","eventType":"onclick","actionList":[{"key":"1654203339731-pk0hymvb3","label":"Select Card","draggable":false,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-1654203348931","type":"cardAction","displayName":"Action","vlocityIcon":"standard-default","targetType":"Web Page","openUrlIn":"Current Window","Web Page":{"targetName":"/apex"},"eventName":"selectcards"},"actionIndex":0}],"showSpinner":"false"}'  extraclass=" slds-text-link_reset"  theme="slds"  onclick={executeAction} data-element-label="card" data-action-key="state0element0" onkeydown={executeActionWithKeyboard} tabindex="0"><div class="slds-grid slds-wrap">
          <div data-style-id="state0element0block_element0" class="slds-col  slds-text-align_right slds-border_top slds-border_right slds-p-top_x-small slds-p-left_x-large slds-p-right_xx-small slds-p-top_xx-small slds-p-bottom_large  slds-size_1-of-12  " data-rindex={rindex} style="background-color:#FFFFFF;     border-top: #FFFFFF 2px solid;border-right: #FFFFFF 2px solid; 
         clip-path: polygon(9% 0, 100% 100%, 100% 0);
position: absolute;
right: 0;
top: 0;
z-index: -1;" ><omnistudio-flex-icon data-style-id="state0element0block_element0_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="utility:check"  size="x-small"  extraclass="slds-icon_container slds-icon__svg--default "  variant="inverse"  imgsrc=""  color="#FFFFFF"  theme="slds"  ></omnistudio-flex-icon>
      </div><div data-style-id="state0element0block_element1" class="slds-col    slds-p-around_x-small  slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state0element0block_element1_child"  card={card}  record={record}  label="FinancialAccounts"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state0element0block_element1block_element0" class="slds-col  slds-p-right_x-small slds-size_11-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-radio-input data-style-id="state0element0block_element1block_element0_child"   card={card}  record={record}  type="radiogroup.vertical"  property-obj='\{"options":[],"label":"","radioDisplayValue":"radiogroup.vertical","controlWidth":"100","controlHeight":"100","imageCountInRow":"3","enabledCaption":"true","name":"Block-1-Radio-1"}'  theme="slds"  ></omnistudio-flex-radio-input>
      </div><div data-style-id="state0element0block_element1block_element1" class="slds-col   slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12  " data-rindex={rindex} style="      
         " ><omnistudio-output-field data-style-id="state0element0block_element1block_element1_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%7BcontactName%7D%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div>
        </div></omnistudio-block>
    </div>
        </div>
      </omnistudio-flex-card-state>
                        </template>
                      </template>
                      <template if:false={hasRecords}>
                            <omnistudio-flex-card-state  record={record} data-statue="false"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12 "  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-p-around_x-small slds-m-bottom_x-small " style="background-color:#FFFFFF;      
         ">
          <div data-style-id="state0element0" class="slds-col   slds-text-link_reset  slds-border_top slds-border_right slds-border_bottom slds-border_left  slds-size_12-of-12  " data-rindex={rindex} style="     border-top: #cccccc 1px solid;border-right: #cccccc 1px solid;border-bottom: #cccccc 1px solid;border-left: #cccccc 1px solid; 
    border-radius:.25em;     max-width: 400px;
margin: auto;
position: relative;
padding: 0 !important;" >
    <omnistudio-block data-style-id="state0element0_child"  card={card}  record={record}  label="Card"  collapsible="false"  collapsed-by-default="false"  action='\{"label":"Action","iconName":"standard-default","eventType":"onclick","actionList":[{"key":"1654203339731-pk0hymvb3","label":"Select Card","draggable":false,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-1654203348931","type":"cardAction","displayName":"Action","vlocityIcon":"standard-default","targetType":"Web Page","openUrlIn":"Current Window","Web Page":{"targetName":"/apex"},"eventName":"selectcards"},"actionIndex":0}],"showSpinner":"false"}'  extraclass=" slds-text-link_reset"  theme="slds"  onclick={executeAction} data-element-label="card" data-action-key="state0element0" onkeydown={executeActionWithKeyboard} tabindex="0"><div class="slds-grid slds-wrap">
          <div data-style-id="state0element0block_element0" class="slds-col  slds-text-align_right slds-border_top slds-border_right slds-p-top_x-small slds-p-left_x-large slds-p-right_xx-small slds-p-top_xx-small slds-p-bottom_large  slds-size_1-of-12  " data-rindex={rindex} style="background-color:#FFFFFF;     border-top: #FFFFFF 2px solid;border-right: #FFFFFF 2px solid; 
         clip-path: polygon(9% 0, 100% 100%, 100% 0);
position: absolute;
right: 0;
top: 0;
z-index: -1;" ><omnistudio-flex-icon data-style-id="state0element0block_element0_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="utility:check"  size="x-small"  extraclass="slds-icon_container slds-icon__svg--default "  variant="inverse"  imgsrc=""  color="#FFFFFF"  theme="slds"  ></omnistudio-flex-icon>
      </div><div data-style-id="state0element0block_element1" class="slds-col    slds-p-around_x-small  slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12  " data-rindex={rindex} style="      
         " >
    <omnistudio-block data-style-id="state0element0block_element1_child"  card={card}  record={record}  label="FinancialAccounts"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          <div data-style-id="state0element0block_element1block_element0" class="slds-col  slds-p-right_x-small slds-size_11-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-radio-input data-style-id="state0element0block_element1block_element0_child"   card={card}  record={record}  type="radiogroup.vertical"  property-obj='\{"options":[],"label":"","radioDisplayValue":"radiogroup.vertical","controlWidth":"100","controlHeight":"100","imageCountInRow":"3","enabledCaption":"true","name":"Block-1-Radio-1"}'  theme="slds"  ></omnistudio-flex-radio-input>
      </div><div data-style-id="state0element0block_element1block_element1" class="slds-col   slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12  " data-rindex={rindex} style="      
         " ><omnistudio-output-field data-style-id="state0element0block_element1block_element1_child"   card={card}  record={record}  merge-field="%3Cdiv%3E%7BcontactName%7D%3C/div%3E"  theme="slds"  ></omnistudio-output-field>
      </div>
        </div></omnistudio-block>
    </div>
        </div></omnistudio-block>
    </div>
        </div>
      </omnistudio-flex-card-state>
                      </template>
            </template>
            <template if:true={hasError}>
            {error}
            </template>
            
      <omnistudio-action action-wrapperclass="slds-hide" re-render-flyout class="action-trigger slds-col" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
      
      </omnistudio-action>
  
            <omnistudio-action class="execute-action" re-render-flyout action-wrapperclass="slds-hide" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
            
            </omnistudio-action>
            </div>
          </template>