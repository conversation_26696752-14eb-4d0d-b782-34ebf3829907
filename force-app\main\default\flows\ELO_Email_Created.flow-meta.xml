<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Any_lead_records</name>
        <label>Any lead records</label>
        <locationX>182</locationX>
        <locationY>539</locationY>
        <defaultConnectorLabel>No lead record exists</defaultConnectorLabel>
        <rules>
            <name>Lead_record_exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_lead_related_to_task</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_lead_related_to_task.Mortgage_Lead_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_lead_status_to_working</targetReference>
            </connector>
            <label>Lead record exists</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>ELO Email Created {!$Flow.CurrentDateTime}</interviewLabel>
    <label>ELO Email Created</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_lead_related_to_task</name>
        <label>Get lead related to task</label>
        <locationX>182</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Any_lead_records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_task_related_to_email_message.WhoId</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>012Dn0000008HSTIA2</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Lead</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_task_related_to_email_message</name>
        <label>Get task related to email message</label>
        <locationX>182</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_lead_related_to_task</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.ActivityId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Task</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_lead_status_to_working</name>
        <label>Update lead status to &apos;working&apos;</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Mortgage_Lead_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>New</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Mortgage_Lead_Status__c</field>
            <value>
                <stringValue>Working</stringValue>
            </value>
        </inputAssignments>
        <object>Lead</object>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_task_related_to_email_message</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>3</stringValue>
            </value>
        </filters>
        <object>EmailMessage</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
</Flow>
