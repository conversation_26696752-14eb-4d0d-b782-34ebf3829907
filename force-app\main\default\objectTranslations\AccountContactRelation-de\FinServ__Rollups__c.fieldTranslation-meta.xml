<?xml version="1.0" encoding="UTF-8"?>
<CustomFieldTranslation xmlns="http://soap.sforce.com/2006/04/metadata">
    <label><!-- Roll-Ups --></label>
    <name>FinServ__Rollups__c</name>
    <picklistValues>
        <masterLabel>All</masterLabel>
        <translation>Alle</translation>
    </picklistValues>
    <picklistValues>
        <masterLabel>Assets and Liabilities</masterLabel>
        <translation>Aktiva und Passiva</translation>
    </picklistValues>
    <picklistValues>
        <masterLabel>Cases</masterLabel>
        <translation>Kundenvorgänge</translation>
    </picklistValues>
    <picklistValues>
        <masterLabel>Claim Participants</masterLabel>
        <translation>Anspruchsbeteiligte</translation>
    </picklistValues>
    <picklistValues>
        <masterLabel>Claims</masterLabel>
        <translation>Ansprüche</translation>
    </picklistValues>
    <picklistValues>
        <masterLabel>Events</masterLabel>
        <translation>Ereignisse</translation>
    </picklistValues>
    <picklistValues>
        <masterLabel>Financial Accounts</masterLabel>
        <translation>Finanz-Accounts</translation>
    </picklistValues>
    <picklistValues>
        <masterLabel>Financial Goals</masterLabel>
        <translation>Finanzziele</translation>
    </picklistValues>
    <picklistValues>
        <masterLabel>Insurance Policies</masterLabel>
        <translation>Versicherungspolicen</translation>
    </picklistValues>
    <picklistValues>
        <masterLabel>Insurance Policy Participants</masterLabel>
        <translation>Versicherungspolicenehmer</translation>
    </picklistValues>
    <picklistValues>
        <masterLabel>Opportunities</masterLabel>
        <translation>Opportunities</translation>
    </picklistValues>
    <picklistValues>
        <masterLabel>Referrals</masterLabel>
        <translation>Empfehlungen</translation>
    </picklistValues>
    <picklistValues>
        <masterLabel>Tasks</masterLabel>
        <translation>Aufgaben</translation>
    </picklistValues>
</CustomFieldTranslation>
