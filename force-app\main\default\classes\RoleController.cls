/**
 * @description       : Controller class for Application Roles UI components
 * <AUTHOR> Zennify
 * @last modified on  : 01-16-2025
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
public with sharing class RoleController {
    @AuraEnabled(cacheable=true)
    public static List<DAO_Roles__c> getRoles(Id applicationId) {
        return [SELECT Id,Name, Individual_Role__c, Business_Owned__c, Email__c,IsCompleted__c,Is_ID_Attached__c FROM DAO_Roles__c where DAO_Application__c =: applicationId ORDER BY CreatedDate];
    }
    
    @AuraEnabled
    public static String insertRole(DAO_Roles__c roleData) {
        String result = '';
        
        if (roleData == null) {
            throw new AuraHandledException('Role Data is Null');
        }
        
        try {
            String applicationId = roleData.DAO_Application__c;
            List<DAO_Application__c> applicationData = [
                SELECT Id, Business_Structure__c 
                FROM DAO_Application__c 
                WHERE Id =: applicationId
            ];
            
            List<DAO_Roles__c> daoRoleData = [
                SELECT Id, Individual_Role__c 
                FROM DAO_Roles__c 
                WHERE DAO_Application__c = :applicationId
            ];
            
            DAO_Application__c app = applicationData[0];
            
            Integer controllingCount = 0;
            Integer authorizedSignerCount = 0;
            
            for (DAO_Roles__c role : daoRoleData) {
                if (role.Individual_Role__c == 'Controlling Individual') {
                    controllingCount++;
                } else if (role.Individual_Role__c == 'Authorized Signer') {
                    authorizedSignerCount++;
                }
            }
            
            if (app.Business_Structure__c == 'Corporation' ||
                app.Business_Structure__c == 'General Partnership' ||
                app.Business_Structure__c == 'Limited Liability Company' ||
                app.Business_Structure__c == 'Limited Liability Partnership' ||
                app.Business_Structure__c == 'Limited Partnership' ||
                app.Business_Structure__c == 'Unincorporated Association') {
                    
                    if (daoRoleData.size() < 5) {
                        insert roleData;
                        return 'Success';
                    } else {
                        return 'Error';
                    }
                    
                } else if (app.Business_Structure__c == 'Unincorporated Association/Non-Profit') {
                    if (roleData.Individual_Role__c == 'Controlling Individual' && controllingCount < 3) {
                        insert roleData;
                        return 'Success';
                    } else if (roleData.Individual_Role__c == 'Authorized Signer' && authorizedSignerCount < 5) {
                        insert roleData;
                        return 'Success';
                    } else {
                        return 'Error';
                    }

                } else if (app.Business_Structure__c == 'Sole Proprietorship') {
                    if (roleData.Individual_Role__c == 'Controlling Individual' && controllingCount < 2) {
                        insert roleData;
                        return 'Success';
                    } else if (roleData.Individual_Role__c == 'Authorized Signer' && authorizedSignerCount < 5) {
                        insert roleData;
                        return 'Success';
                    }else if(roleData.Individual_Role__c == 'Beneficial Owner'){
                        insert roleData;
                        return 'Success';
                    } else {
                        return 'Error';
                    }

                } else {
                    return 'Error';
                }
            
        } catch (Exception ex) {
            throw new AuraHandledException('Failed to insert role: ' + ex.getMessage());
        }
    }
    
    
    
    @AuraEnabled
    public static void sendEmail(DAO_Roles__c mailData) {
        
        System.debug('$sendEmail-mailData: ' + mailData);
        
        String roleEmail = mailData.Email__c;
        String roleId = mailData.Id;
        String roleName = mailData.Name;
        String individualRole = mailData.Individual_Role__c;
        String encodedId = EncodingUtil.base64Encode(Blob.valueOf(roleId));

        DAOConfig__mdt config = [ SELECT Id, EmailSubject__c , OrgWideDefaultEmail__c,	EmailTemplate__c, SiteUrl__c FROM DAOConfig__mdt WHERE isActive__c = true LIMIT 1 ];

        if (config == null) {
            throw new AuraHandledException('Email active configuration not found');
        }

        OrgWideEmailAddress[] orgWideEmails = [SELECT Id, Address FROM OrgWideEmailAddress WHERE Address = :config.OrgWideDefaultEmail__c LIMIT 1];

        if(orgWideEmails.size() == 0){
            throw new AuraHandledException('Org Wide Email Address not found');
        }
        
        EmailTemplate template = [SELECT HtmlValue FROM EmailTemplate WHERE DeveloperName = :config.EmailTemplate__c LIMIT 1];

        if (template == null) {
            throw new AuraHandledException('Email template not found');
        }

        String htmlBody = template.HtmlValue.replace('{name}', roleName).replace('{role}', individualRole).replace('{customUrl}', config.SiteUrl__c + encodedId);
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        mail.setHtmlBody(htmlBody);
        mail.setToAddresses(new List<String>{roleEmail});
        mail.setOrgWideEmailAddressId(orgWideEmails[0].Id);
        mail.setSubject(config.EmailSubject__c);
 
        try {
            Messaging.SendEmailResult[] results = Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail }); 
            if (results != null && results.size() > 0) {
                for (Messaging.SendEmailResult result : results) {
                    if (result.isSuccess()) {
                        System.debug('Email sent successfully.');
                    } else {
                        System.debug('Failed to send email. Error: ' + result.getErrors()[0].getMessage());
                    }
                }
            } else {
                System.debug('No result returned from sendEmail.');
            }
            System.debug('Sent');
        } catch (Exception ex) {
            throw new AuraHandledException('Failed to send email: ' + ex.getMessage());
        }
    }
    
    @AuraEnabled
    public static void deleteRole(Id roleId) {
        delete [SELECT Id FROM DAO_Roles__c WHERE Id = :roleId];
    }
    
    @AuraEnabled
    public static void updateRole(DAO_Roles__c role) {
        update role;
    }

    @AuraEnabled(cacheable=true)
    public static List<DAO_Roles__c> getRolesByApplicationId(Id applicationId) {
        if (applicationId == null) {
            throw new AuraHandledException('Application ID cannot be null.');
        }
        return [
            SELECT Id, Name 
            FROM DAO_Roles__c 
            WHERE IsCompleted__c = false AND Dao_Application__c = :applicationId
        ];
    }
}