<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>New_Lead_or_Updated_Lead</name>
        <label>New Lead or Updated Lead</label>
        <locationX>314</locationX>
        <locationY>323</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>NewLead</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>fNewLead</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Referred_by__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Lead_Account_Relationship_Record</targetReference>
            </connector>
            <label>New Lead</label>
        </rules>
        <rules>
            <name>Updated</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>fReferredByUpdated</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Lead_Account_Relationship_Records</targetReference>
            </connector>
            <label>Updated</label>
        </rules>
    </decisions>
    <decisions>
        <name>Transaction_Participant_Record_Exists</name>
        <label>Transaction Participant Record Exists</label>
        <locationX>270</locationX>
        <locationY>539</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_TPR_exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Lead_Account_Relationship_Records</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_LAR_Referred_By_field</targetReference>
            </connector>
            <label>Yes, TPR exists</label>
        </rules>
        <rules>
            <name>No_TPR_doesn_t_exist</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Lead_Account_Relationship_Records</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Lead_Account_Relationship_Record</targetReference>
            </connector>
            <label>No, TPR doesn&apos;t exist</label>
        </rules>
    </decisions>
    <description>Create a Lead Account Relationship object on ReferredBy update</description>
    <environments>Default</environments>
    <formulas>
        <name>fNewLead</name>
        <dataType>Boolean</dataType>
        <expression>ISNEW()</expression>
    </formulas>
    <formulas>
        <name>fReferredByUpdated</name>
        <dataType>Boolean</dataType>
        <expression>ISCHANGED({!$Record.Referred_by__c})</expression>
    </formulas>
    <interviewLabel>Create Lead Account Relationship for Referred By {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Mortgage Create Lead Account Relationship on ReferredBy</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Create_Lead_Account_Relationship_Record</name>
        <label>Create Lead Account Relationship Record</label>
        <locationX>314</locationX>
        <locationY>1031</locationY>
        <inputAssignments>
            <field>Mortgage_Lead_Role__c</field>
            <value>
                <stringValue>Referred by</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Mortgage_Lead__c</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Relationship__c</field>
            <value>
                <elementReference>$Record.Referred_by__c</elementReference>
            </value>
        </inputAssignments>
        <object>Lead_Account_Relationship__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_Lead_Account_Relationship_Records</name>
        <label>Get Lead Account Relationship Records</label>
        <locationX>270</locationX>
        <locationY>431</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Transaction_Participant_Record_Exists</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Mortgage_Lead__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Relationship__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record__Prior.Referred_by__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Mortgage_Lead_Role__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Referred by</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Lead_Account_Relationship__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_LAR_Referred_By_field</name>
        <label>Update LAR Referred By field</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <inputAssignments>
            <field>Relationship__c</field>
            <value>
                <elementReference>$Record.Referred_by__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record.Lead_Account_Relationships__r</inputReference>
    </recordUpdates>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>New_Lead_or_Updated_Lead</targetReference>
        </connector>
        <filterFormula>{!$Record.RecordType.DeveloperName} = &apos;Mortgage_Lead&apos;</filterFormula>
        <object>Lead</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>leadaccountrelationshiprecordcount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>MatchingLeadAccountRelationshipRecords</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Lead_Account_Relationship__c</objectType>
    </variables>
    <variables>
        <name>StagingLeadUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Lead_Account_Relationship__c</objectType>
    </variables>
    <variables>
        <name>ZeroValue</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
</Flow>
