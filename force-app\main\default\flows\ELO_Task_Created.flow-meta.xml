<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>Any_ELO_task_records</name>
        <label>Any ELO task records</label>
        <locationX>182</locationX>
        <locationY>431</locationY>
        <defaultConnectorLabel>No ELO tasks record exists</defaultConnectorLabel>
        <rules>
            <name>ELO_task_record_exists</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_ELO_Activity_records</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_ELO_Activity_records.Mortgage_Lead_Status__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>New</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_lead_status_to_working</targetReference>
            </connector>
            <label>ELO task record exists</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>ELO Task Created {!$Flow.CurrentDateTime}</interviewLabel>
    <label>ELO Task Created</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_ELO_Activity_records</name>
        <label>Get ELO Activity records</label>
        <locationX>182</locationX>
        <locationY>323</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Any_ELO_task_records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.WhoId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Lead</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_lead_status_to_working</name>
        <label>Update lead status to &apos;working&apos;</label>
        <locationX>50</locationX>
        <locationY>539</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Mortgage_Lead_Status__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>New</stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>Mortgage_Lead_Status__c</field>
            <value>
                <stringValue>Working</stringValue>
            </value>
        </inputAssignments>
        <object>Lead</object>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_ELO_Activity_records</targetReference>
        </connector>
        <filterFormula>{!$Record.RecordType.Name} = &quot;ELO Tasks&quot;</filterFormula>
        <object>Task</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
