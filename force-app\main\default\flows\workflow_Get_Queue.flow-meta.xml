<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>QueueIdAssignment</name>
        <label>QueueIdAssignment</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>QueueDevName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Queue.Queue.DeveloperName</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <description>Get Queue given dao_application__c.preferred_branch__c contains</description>
    <environments>Default</environments>
    <formulas>
        <name>QueueNameFormula</name>
        <dataType>String</dataType>
        <expression>{!PreferredBranch} + &apos; Branch&apos;</expression>
    </formulas>
    <interviewLabel>[workflow] Get Queue ID {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[workflow] Get Queue</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Queue</name>
        <label>Get Queue</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>QueueIdAssignment</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>QueueId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Match_Group.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>sObjectType</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>QueueSobject</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Match_Group</name>
        <label>Match Group</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Queue</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>QueueNameFormula</elementReference>
            </value>
        </filters>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Match_Group</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>PreferredBranch</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>QueueDevName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>QueueName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <description>API Object Name</description>
        <name>sObjectType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
