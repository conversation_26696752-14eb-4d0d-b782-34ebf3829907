<?xml version="1.0" encoding="UTF-8"?>
<NamedCredential xmlns="http://soap.sforce.com/2006/04/metadata">
    <allowMergeFieldsInBody>false</allowMergeFieldsInBody>
    <allowMergeFieldsInHeader>false</allowMergeFieldsInHeader>
    <calloutStatus>Enabled</calloutStatus>
    <generateAuthorizationHeader>true</generateAuthorizationHeader>
    <label>SocureAPI</label>
    <namedCredentialParameters>
        <parameterName>Url</parameterName>
        <parameterType>Url</parameterType>
        <parameterValue>https://tapi.redwoodcu.org:7140/api/riskscoreonly</parameterValue>
    </namedCredentialParameters>
    <namedCredentialParameters>
        <externalCredential>AzureOAuthToken</externalCredential>
        <parameterName>ExternalCredential</parameterName>
        <parameterType>Authentication</parameterType>
    </namedCredentialParameters>
    <namedCredentialParameters>
        <parameterName>AllowedNamespace_HktIZZG1737490432843</parameterName>
        <parameterType>AllowedManagedPackageNamespaces</parameterType>
        <parameterValue>omnistudio</parameterValue>
    </namedCredentialParameters>
    <namedCredentialType>SecuredEndpoint</namedCredentialType>
</NamedCredential>
