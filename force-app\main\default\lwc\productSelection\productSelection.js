import { LightningElement, track , api, wire} from 'lwc';
import {OmniscriptBaseMixin} from 'omnistudio/omniscriptBaseMixin';
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import getProductCodes from "@salesforce/apex/ProductSelectionController.getProductCodes";
export default class productSelection extends OmniscriptBaseMixin(LightningElement) {
    @api omniScript; 
    @track value1 = '';
    @track isModalOpen = false;
    @track isModalOpen1 = false;
    @track essentialChecked = false;
    @track essentailFinalCheck = true;
    @track choiceChecked = false;
    @track savingCheckBox = false;
    @track eVisaDebit = null;
    @track cVisaDebit = null;
    @track businessVisa;
    @track businessVisa1;
    @track essentialVisa = '';
    @track essentialVisa1;
    @track cVisaDebit1 = '';
    @track accountEStatement1 = '';
    @track accountOnline1 = '';
    @track checkingVisa = '';
    @track checkingVisa1 = '';
    @track accountEStatement = '';
    @track accountOnline = '';
    @track eStatements = null;
    @track onlineBankingValue = null;
    @track businessCreditCard = null;
    @track prouductCodesMap = null;






    @wire(getProductCodes)
    wiredCodes({ error, data }) {
      if (data) {
        this.prouductCodesMap = new Map();
        console.log('$Codes', data);
        data.forEach(mdt => {
            console.log('mdt: ',mdt);
            if(mdt.Name__c == 'Business Savings'){
                this.prouductCodesMap.set('Savings', mdt.Product_Code__c);
            }else if(mdt.Name__c == 'Business Choice Checking'){
                this.prouductCodesMap.set('Choice Checking', mdt.Product_Code__c);
            }else if(mdt.Name__c == 'Business Essential Checking'){
                this.prouductCodesMap.set('Essential Checking', mdt.Product_Code__c);
            }
        });
        console.log('$this.prouductCodesMap: ', this.prouductCodesMap);
      } else if (error) {
        console.error('$Error:', error);
      }
    }

    get options() {
        return [
            { label: 'Yes', value: 'Yes' },
            { label: 'No', value: 'No' }
        ];
    }
    
    @track showMembershipBenefitsSection = false;
   
    connectedCallback() {
      console.log(JSON.stringify(this.omniJsonData));
        let selectedType = this.omniJsonData.type;
        if(selectedType == 'Essential Checking'){
            console.log('Essential : ',this.omniJsonData.eVisaDebit + ' ' + this.omniJsonData.eBoxCheck);
            this.essentialChecked = true;
            this.essentailFinalCheck=true;
            this.choiceChecked = false;
            this.eVisaDebit = this.omniJsonData.eVisaDebit;  

            this.essentialVisa = this.eVisaDebit == 'Yes' ? true : false;
            this.essentialVisa1 = !this.essentialVisa;

            if(this.omniJsonData.businessCreditCard){
                this.businessCreditCard = this.omniJsonData.businessCreditCard;
                if(this.businessCreditCard == 'Yes'){
                    this.businessVisa = true;
                }
                if(this.businessCreditCard == 'No')
                    this.businessVisa1 = true;
            }

            this.eStatements = this.omniJsonData.eStatements || 'No';
            this.accountEStatement = this.eStatements == 'Yes' ? true : false;
            this.accountEStatement1 = !this.accountEStatement;
            this.onlineBankingValue = this.omniJsonData.onlineBankingValue || 'No';
            this.accountOnline = this.onlineBankingValue == 'Yes' ? true : false;
            this.accountOnline1 = !this.accountOnline;
            this.showMembershipBenefitsSection = true;


        }else if(selectedType == 'Choice Checking'){
            this.essentailFinalCheck=false;
            this.choiceChecked = true;
            this.essentialChecked = false;
            this.cVisaDebit = this.omniJsonData.cVisaDebit;
            // console.log('cVisaDebit>>>>>', this.cVisaDebit);
            this.checkingVisa = this.cVisaDebit == 'Yes' ? true : false;
            // console.log('checkingVisa<><<>>', this.checkingVisa);
            this.checkingVisa1 = !this.checkingVisa;


            if(this.omniJsonData.businessCreditCard){
                this.businessCreditCard = this.omniJsonData.businessCreditCard;
                if(this.businessCreditCard == 'Yes'){
                    this.businessVisa = true;
                }
                if(this.businessCreditCard == 'No')
                    this.businessVisa1 = true;
            }

            this.eStatements = this.omniJsonData.eStatements //|| 'No';
            this.accountEStatement = this.eStatements == 'Yes' ? true : false;
            this.accountEStatement1 = !this.accountEStatement;
            this.onlineBankingValue = this.omniJsonData.onlineBankingValue; //  || 'No';
            this.accountOnline = this.onlineBankingValue == 'Yes' ? true : false;
            this.accountOnline1 = !this.accountOnline;
            this.showMembershipBenefitsSection = true;
        }else{
            this.essentialVisa = true;
            this.eVisaDebit = 'Yes';
            this.checkingVisa = true;
            //////
            this.cVisaDebit = 'Yes';
            ////
            this.accountEStatement = true;
            this.eStatements = 'Yes';
            this.accountOnline = true;
            this.onlineBankingValue = 'Yes';
        }
        this.showSomeDebugs(); 
        
    }
    








    changeEssentialRadio(event) {

         this.essentialChecked = event.target.checked;
        if (this.essentialChecked) {
            this.choiceChecked = false;
            this.essentailFinalCheck = true;
            this.showMembershipBenefitsSection = true;
        }
        console.log('$essentialChecked>>>>: ', this.essentialChecked);

    }

    changeChoiseRadio(event) {

        this.choiceChecked = event.target.checked;
        if (this.choiceChecked) {
            this.essentialChecked = false;
            this.essentailFinalCheck = false;
            this.showMembershipBenefitsSection = true;
        }
        console.log('$choiceChecked: ', this.choiceChecked);

    }

    changeVisaDebitCardRadio(event) {
        this.eVisaDebit = event.target.value;
        console.log('$eVisaDebit :', this.eVisaDebit);
       

    }

    changeVisaDebitCardRadioCType(event) {
        this.cVisaDebit = event.target.value;

        console.log('$cVisaDebit: ',this.cVisaDebit);
    
    }
    
    changeEStatementRadio(event) {
        this.eStatements = event.target.value;
        console.log('$eStatements: ', this.eStatements);
    }

    changeDigitalBankingRadio(event) {
        this.onlineBankingValue = event.target.value;
        console.log('$onlineBankingValue: ', this.onlineBankingValue);
    }

    changeBussinessCreditCardRadio(event) {
        this.businessCreditCard = event.target.value;
        console.log('$businessCreditCard: ',this.businessCreditCard);
    }

    handleNextPage() {
        if (!this.essentialChecked && !this.choiceChecked) {
            const evt = new ShowToastEvent({
                title: 'Error',
                message: 'Required Fields Are Missing',
                variant: 'error',
            });
            this.dispatchEvent(evt);
        }
        else {
            let dataToPass = {};
            if (this.essentialChecked) {
                console.log('$eVisaDebit: ', this.eVisaDebit);
                console.log('$eStatements: ', this.eStatements);
                console.log('$onlineBankingValue: ', this.onlineBankingValue);
                console.log('$businessCreditCard: ', this.businessCreditCard);
                if (this.eVisaDebit && this.eStatements && this.onlineBankingValue && this.businessCreditCard) {
                    dataToPass = {
                        type: "Essential Checking",
                        code: this.prouductCodesMap.get('Essential Checking'),
                        savingsCode: this.prouductCodesMap.get('Savings'),
                        eVisaDebit: this.eVisaDebit,
                        eStatements: this.eStatements,
                        onlineBankingValue: this.onlineBankingValue,
                        businessCreditCard: this.businessCreditCard
                    };
                    console.log(JSON.stringify(dataToPass));
                    this.omniApplyCallResp(dataToPass);
                    this.omniNextStep();
                } else {
                    this.showMissingFieldsError();
                }
            }else if (this.choiceChecked) {
                console.log('$cVisaDebit: ', this.cVisaDebit);
                console.log('$eStatements: ', this.eStatements);
                console.log('$onlineBankingValue: ', this.onlineBankingValue);
                console.log('$businessCreditCard: ', this.businessCreditCard);
                if (this.cVisaDebit && this.eStatements && this.onlineBankingValue && this.businessCreditCard) {
                    dataToPass = {
                        type: "Choice Checking",
                        code: this.prouductCodesMap.get('Choice Checking'),
                        savingsCode: this.prouductCodesMap.get('Savings'),
                        cVisaDebit: this.cVisaDebit,
                        eStatements: this.eStatements,
                        onlineBankingValue: this.onlineBankingValue,
                        businessCreditCard: this.businessCreditCard
                    };
                    console.log(JSON.stringify(dataToPass));
                    this.omniApplyCallResp(dataToPass);
                    this.omniNextStep();
                } else {
                    this.showMissingFieldsError();
                }
            }
        }
    }

    openModal(event) {
        console.log('OPEN');
        event.preventDefault();
        event.stopPropagation();
        this.isModalOpen = true;
    }

    openModal2(event) {
        event.preventDefault();
        event.stopPropagation();
        this.isModalOpen1 = true;
    }

    closeModal() {
        this.isModalOpen = false;
        this.isModalOpen1 = false;
    }

    showMissingFieldsError() {
        const evt = new ShowToastEvent({
            title: 'Error',
            message: 'Required Fields Are Missing',
            variant: 'error',
        });
        this.dispatchEvent(evt);
    }

    handlePreviousPage() {
        this.omniPrevStep();
    }

    renderedCallback(){
        this.showSomeDebugs();
    }

    showSomeDebugs(){
        console.log('$choiceChecked: ', this.choiceChecked);
        console.log('$essentialChecked: ', this.essentialChecked);
        console.log('$essentailFinalCheck: ', this.essentailFinalCheck);
        console.log('$eVisaDebit: ', this.eVisaDebit);
        console.log('$cVisaDebit: ', this.cVisaDebit);
        console.log('$eStatements: ', this.eStatements);
        console.log('$onlineBankingValue: ', this.onlineBankingValue);
        console.log('$businessCreditCard: ', this.businessCreditCard);
        console.log(JSON.stringify(this.omniJsonData));
    } 
}