global with sharing class GeneratePdf implements omnistudio.VlocityOpenInterface {
    public List<RoleData> roleDataList { get; set; }
    
    global GeneratePdf() {
        roleDataList = new List<RoleData>(); 
    }
    
    global Boolean invokeMethod(String methodName, Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        Boolean result = true;
        
        try {
            if (methodName.equals('generatePdf')) {
                Map<String, Object> applicationSummary = (Map<String, Object>)inputMap.get('ApplicationSummary');
                String appId = (String)applicationSummary.get('AppId');
                String pdfAttachmentId = generateAndAttachPDF(applicationSummary, appId);
                outMap.put('pdfAttachmentId', pdfAttachmentId); // Returning the PDF attachment ID
            } else {
                result = false;
                outMap.put('error', 'Method not supported.');
            }
        } catch (Exception e) {
            result = false;
            outMap.put('error', 'Error in invokeMethod: ' + e.getMessage());
        }
        
        return result;
    }
    
    private String generateAndAttachPDF(Map<String, Object> applicationSummary, String appId) {
        try {
            PageReference pdfPage = Page.GeneratePdf; // Replace with the Visualforce page name
            for (String key : applicationSummary.keySet()) {
                if (applicationSummary.get(key) != null && applicationSummary.get(key) instanceof Map<String, Object>) {
                    Map<String, Object> nestedMap = (Map<String, Object>) applicationSummary.get(key);
                    for (String nestedKey : nestedMap.keySet()) {
                        pdfPage.getParameters().put(key + '.' + nestedKey, String.valueOf(nestedMap.get(nestedKey)));
                        pdfPage.getParameters().put('AppId.', String.valueOf(appId));
                    }
                } else {
                    pdfPage.getParameters().put(key, String.valueOf(applicationSummary.get(key)));
                    pdfPage.getParameters().put('AppId.', String.valueOf(appId));
                }
            }
            
            if (applicationSummary.containsKey('Block17') && applicationSummary.get('Block17') instanceof Map<String, Object>) {
                Map<String, Object> block17Data = (Map<String, Object>) applicationSummary.get('Block17');
                for (String blockKey : block17Data.keySet()) {
                    pdfPage.getParameters().put('Block17.' + blockKey, String.valueOf(block17Data.get(blockKey)));
                    
                }
            }
            Blob pdfBlob = pdfPage.getContentAsPDF();
            ContentVersion contentVersion = new ContentVersion();
            contentVersion.VersionData = pdfBlob;
            contentVersion.Title = 'Application Summary PDF';
            contentVersion.PathOnClient = 'GeneratedPDF_' + appId + '.pdf'; // Optionally include appId in filename
            insert contentVersion;
            
            ContentDocumentLink contentLink = new ContentDocumentLink();
            contentLink.ContentDocumentId = [SELECT ContentDocumentId FROM ContentVersion WHERE Id = :contentVersion.Id].ContentDocumentId;
            contentLink.LinkedEntityId = appId; 
            contentLink.ShareType = 'V';
            contentLink.Visibility = 'AllUsers';
            insert contentLink;
            
            return contentVersion.Id; 
        } catch (Exception e) {
            System.debug('Error generating and attaching PDF: ' + e.getMessage());
            return null;
        }
    }
    
    
    
    public class RoleData {
        public String DAORolesName { get; set; }
        public String Email5 { get; set; }
        public String SSNTIN { get; set; }
    }
}