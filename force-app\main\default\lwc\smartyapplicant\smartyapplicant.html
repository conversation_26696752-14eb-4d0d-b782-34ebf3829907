<!--
  @description       : Prototype for the Smarty component
  <AUTHOR> Zennify
  @last modified on  : 05-20-2025
  @last modified by  : <PERSON><PERSON><PERSON>
-->
<template>
    <!-- Aligned Address Field -->
    <div class="slds-form-element">
        <div class="slds-form-element__control">
            <div class="slds-input-has-icon slds-input-has-icon_right">
                <lightning-input 
                    type="text" 
                    label="Address" 
                    id="inputField" 
                    onchange={handleInputChange} 
                    value={fullAddress}
                    required
                ></lightning-input>
                <template if:true={isAddressFilled}>
                    <lightning-button-icon icon-name="utility:edit" onclick={handlePencilClick} alternative-text="Edit" title="Edit"></lightning-button-icon>
                </template>
            </div>
            <div if:true={suggestion}>
                <div class="slds-m-around_medium address-suggestions-center" role="listbox">
                    <ul class="slds-listbox slds-listbox_vertical slds-dropdown slds-dropdown_fluid" role="presentation">
                        <template for:each={suggestion} for:item="addressRecommendation">
                            <li data-key={addressRecommendation.id} key={addressRecommendation.id} role="presentation"
                                onclick={setAddress}
                                data-value={addressRecommendation.value} class="slds-listbox__item">
                                <span class="slds-media slds-listbox__option slds-listbox__option_entity slds-listbox__option_has-meta" role="option">
                                    <span class="slds-media__body slds-m-left_xx-small slds-m-bottom_xx-small">
                                        <div class="slds-grid slds-m-bottom_small">
                                            <div>
                                                <lightning-button-icon size="medium" icon-name="utility:checkin" class="slds-input__icon" variant="bare"></lightning-button-icon>
                                            </div>
                                            <div class="slds-m-left_medium">
                                                <span><b>{addressRecommendation.value}</b></span>
                                            </div>
                                        </div>
                                    </span>
                                </span>
                            </li>
                        </template>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Aligned Address Edit Form -->
    <template if:true={showAddressForm}>
        <div class="slds-form-element slds-m-top_small">
            <div class="slds-form-element__control">
                <lightning-input-address
                    id="addressInput"
                    address-label=""
                    street-label="Street"
                    city-label="City"
                    country-label="Country"
                    province-label="State"
                    state-label="State"
                    postal-code-label="PostalCode"
                    street={street}
                    city={city}
                    country="US"
                    postal-code={postalCode}
                    province={state}
                    required
                    field-level-help=""
                    onchange={handleAddressChange}>
                </lightning-input-address>
            </div>
        </div>
    </template>
</template>