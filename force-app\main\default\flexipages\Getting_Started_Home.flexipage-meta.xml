<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>home:heroChart</componentName>
                <identifier>home_heroChart</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-fc6b529c-54ab-4962-bbeb-bf53fdcac66b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <valueList>
                        <valueListItems>
                            <value>standard-Product2</value>
                        </valueListItems>
                        <valueListItems>
                            <value>standard-Pricebook2</value>
                        </valueListItems>
                        <valueListItems>
                            <value>standard-Quote</value>
                        </valueListItems>
                        <valueListItems>
                            <value>standard-Contract</value>
                        </valueListItems>
                        <valueListItems>
                            <value>standard-Order</value>
                        </valueListItems>
                        <valueListItems>
                            <value>standard-Forecasting3</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Sales Operations</value>
                </componentInstanceProperties>
                <componentName>runtime_mobilesapp:launchPadTileList</componentName>
                <identifier>runtime_mobilesapp_launchPadTileList</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <valueList>
                        <valueListItems>
                            <value>standard-Campaign</value>
                        </valueListItems>
                        <valueListItems>
                            <value>standard-ListEmail</value>
                        </valueListItems>
                        <valueListItems>
                            <value>standard-EmailTemplate</value>
                        </valueListItems>
                        <valueListItems>
                            <value>standard-EnhancedLetterhead</value>
                        </valueListItems>
                        <valueListItems>
                            <value>standard-LightningQuickText</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Email</value>
                </componentInstanceProperties>
                <componentName>runtime_mobilesapp:launchPadTileList</componentName>
                <identifier>runtime_mobilesapp_launchPadTileList2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <valueList>
                        <valueListItems>
                            <value>standard-DuplicateRecordSet</value>
                        </valueListItems>
                        <valueListItems>
                            <value>standard-DeleteEvent</value>
                        </valueListItems>
                        <valueListItems>
                            <value>standard-File</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Data</value>
                </componentInstanceProperties>
                <componentName>runtime_mobilesapp:launchPadTileList</componentName>
                <identifier>runtime_mobilesapp_launchPadTileList3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-1280b9bd-e429-4fa4-a4e0-07a0132d5fa2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-fc6b529c-54ab-4962-bbeb-bf53fdcac66b</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Monitor</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-1280b9bd-e429-4fa4-a4e0-07a0132d5fa2</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Manage</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>flexipage_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-2645d0c5-2720-4908-adc5-5044fcd6e655</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>Facet-2645d0c5-2720-4908-adc5-5044fcd6e655</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <name>top</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>entityNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Contact</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Recent Contacts</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecords</name>
                    <value>5</value>
                </componentInstanceProperties>
                <componentName>flexipage:recentItems</componentName>
                <identifier>flexipage_recentItems</identifier>
            </componentInstance>
        </itemInstances>
        <name>bottomLeft</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>enableInlineEdit</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>entityName</name>
                    <value>Account</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>filterName</name>
                    <value>MyAccounts</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideActionBar</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideSearchBar</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>pageSize</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentName>flexipage:filterListCard</componentName>
                <identifier>flexipage_filterListCard</identifier>
            </componentInstance>
        </itemInstances>
        <name>bottomRight</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>home:eventContainer</componentName>
                <identifier>home_eventContainer</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>runtime_sales_activities:todayTaskContainer</componentName>
                <identifier>runtime_sales_activities_todayTaskContainer</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>home:assistant</componentName>
                <identifier>home_assistant</identifier>
            </componentInstance>
        </itemInstances>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Getting Started Home</masterLabel>
    <template>
        <name>home:desktopTemplate</name>
    </template>
    <type>HomePage</type>
</FlexiPage>
