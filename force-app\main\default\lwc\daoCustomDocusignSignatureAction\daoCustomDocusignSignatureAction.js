import { LightningElement, api, track } from 'lwc';
import { OmniscriptBaseMixin } from 'omnistudio/omniscriptBaseMixin';
import omniscriptDocusignSignatureAction from 'omnistudio/omniscriptDocusignSignatureAction';
import tmpl from './daoCustomDocusignSignatureAction.html';

export default class DaoCustomDocusignSignatureAction extends OmniscriptBaseMixin(omniscriptDocusignSignatureAction) {
    @track _docusignModal = false;
    @track _headerClasses = 'slds-modal__header';
    @track _footerClasses = 'slds-modal__footer';
    @track _modalContainerClass = 'slds-modal';
    @track _envelopeId = '';
    @track _envelopeIdArray = [];
    @track _pdfData = '';
    @track showViewPdfBtn = false;
    @track disableViewPdfBtn = false;
    @track isPageLoading = false;
    @track _isBtn = true;
    @track isBtnLoading = false;
    @api name;
    @api fieldLabel;
    @api signerName;
    @api signerEmail;
    @api emailSubject;
    @api docusignReturnUrl;
    @api dateFormat;
    @api dateTimeFormat;
    @api timeFormat;
    @api validationRequired;
    @api templates;
    @api extraclass;
    @api _theme;
    @api _propSetMap = { label: 'Sign with DocuSign' };
    @api spinnerActionMessage = 'Loading...';
    @api allCustomLabelsUtil = {
        OmniDocuSignModalTitle: 'DocuSign Signature',
        OmniDocuSignModalViewPdf: 'View PDF',
        OmniDocuSignModalClose: 'Close'
    };

    @api
    openModal(envelopeId) {
        this._envelopeId = envelopeId;
        this._docusignModal = true;
    }

    execute() {
        // TODO: Implement DocuSign process initiation logic
        this._docusignModal = true;
        console.log('execute');
    }

    viewPDF() {
        // TODO: Implement PDF viewing logic
        this.disableViewPdfBtn = true;
    }

    closeModal() {
        this._docusignModal = false;
    }

    render() {
        return tmpl;
    }

    connectedCallback() {
        console.log('connectedCallback');
    }
}