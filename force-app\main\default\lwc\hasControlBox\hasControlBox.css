/* Custom styles for modal */
.open-modal-button {
    background-color: #82C341;
}

.slds-button_icon-container.custom-close-button {
    background-color: #82C341;
}

.custom-ok-button {
    background-color: #82C341;
    color: white;
}

.slds-modal__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.slds-modal__header button.slds-button_icon {
    background-color: #82C341;
}

.slds-button.slds-button_icon-container {
    background-color: #82C341;
}

/* .slds-button_icon-container.custom-close-button {
    position: absolute;
    top: 0;
    right: 0;
} */

/* Custom styling for input field and button 
.input-with-button {
    display: inline-block;
    width: calc(100% - 40px); /* Make space for button 
} */


.slds-modal__content {
    /* display: flex; */
    justify-content: flex-start;
    align-items: center;
}

.slds-modal__content .slds-form-element {
    /* display: flex; */
    justify-content: space-between;
    align-items: center;
}

.slds-modal__content lightning-button {
    margin-left: 10px; /* Space between the text input and the button */
}

.modal-container {
    position: relative; /* Parent container to anchor the button */
}

.question-button {
    position: absolute; /* Position the button relative to the parent */
    top: 0px; /* Align to the top of the container */
    right: 9px; /* Align to the right of the container */
   /*  z-index: 10;  Ensure the button stays above other elements */
    border-radius: 22px;
    background-color: #82C341;
    color: #fff; 
    border:none;
	height: 14px;
	width: 14px;
	
}

.input-with-button {
    margin-top: -10px; /* Add spacing between input and button if needed */
    position: sticky;
}

.custom-small-container {
    position: relative;
    height: 650px;
    top: 100px;
    width: 252px;
    /* vertical-align: middle; */
    padding-top: 0px;
    border-radius: 4px;
}

.custom-close-button {
    position: relative;
    top: 0px; /* Align to top */
    right: 0px; /* Align to right */
    background-color: #82C341; /* Green background */
    border-radius: 50%;
    padding: 4px;
    width: 20px;
    height: 20px;
    margin-left: 250px;
    padding-bottom: 5px;
    color: white;
    border: none;
}

.custom-close-icon {
    fill: #fff; /* White color for the icon */
    color: white;
}

/* Modal Content Adjustments 
.custom-modal-content {
    font-size: 12px; /* Small font size for content  
    width:300px;
    height: 600px;
    line-height: 1.5;
    max-height: 100%; /* Prevents content from scrolling vertically 
    overflow: hidden; 
} 
 */
/* OK Button Styling */
.custom-ok-button {
    background-color: #82C341; /* Green background */
    color: #fff; /* White text */
    font-weight: bold;
    width: 120px; /* Fixed button width */
    border-radius: 4px; /* Rounded corners */
     /* Center button */
    left: 85px;
}

 .custom-small-modal {
    top: 5px;
    margin: 0 auto; /* Center the modal horizontally  */
    
} 

.custom-small-modal .slds-modal__content {
    font-size: 12px; /* Adjust font size for compact content */
    line-height: 1.5; /* Ensure content is readable */
}
lightning-button-icon 
{
    color:white !important;
}

.slds-backdrop.slds-backdrop_open {
    background-color: transparent !important;
    backdrop-filter: none !important; /* Removes any blur effect */
}