let definition =
      {"states":[{"fields":[],"conditions":{"id":"state-condition-object","isParent":true,"group":[]},"definedActions":{"actions":[]},"name":"Active","isSmartAction":false,"smartAction":{},"styleObject":{"padding":[{"type":"around","size":"x-small"}],"margin":[{"type":"bottom","size":"x-small"}],"container":{"class":"slds-card"},"size":{"isResponsive":false,"default":"12"},"sizeClass":"slds-size_12-of-12","class":"slds-card slds-p-around_x-small slds-m-bottom_x-small"}}],"dataSource":{"type":null,"value":{},"orderBy":{},"contextVariables":[]},"title":"testflex","enableLwc":true,"isFlex":true,"theme":"slds","selectableMode":"Multi","Name":"testflex","uniqueKey":"testflex_1_Redwood_CU_New_Org","Id":"0koU80000000H45IAE","OmniUiCardKey":"testflex/Redwood_CU_New_Org/1.0","OmniUiCardType":"Parent"};
  export default definition