public with sharing class ApplicationDetailsController {
    @AuraEnabled(cacheable=true)
    public static DAO_Application__c getApplicationDetails(String applicationId) {
        // Query to fetch the application details based on the provided application ID
        DAO_Application__c applicationDetails = [
            SELECT 
                Id, Name,  NAICS_Code_Text__c  , Business_Description__c, Other_Occupancy_Status__c,
                Previous_City__c, Previous_State__c, Previous_Street__c, Previous_Zip__c, isAcknowledged__c,
                Depend_on_Charitable_donations_voluntar__c, Business_Headquarters_County__c, Different_Mailing_Address__c,

                DBA_Name__c, Fictitious_Business_Name_DBA__c,
                Business_Tax_ID_Type__c, Business_Tax_ID__c, Applicant_SSN__c, Business_Structure__c,
                Establish_Date__c, Business_Annual_Revenue__c, Number_of_Employees__c,
                Business_HQ_County__c, State_Registered__c,
                Other_Headquarters_County__c, Principal_location_of_your_business__c, Preferred_Branch__c,
                BusinessStreet__c, BusinessCity__c, BusinessState__c, BusinessZip__c, 
                MailingStreet__c, MailingCity__c, MailingState__c, MailingZip__c,
                Occupancy_Status__c, Occupancy_Duration_Years__c, Occupancy_Duration_Months__c,
                Business_Email__c, Business_Cell_Phone__c, Business_Phone__c, Business_Fax__c,

                
                Is_this_a_publicly_traded_company__c, Traded_on_the_stock_exchange__c, Owned_by_an_entitiy_on_stock_exchange__c,
                Business_headquartered_in_US__c, Country_business_headquartered__c, Registered_to_do_business_in_California__c,
                Charitable_Donations_and_Voluntary_Servi__c, Objectives_Programs_Activities_Services__c, Chartered_Organization_Country__c,
                Registered_as_a_Non_profit__c, Sources_of_funding_for_the_organization__c, Methods_to_obtain_funding__c,
                Value_of_annual_funding_or_gross_receipt__c, Volunteers_from_non_US_countries__c, Donor_or_volunteer_countries__c,
                Will_services_benefit_individuals_in_for__c, In_what_countries_do_you_provide_charita__c, Countries_your_foreign_beneficiaries__c,

                General_Operation_Funds__c, Payroll__c, Savings__c, Credit_Card_Processing__c, IOLTA_IOLA__c, 
                Lottery__c, MSB_Activity__c, Private_Banking__c, Other__c, Other_Notes__c,
                
                Private_Label_Credit_Card__c, Private_Label_Credit_Card_Point_of_Sale__c,
                Postage_Remittance__c, Postage_Purchase__c, Postage_Third_Parties__c, Postage_Cash_Refund__c,
                Equipment_or_Lease__c, Equipment_Purchase__c, Equipment_Third_Parties__c, Equipment_Cash_Refund__c,
                Pooled_Investment_Vehicle__c, Excluded_Financial_Institution__c, Personal_Banking_Activity__c, Major_Vendors_or_Customers__c,

                Cash_Deposits__c, Cash_Deposits_Avg_Amount__c, ATM_Cash_Deposits__c, ATM_Cash_Deposits_Avg_Amount__c,
                Cash_Withdrawals__c, Cash_Withdrawals_Avg_Amount__c, ATM_Cash_Withdrawals__c, ATM_Cash_Withdrawals_Avg_Amount__c,
                Check_Deposits__c, Check_Deposits_Avg_Amount__c, Remote_Deposit_Capture__c, Check_Withdrawals__c, Check_Withdrawals_Avg_Amount__c,
                Cash_Deposits_Courier__c, Cash_Withdrawals_Courier__c,

                Incoming_Wire_Transfers__c, Incoming_Wire_Transfers_Avg_Amount__c, Incoming_Wire_Transfers_Non_US__c, Incoming_Wire_Transfers_Countries__c,
                Outgoing_Wire_Transfers__c, Outgoing_Wire_Transfers_Avg_Amount__c, Outgoing_Wire_Transfers_Non_US__c, Outgoing_Wire_Transfers_Countries__c, 
                Incoming_Electronic_Transfers__c, Incoming_Electronic_Transfers_Avg_Amount__c, Incoming_Electronic_Transfers_Non_US__c, Incoming_Electronic_Transfers_Countries__c, 
                Outgoing_Electronic_Transfers__c, Outgoing_Electronic_Transfers_Avg_Amount__c, Outgoing_Electronic_Transfers_Non_US__c, Outgoing_Electronic_Transfers_Countries__c, 

                Internet_Gambling__c, 

                Marijuana_Business__c, MarijuanaActivity__c, MarijuanaLicensed__c, MarijuanaPercentage__c,

                Intermediary__c, ProfessionalOthersUsing__c, ProfessionalTypeOtherText__c, ProfessionalType__c, ServicesTypes__c,

                Courier_Services__c, 

                Third_Party_Payment_Processor__c, BusinessTypeRestrictionsText__c, BusinessTypeRestrictions__c, CreateChecksRemotely__c, PaymentServicesOtherText__c, PaymentServices__c, PaymentsHowProcessed__c, PaymentsThroughAccounts__c, TransactionsReceivePayments__c, TransactionsSendPayments__c,
                
                ATM_Business__c, AtmDenomination__c, AtmMaxHolding__c, NumberOfAtm__c, PrivateAtmType__c, SourceOfAtmCash__c, ReplenishAtmCash__c,
                
                Non_Bank_Financial_Institution__c, DocBsaAmlProgram__c, InvolveCasinos__c, InvolveCreditCards__c, InvolveGovAgency__c, InvolveInsuranceStateRegIns__c, InvolveInsurance__c, InvolveLoanFinance__c, InvolveNone__c, InvolvePawnBrokerage__c, InvolvePostalService__c, InvolvePreciousMetalsBuy50k__c, InvolvePreciousMetalsSell50k__c, InvolvePreciousMetals__c, InvolveRealEstateClosing__c, SecuritiesFinancialInstitution__c, SecuritiesHowBusinessRegistered__c, SecuritiesCountriesText__c, SecuritiesInvestFundsInternationally__c, SecuritiesProductTypes__c, SecuritiesServiceTypes__c, SecuritiesInvolveSecurities__c, InvolveSecurities__c, InvolveTelegraphCompany__c, InvolveTravelAgency__c, InvolveVehicleTypes__c, InvolveVehicleSales__c,
                
                Money_Service_Business__c, GiftCardBsaAmlProg__c, InvolveCheckTypes__c, InvolveCashChecks__c, Involve_Curency_Exchange_Agent__c, Involve_Curency_Exchange_Principal__c, InvolveCurrencyExchange__c, Gift_Card_Agent__c, Gift_Card_Principal__c, GiftCardActivationProcess__c, GiftCardExceedDailyMax__c, GiftCardReloaded__c, GiftCardRequireCustomerId__c, GiftCardTransferFunds__c, GiftCardTransferFundsInternationally__c, GiftCardAccessDailyMax__c, GiftCardNetworkBranded__c, GiftCardPreventSales__c, InvolveGiftCards__c, Involve_Money_Orders_Agent__c, Involve_Money_Orders_Principal__c, InvolveMoneyOrders__c, Transmit_Money_Agent__c, Transmit_Money_Principal__c, TransmitMoneyActivities__c, TransmitMoneyCvc__c, TransmitForeignCountriesText__c, TransmitMoneyNonUsLocations__c, TransmitMoneyTypes__c, InvolveTransmitMoney__c, InvolveVirtualCurrency__c, RegisteredFinCen__c,
                
                How_do_you_want_to_fund_your_account__c, Business_Savings_5_00_minimum__c, Business_Essential_Checking__c, Total_Deposit__c,
                Account_Type_Checking_or_Savings__c, Name_on_Account__c, External_Account_Number__c, External_Routing_Number__c, Financial_Institution__c, Financial_Institution_State__c,

                CreatedDate
            FROM DAO_Application__c
            WHERE Id = :applicationId
            LIMIT 1
        ];
        
        // Return the list of application details
        return applicationDetails;
    }

    @AuraEnabled(cacheable=true)
    public static List<DAO_Roles__c> getApplicationRoles(String applicationId) {
        // Query to fetch the roles associated with the application
        List<DAO_Roles__c> roles = [
            SELECT 
                Id, Name, Individual_Role__c, Business_Owned__c, Has_Control__c, Is_ID_Attached__c, IsCompleted__c,
                 Different_Mailing_Address__c,isApplicant__c,
                
                First_Name__c, Middle_Name__c, Last_Name__c, Suffix__c,
                SSN_TIN__c, Date_of_Birth__c, Citizenship_Status__c,

                Frequent_Traveler__c, Outside_United_States__c, Which_Countries__c,
                
                Street_Address__c, City__c, State__c, Zipcode__c, 
                Mailing_Address_same_as_Physical__c,
                Mailing_Street_Address__c, Mailing_City__c, Mailing_State__c, Mailing_Zip_code__c,

                Preferred_Contact_Method__c, Email__c, Primary_Home_Phone__c, Work_Phone__c, Secondary_Mobile_Phone__c,

                Employment_Status__c, Profession_Job_Title__c, Employer__c, Gross_Monthly_Income__c, Employment_Duration_Years__c, Employment_Duration_Months__c,
                Profession_MOS__c, Branch_of_Service__c, Pay_Grade__c, Time_in_Service_Years__c, Time_in_Services_Months__c,
                Former_Profession_Job_Title__c, UnEmployment_Duration_Years__c, UnEmployment_Duration_Month__c,
                Business_Type__c, 
                
                ID_Type__c, ID_Number__c, Id_State__c, ID_Country__c, ID_Issued_Date__c, ID_Expiration_Date__c,

                CreatedDate
            FROM DAO_Roles__c
            WHERE DAO_Application__c = :applicationId
        ];
         System.debug('Roles fetched: ' + roles.size());
         for (DAO_Roles__c role : roles) {
             System.debug('Role details: ' + role.Mailing_Address_same_as_Physical__c + ' ' + role.Mailing_Street_Address__c + ' ' + role.Mailing_City__c + ' ' + role.Mailing_State__c + ' ' + role.Mailing_Zip_code__c);
         }
        // Return the list of roles
        return roles;
    }

    @AuraEnabled(cacheable=true)
    public static List<DAO_Products__c> getApplicationProducts(String applicationId) {
        // Query to fetch the products associated with the application
        List<DAO_Products__c> products = [
            SELECT 
                Id, Name, Type_of_Account__c,
                Visa_Debit_Card__c, Online_Banking__c, eStatements__c, Want_Business_Visa_Credit_Card__c,
                Checks_and_Electronic_Transactions_Only__c, All_Overdraft_Pay_Advantage_Services__c, No_Overdraft_Pay_Advantage_Services__c,
                CreatedDate
            FROM DAO_Products__c
            WHERE DAO_Application__c = :applicationId
        ];
        
        // Return the list of products
        return products;
    }
}