<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>DataDotComAccountInsights</excludeButtons>
    <excludeButtons>DataDotComClean</excludeButtons>
    <excludeButtons>DisableCustomerPortalAccount</excludeButtons>
    <excludeButtons>DisablePartnerPortalAccount</excludeButtons>
    <excludeButtons>IncludeOffline</excludeButtons>
    <excludeButtons>Share</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Account Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Mortgage_Preferred_Name__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PersonEmail</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Alternate_Email__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Website</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Real_Estate_License__pc</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>License_Anniversary_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Comments_Notes__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ShippingAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Mortgage_County__c</field>
            </layoutItems>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingAddress</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <emptySpace>true</emptySpace>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Account.ELO_Log_a_call</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.ELO_Task</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>SendEmail</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Account.ELO_Events</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.TextPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.LinkPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.PollPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>10</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>AccountAddToCampaign</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>11</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>StartOutboundConversation</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>12</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>JigsawSearch</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>13</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>GenerateDonorReceipt</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>14</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CreateCallList</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>15</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>AccountHierarchy</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>16</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>XClean</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>17</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>RecordShareHierarchy</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>18</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>EvalAccForResearchStudy</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>19</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>PrintableView</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>20</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>21</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewGiftEntry</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>22</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Entitlements</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>23</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>AssignRecordLabel</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>24</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CreateSalesSummaries</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>25</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CreateAccountChannel</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>26</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangeRecordType</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>27</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>SalesAIRelationshipGraph</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>28</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>ChangeOwnerOne</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>29</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CreatePartnerChannelQuickAction</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>30</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>BrowseCatalogs</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>31</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CreateSurveyInvitation</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>32</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>OpenRelationshipVisualizer</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>33</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>PartnerScorecard</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>34</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>SendSurveyInvitation</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>35</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>AddToActionableList</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>36</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CreateDonorBrief</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>37</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>38</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CallHighlightAction</actionName>
            <actionType>ProductivityAction</actionType>
            <sortOrder>39</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>SmsHighlightAction</actionName>
            <actionType>ProductivityAction</actionType>
            <sortOrder>40</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>EmailHighlightAction</actionName>
            <actionType>ProductivityAction</actionType>
            <sortOrder>41</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>WebsiteHighlightAction</actionName>
            <actionType>ProductivityAction</actionType>
            <sortOrder>42</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList>
        <quickActionListItems>
            <quickActionName>FeedItem.TextPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.ContentPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>LogACall</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>NewTask</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>NewEvent</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.LinkPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.PollPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>SendEmail</quickActionName>
        </quickActionListItems>
    </quickActionList>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>Lead_Account_Relationship__c.Relationship__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>ACCOUNT.NAME</fields>
        <fields>ACCCONRELATION.ROLES</fields>
        <relatedList>RelatedContactAccountRelationList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>TASK.WHAT_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>TASK.CLOSED</fields>
        <fields>TASK.PRIORITY</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.LAST_UPDATE</fields>
        <relatedList>Activity.FinServ__Household__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>et4ae5__Clicked__c</fields>
        <fields>et4ae5__DateOpened__c</fields>
        <fields>et4ae5__DateSent__c</fields>
        <relatedList>et4ae5__IndividualEmailResult__c.et4ae5__Contact__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>LEAD.COMPANY</fields>
        <fields>LEAD.PHONE</fields>
        <relatedList>Lead.ReferredBy__c</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hUO000005tRfr</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
