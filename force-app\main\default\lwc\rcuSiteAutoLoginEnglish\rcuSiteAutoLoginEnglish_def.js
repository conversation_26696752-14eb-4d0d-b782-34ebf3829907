export const OMNIDEF = {"userTimeZone":-420,"userProfile":"System Administrator","userName":"<EMAIL>","userId":"005U8000003otzDIAQ","userCurrencyCode":"USD","timeStamp":"2025-04-28T23:25:53.813Z","sOmniScriptId":"0jNU8000000AsjtMAC","sobjPL":{},"RPBundle":"","rMap":{},"response":null,"propSetMap":{"currentLanguage":"en_US","scrollBehavior":"auto","disableUnloadWarn":true,"stepChartPlacement":"right","stylesheet":{"lightningRtl":"","newportRtl":"","lightning":"daoApplicationCSSV2","newport":""},"errorMessage":{"custom":[]},"consoleTabIcon":"custom:custom18","consoleTabTitle":null,"rtpSeed":false,"showInputWidth":false,"currencyCode":"","autoFocus":false,"pubsub":false,"message":{},"ssm":false,"wpm":false,"consoleTabLabel":"New","cancelRedirectTemplateUrl":"vlcCancelled.html","cancelRedirectPageName":"OmniScriptCancelled","cancelSource":"%ContextId%","allowCancel":true,"cancelType":"SObject","visualforcePagesAvailableInPreview":{},"mergeSavedData":false,"hideStepChart":true,"timeTracking":false,"knowledgeArticleTypeQueryFieldsMap":{},"lkObjName":null,"bLK":false,"enableKnowledge":false,"trackingCustomData":{},"seedDataJSON":{},"elementTypeToHTMLTemplateMapping":{},"autoSaveOnStepNext":false,"saveURLPatterns":{},"saveObjectId":"%ContextId%","saveContentEncoded":false,"saveForLaterRedirectTemplateUrl":"vlcSaveForLaterAcknowledge.html","saveForLaterRedirectPageName":"sflRedirect","saveExpireInDays":null,"saveNameTemplate":null,"allowSaveForLater":false,"persistentComponent":[{"modalConfigurationSetting":{"modalSize":"lg","modalController":"ModalProductCtrl","modalHTMLTemplateId":"vlcProductConfig.html"},"itemsKey":"cartItems","id":"vlcCart","responseJSONNode":"","responseJSONPath":"","sendJSONNode":"","sendJSONPath":"","postTransformBundle":"","preTransformBundle":"","remoteOptions":{"postTransformBundle":"","preTransformBundle":""},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","label":"","render":false},{"modalConfigurationSetting":{"modalSize":"lg","modalController":"","modalHTMLTemplateId":""},"itemsKey":"knowledgeItems","id":"vlcKnowledge","postTransformBundle":"","preTransformBundle":"","remoteOptions":{"postTransformBundle":"","preTransformBundle":""},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","label":"","dispOutsideOmni":false,"render":false}]},"prefillJSON":"{}","lwcId":"6cf237ea-9193-85aa-062a-1095ac4ae218","labelMap":{"userVerificationLastFour":"userVerification:personalInformationBlock:userVerificationLastFour","userVerificationDOB":"userVerification:personalInformationBlock:userVerificationDOB","userVerificationEmail":"userVerification:personalInformationBlock:userVerificationEmail","LineBreak4":"continueApplicationGeneric:LineBreak4","TextBlock6":"continueApplicationGeneric:TextBlock6","Block1":"continueApplicationGeneric:Block1","LineBreak3":"continueApplicationGeneric:LineBreak3","TextBlock5":"continueApplicationGeneric:TextBlock5","TextBlock4":"userNotFound:TextBlock4","formulaValidateTokenInput":"verifyPasscode:formulaValidateTokenInput","verifyTokenInput":"verifyPasscode:verifyTokenInput","verifyPasscodeText":"verifyPasscode:verifyPasscodeText","token":"userVerification:token","personalInformationBlock":"userVerification:personalInformationBlock","d-formulaSetContextId":"newOrExisitngApplication:d-formulaSetContextId","isNewApplication":"newOrExisitngApplication:isNewApplication","selectNewOrExisting":"newOrExisitngApplication:selectNewOrExisting","daoSiteUserLoginLWC":"customLWCUserLoginStep:daoSiteUserLoginLWC","WelcomeMessage":"welcome:WelcomeMessage","redirectUserToAuthenticatedSite":"redirectUserToAuthenticatedSite","customLWCUserLoginStep":"customLWCUserLoginStep","setContextId":"setContextId","continueApplicationGeneric":"continueApplicationGeneric","setTokenError":"setTokenError","verifyPasscode":"verifyPasscode","sendVerificationToken":"sendVerificationToken","userNotFound":"userNotFound","getUserByApplicationRemote":"getUserByApplicationRemote","userVerification":"userVerification","newOrExisitngApplication":"newOrExisitngApplication","welcome":"welcome","getApplicantRoleInfo":"getApplicantRoleInfo","applicantDecodedId":"applicantDecodedId"},"labelKeyMap":{},"errorMsg":"","error":"OK","dMap":{"CACEAIAGA7726fdc901664e05bbcf1a94b5d5999e":"015U8000002AWtWIAW","CACEAIAGA1deffb0b512c4960a5ffdbd39feea724":"015U8000002AY8vIAG"},"depSOPL":{},"depCusPL":{},"cusPL":{},"children":[{"type":"Remote Action","propSetMap":{"sendOnlyExtraPayload":true,"businessEvent":"","businessCategory":"","useContinuation":false,"enableActionMessage":false,"enableDefaultAbort":false,"errorMessage":{"default":null,"custom":[]},"svgIcon":"","svgSprite":"","pubsub":false,"message":{},"ssm":false,"wpm":false,"HTMLTemplateId":"","show":{"group":{"rules":[{"field":"ContextId","condition":"<>","data":null}],"operator":"AND"}},"showPersistentComponent":[true,false],"redirectPreviousWidth":3,"redirectPreviousLabel":"Previous","redirectNextWidth":3,"redirectNextLabel":"Next","redirectTemplateUrl":"vlcAcknowledge.html","redirectPageName":"","validationRequired":"Step","failureAbortMessage":"Are you sure?","failureGoBackLabel":"Go Back","failureAbortLabel":"Abort","failureNextLabel":"Continue","postMessage":"Done","inProgressMessage":"In Progress","extraPayload":{"ContextId":"%ContextId%"},"responseJSONNode":"","responseJSONPath":"","sendJSONNode":"","sendJSONPath":"","postTransformBundle":"","preTransformBundle":"","remoteTimeout":30000,"remoteOptions":{"postTransformBundle":"","preTransformBundle":""},"remoteMethod":"decodeUserId","remoteClass":"DAORemote","label":"applicantDecodedId","controlWidth":12,"aggElements":{}},"offSet":0,"name":"applicantDecodedId","level":0,"indexInParent":0,"bHasAttachment":false,"bEmbed":false,"bRemoteAction":true,"JSONPath":"applicantDecodedId","lwcId":"lwc0"},{"type":"DataRaptor Extract Action","propSetMap":{"businessEvent":"","businessCategory":"","enableActionMessage":false,"enableDefaultAbort":false,"errorMessage":{"default":null,"custom":[]},"pubsub":false,"message":{},"ssm":false,"wpm":false,"HTMLTemplateId":"","show":{"group":{"rules":[{"field":"ContextId","condition":"<>","data":null}],"operator":"AND"}},"showPersistentComponent":[true,false],"redirectPreviousWidth":3,"redirectPreviousLabel":"Previous","redirectNextWidth":3,"redirectNextLabel":"Next","redirectTemplateUrl":"vlcAcknowledge.html","redirectPageName":"","validationRequired":"Step","failureAbortMessage":"Are you sure?","failureGoBackLabel":"Go Back","failureAbortLabel":"Abort","failureNextLabel":"Continue","postMessage":"Done","inProgressMessage":"In Progress","responseJSONNode":"","responseJSONPath":"","remoteTimeout":30000,"dataRaptor Input Parameters":[{"inputParam":"roleId","element":"Id"}],"ignoreCache":false,"bundle":"DRGetApplicationAndRolesById","label":"getApplicantRoleInfo","controlWidth":12,"aggElements":{}},"offSet":0,"name":"getApplicantRoleInfo","level":0,"indexInParent":1,"bHasAttachment":false,"bEmbed":false,"bDataRaptorExtractAction":true,"JSONPath":"getApplicantRoleInfo","lwcId":"lwc1"},{"type":"Step","propSetMap":{"businessEvent":"","businessCategory":"","pubsub":false,"message":{},"ssm":false,"wpm":false,"errorMessage":{"default":null,"custom":[]},"allowSaveForLater":false,"chartLabel":"Welcome","instructionKey":"","HTMLTemplateId":"","conditionType":"Hide if False","show":{"group":{"rules":[{"field":"ContextId","condition":"=","data":null}],"operator":"AND"}},"knowledgeOptions":{"typeFilter":"","remoteTimeout":30000,"dataCategoryCriteria":"","keyword":"","publishStatus":"Online","language":"English"},"remoteOptions":{},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","showPersistentComponent":[true,false],"instruction":"","completeMessage":"Are you sure you want to complete the script?","completeLabel":"Complete","saveMessage":"Are you sure you want to save it for later?","saveLabel":"Save for later","cancelMessage":"Are you sure?","cancelLabel":"Cancel","nextWidth":"6","nextLabel":"Open a Business Account","previousWidth":3,"previousLabel":"Previous","validationRequired":true,"label":"","uiElements":{"welcome":""},"aggElements":{}},"offSet":0,"name":"welcome","level":0,"indexInParent":2,"bHasAttachment":false,"bEmbed":false,"response":null,"inheritShowProp":null,"children":[{"response":null,"level":1,"indexInParent":0,"eleArray":[{"type":"Text Block","rootIndex":2,"response":null,"propSetMap":{"sanitize":false,"textKey":"","HTMLTemplateId":"","dataJSON":false,"show":null,"text":"<p><img src=\"/servlet/servlet.ImageServer?id=015U8000002AWtWIAW&amp;docName=CACEAIAGA7726fdc901664e05bbcf1a94b5d5999e&amp;oid=00DU8000000o2kz\" alt=\"\" width=\"147\" height=\"59\" /></p>\n<p><img src=\"/servlet/servlet.ImageServer?id=015U8000002AY8vIAG&amp;docName=CACEAIAGA1deffb0b512c4960a5ffdbd39feea724&amp;oid=00DU8000000o2kz\" alt=\"\" width=\"1730\" height=\"610\" /></p>","label":"TextBlock1","controlWidth":12},"name":"WelcomeMessage","level":1,"JSONPath":"welcome:WelcomeMessage","indexInParent":0,"index":0,"children":[],"bHasAttachment":false,"bTextBlock":true,"lwcId":"lwc20-0"}],"bHasAttachment":false}],"bAccordionOpen":false,"bAccordionActive":false,"bStep":true,"isStep":true,"JSONPath":"welcome","lwcId":"lwc2"},{"type":"Step","propSetMap":{"businessEvent":"","businessCategory":"","pubsub":false,"message":{},"ssm":false,"wpm":false,"errorMessage":{"default":null,"custom":[]},"allowSaveForLater":true,"chartLabel":"Select Existing or New Application","instructionKey":"","HTMLTemplateId":"","conditionType":"Hide if False","show":{"group":{"rules":[{"field":"ContextId","condition":"=","data":null}],"operator":"AND"}},"knowledgeOptions":{"typeFilter":"","remoteTimeout":30000,"dataCategoryCriteria":"","keyword":"","publishStatus":"Online","language":"English"},"remoteOptions":{},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","showPersistentComponent":[true,false],"instruction":"","completeMessage":"Are you sure you want to complete the script?","completeLabel":"Complete","saveMessage":"Are you sure you want to save it for later?","saveLabel":"Save for later","cancelMessage":"Are you sure?","cancelLabel":"Cancel","nextWidth":3,"nextLabel":"Next","previousWidth":3,"previousLabel":"Previous","validationRequired":true,"label":"","uiElements":{"newOrExisitngApplication":"","selectNewOrExisting":""},"aggElements":{"isNewApplication":"","d-formulaSetContextId":""}},"offSet":0,"name":"newOrExisitngApplication","level":0,"indexInParent":3,"bHasAttachment":false,"bEmbed":false,"response":null,"inheritShowProp":null,"children":[{"response":null,"level":1,"indexInParent":0,"eleArray":[{"type":"Radio","rootIndex":3,"response":null,"propSetMap":{"disOnTplt":false,"enableCaption":true,"imageCountInRow":3,"optionHeight":100,"optionWidth":100,"hide":false,"HTMLTemplateId":"","accessibleInFutureSteps":false,"conditionType":"Hide if False","show":null,"controllingField":{"source":"","type":"","element":""},"optionSource":{"source":"","type":""},"options":[{"autoAdv":null,"value":"Starting a new application","developerName":null,"name":"true"},{"autoAdv":null,"value":"Continuing an existing application","developerName":null,"name":"false"}],"helpTextPos":"","helpText":"","help":false,"defaultValue":null,"horizontalMode":false,"readOnly":false,"repeatLimit":null,"repeatClone":false,"repeat":false,"required":false,"label":"Are you starting a new application or continuing an existing one?","controlWidth":12},"name":"selectNewOrExisting","level":1,"JSONPath":"newOrExisitngApplication:selectNewOrExisting","indexInParent":0,"index":0,"children":[],"bHasAttachment":false,"bRadio":true,"lwcId":"lwc30-0"}],"bHasAttachment":false},{"response":null,"level":1,"indexInParent":1,"eleArray":[{"type":"Formula","rootIndex":3,"response":null,"propSetMap":{"controlWidth":12,"label":"isNewApplication","showInputWidth":false,"inputWidth":12,"expression":"IF( %newOrExisitngApplication:selectNewOrExisting% == \"true\", true, false)","hide":true,"show":null,"mask":null,"dataType":null,"hideGroupSep":false,"dateFormat":"MM-dd-yyyy","HTMLTemplateId":"","disOnTplt":false},"name":"isNewApplication","level":1,"JSONPath":"newOrExisitngApplication:isNewApplication","indexInParent":1,"index":0,"children":[],"bHasAttachment":false,"bFormula":true,"lwcId":"lwc31-0"}],"bHasAttachment":false},{"response":null,"level":1,"indexInParent":2,"eleArray":[{"type":"Formula","rootIndex":3,"response":null,"propSetMap":{"disOnTplt":false,"HTMLTemplateId":"","dateFormat":"MM-dd-yyyy","hideGroupSep":false,"dataType":null,"mask":null,"show":null,"hide":true,"expression":"IF( %newOrExisitngApplication:isNewApplication% == true , \"new\", \"existing\")","inputWidth":12,"showInputWidth":false,"label":"formulaSetContextId","controlWidth":12},"name":"d-formulaSetContextId","level":1,"JSONPath":"newOrExisitngApplication:d-formulaSetContextId","indexInParent":2,"index":0,"children":[],"bHasAttachment":false,"bFormula":true,"lwcId":"lwc32-0"}],"bHasAttachment":false}],"bAccordionOpen":false,"bAccordionActive":false,"bStep":true,"isStep":true,"JSONPath":"newOrExisitngApplication","lwcId":"lwc3"},{"type":"Step","propSetMap":{"businessEvent":"","businessCategory":"","pubsub":false,"message":{},"ssm":false,"wpm":false,"errorMessage":{"default":null,"custom":[]},"allowSaveForLater":true,"chartLabel":"Personal Information","instructionKey":"","HTMLTemplateId":"","conditionType":"Hide if False","show":{"group":{"rules":[{"field":"isNewApplication","condition":"=","data":"false"},{"data":null,"condition":"=","field":"ContextId"}],"operator":"AND"}},"knowledgeOptions":{"typeFilter":"","remoteTimeout":30000,"dataCategoryCriteria":"","keyword":"","publishStatus":"Online","language":"English"},"remoteOptions":{},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","showPersistentComponent":[true,false],"instruction":"","completeMessage":"Are you sure you want to complete the script?","completeLabel":"Complete","saveMessage":"Are you sure you want to save it for later?","saveLabel":"Save for later","cancelMessage":"Are you sure?","cancelLabel":"Cancel","nextWidth":3,"nextLabel":"Next","previousWidth":3,"previousLabel":"Previous","validationRequired":true,"label":"","uiElements":{"userVerification":"","userVerificationEmail":"","userVerificationDOB":"","userVerificationLastFour":"","personalInformationBlock":""},"aggElements":{"token":""}},"offSet":0,"name":"userVerification","level":0,"indexInParent":4,"bHasAttachment":false,"bEmbed":false,"response":null,"inheritShowProp":null,"children":[{"response":null,"level":1,"indexInParent":0,"eleArray":[{"type":"Block","rootIndex":4,"response":null,"propSetMap":{"bus":true,"hide":false,"HTMLTemplateId":"","accessibleInFutureSteps":false,"conditionType":"Hide if False","show":null,"repeatLimit":null,"repeatClone":false,"repeat":false,"collapse":false,"label":"Please enter the following information","controlWidth":12},"name":"personalInformationBlock","level":1,"JSONPath":"userVerification:personalInformationBlock","indexInParent":0,"index":0,"children":[{"response":null,"level":2,"indexInParent":0,"eleArray":[{"type":"Email","rootIndex":4,"response":null,"propSetMap":{"autocomplete":null,"disOnTplt":false,"hide":false,"HTMLTemplateId":"","debounceValue":0,"accessibleInFutureSteps":false,"conditionType":"Hide if False","show":null,"placeholder":"","ptrnErrText":"","pattern":"","helpTextPos":"","helpText":"","help":false,"defaultValue":null,"readOnly":false,"repeatLimit":null,"repeatClone":false,"repeat":false,"required":true,"inputWidth":12,"showInputWidth":false,"label":"Email","controlWidth":12},"name":"userVerificationEmail","level":2,"JSONPath":"userVerification:personalInformationBlock|1:userVerificationEmail","indexInParent":0,"index":0,"children":[],"bHasAttachment":false,"bEmail":true,"lwcId":"lwc4000-0"}],"bHasAttachment":false},{"response":null,"level":2,"indexInParent":1,"eleArray":[{"type":"Date","rootIndex":4,"response":null,"propSetMap":{"maxDate":"","minDate":"","disOnTplt":false,"hide":false,"HTMLTemplateId":"","accessibleInFutureSteps":false,"conditionType":"Hide if False","show":null,"dateFormat":"MM-dd-yyyy","modelDateFormat":"yyyy-MM-dd","dateType":"string","helpTextPos":"","helpText":"","help":false,"defaultValue":null,"readOnly":false,"repeatLimit":null,"repeatClone":false,"repeat":false,"required":true,"inputWidth":12,"showInputWidth":false,"label":"Date of Birth (Example: 09-05-2005)","controlWidth":12},"name":"userVerificationDOB","level":2,"JSONPath":"userVerification:personalInformationBlock|1:userVerificationDOB","indexInParent":1,"index":0,"children":[],"bHasAttachment":false,"bDate":true,"lwcId":"lwc4001-0"}],"bHasAttachment":false},{"response":null,"level":2,"indexInParent":2,"eleArray":[{"type":"Text","rootIndex":4,"response":null,"propSetMap":{"autocomplete":null,"disOnTplt":false,"hide":false,"HTMLTemplateId":"","debounceValue":0,"accessibleInFutureSteps":false,"conditionType":"Hide if False","show":null,"placeholder":"","maxLength":4,"minLength":4,"ptrnErrText":"","pattern":"","mask":"","helpTextPos":"","helpText":"","help":false,"defaultValue":null,"readOnly":false,"repeatLimit":null,"repeatClone":false,"repeat":false,"required":true,"inputWidth":12,"showInputWidth":false,"label":"Last 4 digits of your SSN","controlWidth":12},"name":"userVerificationLastFour","level":2,"JSONPath":"userVerification:personalInformationBlock|1:userVerificationLastFour","indexInParent":2,"index":0,"children":[],"bHasAttachment":false,"bText":true,"lwcId":"lwc4002-0"}],"bHasAttachment":false}],"bHasAttachment":false,"bBlock":true,"lwcId":"lwc40-0"}],"bHasAttachment":false},{"response":null,"level":1,"indexInParent":1,"eleArray":[{"type":"Formula","rootIndex":4,"response":null,"propSetMap":{"disOnTplt":false,"HTMLTemplateId":"","dateFormat":"MM-dd-yyyy","hideGroupSep":false,"dataType":"Text","mask":null,"show":null,"hide":true,"expression":"ROUND(RANDOM(4)*100000)","inputWidth":12,"showInputWidth":false,"label":"token","controlWidth":12},"name":"token","level":1,"JSONPath":"userVerification:token","indexInParent":1,"index":0,"children":[],"bHasAttachment":false,"bFormula":true,"lwcId":"lwc41-0"}],"bHasAttachment":false}],"bAccordionOpen":false,"bAccordionActive":false,"bStep":true,"isStep":true,"JSONPath":"userVerification","lwcId":"lwc4"},{"type":"Remote Action","propSetMap":{"sendOnlyExtraPayload":true,"businessEvent":"","businessCategory":"","useContinuation":false,"enableActionMessage":false,"enableDefaultAbort":false,"errorMessage":{"default":null,"custom":[]},"svgIcon":"","svgSprite":"","pubsub":false,"message":{},"ssm":false,"wpm":false,"HTMLTemplateId":"","show":{"group":{"rules":[{"field":"ContextId","condition":"=","data":null}],"operator":"AND"}},"showPersistentComponent":[true,false],"redirectPreviousWidth":3,"redirectPreviousLabel":"Previous","redirectNextWidth":3,"redirectNextLabel":"Next","redirectTemplateUrl":"vlcAcknowledge.html","redirectPageName":"","validationRequired":"Step","failureAbortMessage":"Are you sure?","failureGoBackLabel":"Go Back","failureAbortLabel":"Abort","failureNextLabel":"Continue","postMessage":"Done","inProgressMessage":"In Progress","extraPayload":{"email":"%userVerification:personalInformationBlock:userVerificationEmail%"},"responseJSONNode":"","responseJSONPath":"","sendJSONNode":"","sendJSONPath":"","postTransformBundle":"","preTransformBundle":"","remoteTimeout":30000,"remoteOptions":{"postTransformBundle":"","preTransformBundle":""},"remoteMethod":"isExistingUser","remoteClass":"DAORemote","label":"getUserByApplicationRemote","controlWidth":12,"aggElements":{}},"offSet":0,"name":"getUserByApplicationRemote","level":0,"indexInParent":5,"bHasAttachment":false,"bEmbed":false,"bRemoteAction":true,"JSONPath":"getUserByApplicationRemote","lwcId":"lwc5"},{"type":"Step","propSetMap":{"businessEvent":"","businessCategory":"","pubsub":false,"message":{},"ssm":false,"wpm":false,"errorMessage":{"default":null,"custom":[]},"allowSaveForLater":true,"chartLabel":"User Not Found","instructionKey":"","HTMLTemplateId":"","conditionType":"Hide if False","show":{"group":{"rules":[{"field":"UserFound","condition":"=","data":"false"},{"data":"false","condition":"=","field":"isNewApplication"},{"data":null,"condition":"=","field":"ContextId"}],"operator":"AND"}},"knowledgeOptions":{"typeFilter":"","remoteTimeout":30000,"dataCategoryCriteria":"","keyword":"","publishStatus":"Online","language":"English"},"remoteOptions":{},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","showPersistentComponent":[true,false],"instruction":"","completeMessage":"Are you sure you want to complete the script?","completeLabel":"Complete","saveMessage":"Are you sure you want to save it for later?","saveLabel":"Save for later","cancelMessage":"Are you sure?","cancelLabel":"Cancel","nextWidth":3,"nextLabel":"Next","previousWidth":3,"previousLabel":"Previous","validationRequired":true,"label":"","uiElements":{"userNotFound":""},"aggElements":{}},"offSet":0,"name":"userNotFound","level":0,"indexInParent":6,"bHasAttachment":false,"bEmbed":false,"response":null,"inheritShowProp":null,"children":[{"response":null,"level":1,"indexInParent":0,"eleArray":[{"type":"Text Block","rootIndex":6,"response":null,"propSetMap":{"sanitize":false,"textKey":"","HTMLTemplateId":"","dataJSON":false,"show":null,"text":"<h3><span style=\"color: #3598db;\">&nbsp;It looks like the email&nbsp; %userVerification:personalInformationBlock:userVerificationEmail% was not found, please go back try a different email or start a new application.&nbsp;</span></h3>","label":"TextBlock4","controlWidth":12},"name":"TextBlock4","level":1,"JSONPath":"userNotFound:TextBlock4","indexInParent":0,"index":0,"children":[],"bHasAttachment":false,"bTextBlock":true,"lwcId":"lwc60-0"}],"bHasAttachment":false}],"bAccordionOpen":false,"bAccordionActive":false,"bStep":true,"isStep":true,"JSONPath":"userNotFound","lwcId":"lwc6"},{"type":"Email Action","propSetMap":{"businessEvent":"","businessCategory":"","enableActionMessage":false,"enableDefaultAbort":false,"errorMessage":{"default":null,"custom":[]},"pubsub":false,"message":{},"ssm":false,"wpm":false,"HTMLTemplateId":"","show":{"group":{"rules":[{"field":"UserFound","condition":"=","data":"true"},{"data":"false","condition":"=","field":"isNewApplication"},{"data":null,"condition":"=","field":"ContextId"}],"operator":"AND"}},"showPersistentComponent":[true,false],"redirectPreviousWidth":3,"redirectPreviousLabel":"Previous","redirectNextWidth":3,"redirectNextLabel":"Next","redirectTemplateUrl":"vlcAcknowledge.html","redirectPageName":"","validationRequired":"Step","failureAbortMessage":"Are you sure?","failureGoBackLabel":"Go Back","failureAbortLabel":"Abort","failureNextLabel":"Continue","postMessage":"Done","inProgressMessage":"In Progress","remoteTimeout":30000,"docList":"","staticDocList":[],"contentVersionList":"","attachmentList":"","fileAttachments":"","OrgWideEmailAddress":"<EMAIL>","emailInformation":{"setHtmlBody":true,"emailBody":"<body style=\"font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 0;\">\n\n    <div style=\"background-color: #ffffff; padding: 20px; margin: 20px auto; max-width: 600px; border-radius: 8px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\">\n        <h1 style=\"color: #333333;\">Two-Factor Authentication (2FA) Required</h1>\n    <!--    <p>Hello, %PersonalInfo:personalInfoBlock:Email%</p> -->\n\t\t\n        <p>To continue securely with your Redwood Credit Union business application, please enter the verification code below.</p>\n    \n        <div style=\"background-color: #e8f4ff; padding: 15px; margin: 20px 0; border-left: 4px solid #007bff; font-size: 24px; text-align: center; font-weight: bold; letter-spacing: 2px; color: #333333;\">\n            <!-- Insert the dynamically generated token here -->\n            %userVerification:token%\n        </div>\n    \n        <p>If you did not request this verification code, please disregard this message.</p>\n\t\t<p>If you need additional assistance, please call our support team at (707)576-5070, Monday through Friday 9 a.m. to 5 p.m.</p>\n\t\t<p>Best regards,</p>\n\t\t<p>Redwood Credit Union</p>\n    \n    <!--\n        <p style=\"text-align: center; color: #999999; font-size: 12px; margin-top: 30px;\">If you need any assistance, please contact our support team at <a href=\"mailto:<EMAIL>\"><EMAIL></a>.</p>\n\t--> \n\t\n    </div>\n\n</body>","emailSubject":"Your Verification Token","bccAddressList":[],"ccAddressList":[],"toAddressList":["%userVerification:personalInformationBlock:userVerificationEmail%"]},"emailTemplateInformation":{"whatId":"","saveAsActivity":false,"emailTargetObjectId":"","emailTemplateName":""},"useTemplate":false,"label":"sendVerificationToken","controlWidth":12,"aggElements":{}},"offSet":0,"name":"sendVerificationToken","level":0,"indexInParent":7,"bHasAttachment":false,"bEmbed":false,"bEmailAction":true,"JSONPath":"sendVerificationToken","lwcId":"lwc7"},{"type":"Step","propSetMap":{"businessEvent":"","businessCategory":"","pubsub":false,"message":{},"ssm":false,"wpm":false,"errorMessage":{"default":null,"custom":[]},"allowSaveForLater":true,"chartLabel":null,"instructionKey":"","HTMLTemplateId":"","conditionType":"Hide if False","show":{"group":{"rules":[{"field":"isNewApplication","condition":"=","data":"false"},{"data":"true","condition":"=","field":"UserFound"},{"data":null,"condition":"=","field":"ContextId"}],"operator":"AND"}},"knowledgeOptions":{"typeFilter":"","remoteTimeout":30000,"dataCategoryCriteria":"","keyword":"","publishStatus":"Online","language":"English"},"remoteOptions":{},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","showPersistentComponent":[true,false],"instruction":"","completeMessage":"Are you sure you want to complete the script?","completeLabel":"Complete","saveMessage":"Are you sure you want to save it for later?","saveLabel":"Save for later","cancelMessage":"Are you sure?","cancelLabel":"Cancel","nextWidth":3,"nextLabel":"Next","previousWidth":3,"previousLabel":"Previous","validationRequired":true,"label":"Verify Passcode","uiElements":{"verifyPasscode":"","verifyTokenInput":""},"aggElements":{"formulaValidateTokenInput":""}},"offSet":0,"name":"verifyPasscode","level":0,"indexInParent":8,"bHasAttachment":false,"bEmbed":false,"response":null,"inheritShowProp":null,"children":[{"response":null,"level":1,"indexInParent":0,"eleArray":[{"type":"Text Block","rootIndex":8,"response":null,"propSetMap":{"sanitize":false,"textKey":"","HTMLTemplateId":"","dataJSON":false,"show":null,"text":"<h4><span style=\"color: #236fa1;\">Please enter the code sent to your email, if you have not received it please go back and re enter your personal information</span></h4>","label":"TextBlock4","controlWidth":12},"name":"verifyPasscodeText","level":1,"JSONPath":"verifyPasscode:verifyPasscodeText","indexInParent":0,"index":0,"children":[],"bHasAttachment":false,"bTextBlock":true,"lwcId":"lwc80-0"}],"bHasAttachment":false},{"response":null,"level":1,"indexInParent":1,"eleArray":[{"type":"Text","rootIndex":8,"response":null,"propSetMap":{"autocomplete":null,"disOnTplt":false,"hide":false,"HTMLTemplateId":"","debounceValue":0,"accessibleInFutureSteps":false,"conditionType":"Hide if False","show":null,"placeholder":"","maxLength":255,"minLength":0,"ptrnErrText":"","pattern":"","mask":"","helpTextPos":"","helpText":"","help":false,"defaultValue":null,"readOnly":false,"repeatLimit":null,"repeatClone":false,"repeat":false,"required":false,"inputWidth":12,"showInputWidth":false,"label":"","controlWidth":12},"name":"verifyTokenInput","level":1,"JSONPath":"verifyPasscode:verifyTokenInput","indexInParent":1,"index":0,"children":[],"bHasAttachment":false,"bText":true,"lwcId":"lwc81-0"}],"bHasAttachment":false},{"response":null,"level":1,"indexInParent":2,"eleArray":[{"type":"Formula","rootIndex":8,"response":null,"propSetMap":{"disOnTplt":false,"HTMLTemplateId":"","dateFormat":"MM-dd-yyyy","hideGroupSep":false,"dataType":null,"mask":null,"show":null,"hide":true,"expression":"IF( %userVerification:token% == %verifyPasscode:verifyTokenInput%, true, false)","inputWidth":12,"showInputWidth":false,"label":"formulaValidateTokenInput","controlWidth":12},"name":"formulaValidateTokenInput","level":1,"JSONPath":"verifyPasscode:formulaValidateTokenInput","indexInParent":2,"index":0,"children":[],"bHasAttachment":false,"bFormula":true,"lwcId":"lwc82-0"}],"bHasAttachment":false}],"bAccordionOpen":false,"bAccordionActive":false,"bStep":true,"isStep":true,"JSONPath":"verifyPasscode","lwcId":"lwc8"},{"type":"Set Errors","propSetMap":{"pubsub":false,"message":{},"ssm":false,"wpm":false,"HTMLTemplateId":"","show":{"group":{"rules":[{"field":"formulaValidateTokenInput","condition":"=","data":"false"},{"data":"false","condition":"=","field":"isNewApplication"},{"data":"true","condition":"=","field":"UserFound"},{"data":null,"condition":"=","field":"ContextId"}],"operator":"AND"}},"showPersistentComponent":[true,false],"elementErrorMap":{"verifyTokenInput":"Oops, the token entered is not valid."},"validationRequired":"Step","label":"setTokenError","controlWidth":12,"aggElements":{}},"offSet":0,"name":"setTokenError","level":0,"indexInParent":9,"bHasAttachment":false,"bEmbed":false,"bSetErrors":true,"JSONPath":"setTokenError","lwcId":"lwc9"},{"type":"Step","propSetMap":{"businessEvent":"","businessCategory":"","pubsub":false,"message":{},"ssm":false,"wpm":false,"errorMessage":{"default":null,"custom":[]},"allowSaveForLater":false,"chartLabel":null,"instructionKey":"","HTMLTemplateId":"","conditionType":"Hide if False","show":{"group":{"operator":"AND","rules":[{"data":null,"condition":"<>","field":"ContextId"}]}},"knowledgeOptions":{"typeFilter":"","remoteTimeout":30000,"dataCategoryCriteria":"","keyword":"","publishStatus":"Online","language":"English"},"remoteOptions":{},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","showPersistentComponent":[true,false],"instruction":"","completeMessage":"Are you sure you want to complete the script?","completeLabel":"Complete","saveMessage":"Are you sure you want to save it for later?","saveLabel":"Save for later","cancelMessage":"Are you sure?","cancelLabel":"Cancel","nextWidth":"6","nextLabel":"Continue Application","previousWidth":"0","previousLabel":"Previous","validationRequired":true,"label":"","uiElements":{"continueApplicationGeneric":"","Block1":""},"aggElements":{}},"offSet":0,"name":"continueApplicationGeneric","level":0,"indexInParent":10,"bHasAttachment":false,"bEmbed":false,"response":null,"inheritShowProp":null,"children":[{"response":null,"level":1,"indexInParent":0,"eleArray":[{"type":"Text Block","rootIndex":10,"response":null,"propSetMap":{"sanitize":false,"textKey":"","HTMLTemplateId":"","dataJSON":false,"show":null,"text":"<p><img src=\"/servlet/servlet.ImageServer?id=015U8000002AWtWIAW&amp;docName=CACEAIAGA7726fdc901664e05bbcf1a94b5d5999e&amp;oid=00DU8000000o2kz\" alt=\"\" width=\"147\" height=\"59\" /></p>\n<h3 style=\"text-align: center;\">&nbsp;</h3>","label":"TextBlock1","controlWidth":12},"name":"TextBlock5","level":1,"JSONPath":"continueApplicationGeneric:TextBlock5","indexInParent":0,"index":0,"children":[],"bHasAttachment":false,"bTextBlock":true,"lwcId":"lwc100-0"}],"bHasAttachment":false},{"response":null,"level":1,"indexInParent":1,"eleArray":[{"type":"Line Break","rootIndex":10,"response":null,"propSetMap":{"label":"LineBreak1","padding":40,"show":null,"HTMLTemplateId":""},"name":"LineBreak3","level":1,"JSONPath":"continueApplicationGeneric:LineBreak3","indexInParent":1,"index":0,"children":[],"bHasAttachment":false,"bLineBreak":true,"lwcId":"lwc101-0"}],"bHasAttachment":false},{"response":null,"level":1,"indexInParent":2,"eleArray":[{"type":"Block","rootIndex":10,"response":null,"propSetMap":{"bus":true,"controlWidth":2,"label":"","collapse":false,"repeat":false,"repeatClone":false,"repeatLimit":null,"show":null,"conditionType":"Hide if False","accessibleInFutureSteps":false,"HTMLTemplateId":"","hide":false},"name":"Block1","level":1,"JSONPath":"continueApplicationGeneric:Block1","indexInParent":2,"index":0,"children":[],"bHasAttachment":false,"bBlock":true,"lwcId":"lwc102-0"}],"bHasAttachment":false},{"response":null,"level":1,"indexInParent":3,"eleArray":[{"type":"Text Block","rootIndex":10,"response":null,"propSetMap":{"controlWidth":9,"label":"TextBlock1","text":"<h3 style=\"text-align: center;\">&nbsp;</h3>\n<h3 style=\"text-align: center;\"><span style=\"color: #169179;\">Please click <span style=\"color: #236fa1;\">Continue Application</span> to proceed.&nbsp;</span></h3>","show":null,"dataJSON":false,"HTMLTemplateId":"","textKey":"","sanitize":false},"name":"TextBlock6","level":1,"JSONPath":"continueApplicationGeneric:TextBlock6","indexInParent":3,"index":0,"children":[],"bHasAttachment":false,"bTextBlock":true,"lwcId":"lwc103-0"}],"bHasAttachment":false},{"response":null,"level":1,"indexInParent":4,"eleArray":[{"type":"Line Break","rootIndex":10,"response":null,"propSetMap":{"HTMLTemplateId":"","show":null,"padding":80,"label":"LineBreak1"},"name":"LineBreak4","level":1,"JSONPath":"continueApplicationGeneric:LineBreak4","indexInParent":4,"index":0,"children":[],"bHasAttachment":false,"bLineBreak":true,"lwcId":"lwc104-0"}],"bHasAttachment":false}],"bAccordionOpen":false,"bAccordionActive":false,"bStep":true,"isStep":true,"JSONPath":"continueApplicationGeneric","lwcId":"lwc10"},{"type":"Set Values","propSetMap":{"pubsub":false,"message":{},"ssm":false,"wpm":false,"HTMLTemplateId":"","show":{"group":{"rules":[{"field":"ContextId","condition":"=","data":null}],"operator":"AND"}},"showPersistentComponent":[true,false],"elementValueMap":{"ContextId":"%userVerification:personalInformationBlock:userVerificationEmail%"},"label":"setContextId","controlWidth":12,"aggElements":{}},"offSet":0,"name":"setContextId","level":0,"indexInParent":11,"bHasAttachment":false,"bEmbed":false,"bSetValues":true,"JSONPath":"setContextId","lwcId":"lwc11"},{"type":"Step","propSetMap":{"businessEvent":"","businessCategory":"","pubsub":false,"message":{},"ssm":false,"wpm":false,"errorMessage":{"default":null,"custom":[]},"allowSaveForLater":false,"chartLabel":null,"instructionKey":"","HTMLTemplateId":"","conditionType":"Hide if False","show":null,"knowledgeOptions":{"typeFilter":"","remoteTimeout":30000,"dataCategoryCriteria":"","keyword":"","publishStatus":"Online","language":"English"},"remoteOptions":{},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","showPersistentComponent":[true,false],"instruction":"","completeMessage":"Are you sure you want to complete the script?","completeLabel":"Complete","saveMessage":"Are you sure you want to save it for later?","saveLabel":"Save for later","cancelMessage":"Are you sure?","cancelLabel":"Cancel","nextWidth":"0","nextLabel":"Next","previousWidth":"0","previousLabel":"Previous","validationRequired":true,"label":"","uiElements":{"customLWCUserLoginStep":""},"aggElements":{"daoSiteUserLoginLWC":""}},"offSet":0,"name":"customLWCUserLoginStep","level":0,"indexInParent":12,"bHasAttachment":false,"bEmbed":false,"response":null,"inheritShowProp":null,"children":[{"response":null,"level":1,"indexInParent":0,"eleArray":[{"type":"Custom Lightning Web Component","rootIndex":12,"response":null,"propSetMap":{"customAttributes":[],"bStandalone":false,"lwcName":"daoSiteUserLogin","hide":false,"conditionType":"Hide if False","show":null,"label":"","controlWidth":12},"name":"daoSiteUserLoginLWC","level":1,"JSONPath":"customLWCUserLoginStep:daoSiteUserLoginLWC","indexInParent":0,"index":0,"children":[],"bHasAttachment":false,"bcustomlightningwebcomponent1":true,"lwcId":"lwc120-0"}],"bHasAttachment":false}],"bAccordionOpen":false,"bAccordionActive":false,"bStep":true,"isStep":true,"JSONPath":"customLWCUserLoginStep","lwcId":"lwc12"},{"type":"Navigate Action","propSetMap":{"targetUrl":"%loginUrl%","businessEvent":"","businessCategory":"","targetLWCLayout":"lightning","replace":true,"iconPosition":"left","iconVariant":"","iconName":"","variant":"brand","targetId":"%loginUrl%","targetFilter":"Recent","loginAction":"login","recordAction":"view","objectAction":"home","targetType":"Web Page","message":{},"pubsub":false,"ssm":false,"wpm":false,"HTMLTemplateId":"","show":null,"validationRequired":"Submit","label":"redirectUserToAuthenticatedSite","controlWidth":12,"aggElements":{}},"offSet":0,"name":"redirectUserToAuthenticatedSite","level":0,"indexInParent":13,"bHasAttachment":false,"bEmbed":false,"bNavigate":true,"JSONPath":"redirectUserToAuthenticatedSite","lwcId":"lwc13"}],"bReusable":false,"bpVersion":7,"bpType":"rcu","bpSubType":"SiteAutoLogin","bpLang":"English","bHasAttachment":false,"lwcVarMap":{}};