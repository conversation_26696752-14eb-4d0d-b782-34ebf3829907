let definition =
      {"dataSource":{"contextVariables":[],"orderBy":{"isReverse":"","name":""},"type":"Custom","value":{"body":"[\n    {\n      \"attributes\": {\n        \"type\": \"Account\",\n        \"url\": \"/services/data/v60.0/sobjects/Account/001O3000002cjz3IAA\"\n      },\n      \"Id\": \"001O3000002cjz3IAA\",\n      \"Name\": \"Bond Adventures\"\n    },\n    {\n      \"attributes\": {\n        \"type\": \"Account\",\n        \"url\": \"/services/data/v60.0/sobjects/Account/001O3000002gO9eIAE\"\n      },\n      \"Id\": \"001O3000002gO9eIAE\",\n      \"Name\": \"Sample Medical Carrier\"\n    },\n    {\n      \"attributes\": {\n        \"type\": \"Account\",\n        \"url\": \"/services/data/v60.0/sobjects/Account/001O3000002iD21IAE\"\n      },\n      \"Id\": \"001O3000002iD21IAE\",\n      \"Name\": \"Alan Test Account\"\n    },\n    {\n      \"attributes\": {\n        \"type\": \"Account\",\n        \"url\": \"/services/data/v60.0/sobjects/Account/001O3000007nq6LIAQ\"\n      },\n      \"Id\": \"001O3000007nq6LIAQ\",\n      \"Name\": \"AmTrust Financial Services, Inc\"\n    },\n    {\n      \"attributes\": {\n        \"type\": \"Account\",\n        \"url\": \"/services/data/v60.0/sobjects/Account/001O3000007nq6MIAQ\"\n      },\n      \"Id\": \"001O3000007nq6MIAQ\",\n      \"Name\": \"Bankers Security\"\n    }\n  ]","dsDelay":"","resultVar":""}},"enableLwc":true,"isFlex":true,"isRepeatable":false,"lwc":{"DeveloperName":"cfTestDatatable","Id":"0RbU8000000EcJxKAK","MasterLabel":"cfTestDatatable","NamespacePrefix":"c","ManageableState":"unmanaged"},"selectableMode":"Multi","states":[{"actions":[],"childCards":[],"components":{"layer-0":{"children":[{"class":"slds-col ","element":"flexDatatable","elementLabel":"Datatable-0","name":"Datatable","property":{"card":"{card}","cellLevelEdit":true,"columns":[{"editable":"true","fieldName":"Name","label":"Name","searchable":false,"sortable":true,"type":"text"}],"groupOrder":"asc","issearchavailable":false,"issortavailable":true,"pagelimit":3,"record":"{record}","records":"{records}"},"size":{"default":"12","isResponsive":false},"stateIndex":0,"styleObject":{"sizeClass":"slds-size_12-of-12"},"type":"element"}]}},"conditions":{"group":[],"id":"state-condition-object","isParent":true},"definedActions":{"actions":[]},"documents":[],"fields":[],"isSmartAction":false,"name":"Active","omniscripts":[],"smartAction":{},"styleObject":{"class":"slds-card slds-p-around_x-small slds-m-bottom_x-small","container":{"class":"slds-card"},"margin":[{"size":"x-small","type":"bottom"}],"padding":[{"size":"x-small","type":"around"}],"size":{"default":"12","isResponsive":false},"sizeClass":"slds-size_12-of-12"}}],"theme":"slds","title":"TestDatatable","Name":"TestDatatable","uniqueKey":"TestDatatable","Id":"0koU80000000VrpIAE","OmniUiCardKey":"TestDatatable/Rodrigo/1.0","OmniUiCardType":"Parent"};
  export default definition