public with sharing class cdaoModel {

    public class Application{
        public Applicant primaryApplicant{get;set;}
        public list<Applicant> jointApplicants{get;set;}
        public Products products{get;set;}
        public AddionalInfromation addionalInfromation{get;set;}
        public FundingOptions fundingOptions{get;set;}
    }

    public class Applicant{
        public MemberInformation MemberInformation{get;set;}
        public MailingAddress MailingAddress{get;set;}
        public Boolean Mailing_Address_same_as_Physical{get;set;}
        public PhysicalAddress PhysicalAddress{get;set;}
        public Identity IdentityInfo{get;set;}
    }

    public class MemberInformation{
        public String SSN_TIN{get;set;}
        public String First_Name{get;set;}
        public String Middle_Name{get;set;}
        public String Individual_Role{get;set;}
        public String Last_Name{get;set;}
        public String Date_of_Birth{get;set;}
        public String Suffix{get;set;}
        public String Phone{get;set;}
        public String Email_Address{get;set;}
    }
    public class MailingAddress{
        public String Mailing_Street_Address{get;set;}
        public String Mailing_Zip_code{get;set;}
        public String Mailing_State{get;set;}
        public String Mailing_City{get;set;}
    }

    public class PhysicalAddress{
        public String Physical_Street_Address{get;set;}
        public String Physical_Zip_code{get;set;}
        public String Physical_State{get;set;}
        public String Physical_City{get;set;}
    }

    public class Address{
        public String Street_Address{get;set;}
        public String Zip_code{get;set;}
        public String State{get;set;}
        public String City{get;set;}
    }    

    public class Identity{
        public String Type{get;set;}
        public String ID_Number{get;set;}
        public String ID_Issued_Date{get;set;}
        public String ID_Expiration_Date{get;set;}
        public String ID_Country{get;set;}
        public String ID_State{get;set;}
    }

    public class Products {
        public Boolean Savings{get;set;}
        public Boolean Benifits_Checking{get;set;}
        public Boolean Regular_Checking{get;set;}
        public Boolean Plus_Checking{get;set;}
        public Boolean Direct_Access_Checking{get;set;}
        public Boolean Debit_Card{get;set;}
        public Boolean Digital_Banking{get;set;}
        public Boolean eStatement{get;set;}
        public Boolean Credit_Card{get;set;}
        public Boolean All_OPA{get;set;}
        public Boolean Checks_Electronics_OPA{get;set;}
        public Boolean No_OPA{get;set;}
    }

    public class AddionalInfromation  {
        public String Verbal_Password{get;set;}
        public String Verbal_Password_Hint{get;set;}

        public String Membership_Eigblity{get;set;}
        public String County{get;set;}
        public String Member_Name{get;set;}
        public String Relation_Type{get;set;}

        public String Housing_Status{get;set;}
        public String Collage_Student{get;set;}


        public String Employment_Status{get;set;}
        public String Job_Title{get;set;}
        public String RCU_Employee{get;set;}
        public String Employer_Name{get;set;}
        public String Employment_Years{get;set;}
        public String Employment_Months{get;set;}

        public String Type_Of_Business{get;set;}


        public String Monthly_Income{get;set;}
        public String Monthly_Payment{get;set;}

        public Boolean isAdditional_Monthly_Income{get;set;}
        public String Additional_Monthly_Income{get;set;}

        public Boolean IRS_Withholding{get;set;}

    }

    public class FundingOptions  {
        public String Funding_Type{get;set;}
        public String Savings_Deposit_Amount{get;set;}
        public String Checking_Deposit_Amount{get;set;}

        //Internal Transfer Fields

        public String Internal_Transfer_Account_Type{get;set;}
        public String Internal_Transfer_Account_Number{get;set;}

        //External Transfer Fields
        public String External_Transfer_Account_Type{get;set;}
        public String External_Transfer_Account_Number{get;set;}
        public String External_Transfer_State{get;set;}
        public String External_Transfer_Name_On_Account{get;set;}
        public String External_Transfer_Routing_Number{get;set;}
        public String External_Transfer_Bank_Name{get;set;}

        //CreditCard Transfer Fields
        public String CC_Number{get;set;}
        public String CC_Holder_Name{get;set;}
        public String CC_Expiration_Date{get;set;}
        public String CC_CVV{get;set;}
        public Address BillingAddress{get;set;}

    }

}