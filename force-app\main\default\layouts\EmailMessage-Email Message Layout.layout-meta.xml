<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>RelatedToId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>MessageDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Address Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FromAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>FromName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ToAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CcAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BccAddress</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Message Content</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Subject</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HtmlBody</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TextBody</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>EmailMessage._ReplyAll</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>EmailMessage._Reply</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>EmailMessage._Forward</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <relatedLists>
        <relatedList>RelatedAttachmentList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>TYPE</fields>
        <relatedList>RelatedEmailMessagePeopleList</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
