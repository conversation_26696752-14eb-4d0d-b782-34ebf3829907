@isTest
public class rcuConsumerDAOTest {
    @isTest
    static void testGetApplication() {
        // Act: Call the method to get an initialized application
        cdaoModel.Application result = rcuConsumerDAO.getApplication();
        
        // Assert: Verify the application structure is properly initialized
        System.assertNotEquals(null, result, 'Application should not be null');
        System.assertNotEquals(null, result.primaryApplicant, 'Primary applicant should not be null');
        System.assertNotEquals(null, result.primaryApplicant.MemberInformation, 'Member information should not be null');
        System.assertNotEquals(null, result.primaryApplicant.PhysicalAddress, 'Physical address should not be null');
        System.assertNotEquals(null, result.primaryApplicant.MailingAddress, 'Mailing address should not be null');
        System.assertNotEquals(null, result.primaryApplicant.IdentityInfo, 'Identity info should not be null');
        System.assertEquals(false, result.primaryApplicant.Mailing_Address_same_as_Physical, 'Mailing address same as physical should be false by default');
        System.assertNotEquals(null, result.jointApplicants, 'Joint applicants list should not be null');
        System.assertEquals(0, result.jointApplicants.size(), 'Joint applicants list should be empty');
        System.assertNotEquals(null, result.products, 'Products should not be null');
        System.assertNotEquals(null, result.addionalInfromation, 'Additional information should not be null');
        System.assertNotEquals(null, result.fundingOptions, 'Funding options should not be null');
    }

    @isTest
    static void testSaveApplicationSuccess() {
        // Arrange: Create mock data for the application
        String mockJsonData = '{"primaryApplicant": {"MemberInformation": {"First_Name": "John", "Last_Name": "Doe"}}}';

        // Act: Call the method with valid data
        String result = rcuConsumerDAO.saveApplication(mockJsonData);

        // Assert: Verify the success message
        System.assertEquals('Success', result);
    }

    @isTest
    static void testSaveApplicationNullData() {
        // Arrange: Pass null data
        String mockJsonData = null;

        // Act: Call the method with null data
        String result = rcuConsumerDAO.saveApplication(mockJsonData);

        // Assert: Verify the error message
        System.assertEquals('Error: Deserialization resulted in null object', result);
    }

    @isTest
    static void testSaveApplicationInvalidJson() {
        // Arrange: Create invalid JSON data
        String mockJsonData = '{"primaryApplicant": {"MemberInformation": {"First_Name": "John", "Last_Name": "Doe"';

        // Act: Call the method with invalid JSON
        String result = rcuConsumerDAO.saveApplication(mockJsonData);

        // Assert: Verify the error message
        System.assert(result.contains('Error:'), 'Should return an error message for invalid JSON');
    }
    
    @isTest
    static void testCompleteApplicationFlow() {
        // Step 1: Get initial application
        cdaoModel.Application app = rcuConsumerDAO.getApplication();
        System.assertNotEquals(null, app, 'Initial application should not be null');
        
        // Step 2: Update member information
        app.primaryApplicant.MemberInformation.First_Name = 'Jane';
        app.primaryApplicant.MemberInformation.Last_Name = 'Smith';
        app.primaryApplicant.MemberInformation.SSN_TIN = '***********';
        app.primaryApplicant.MemberInformation.Date_of_Birth = '1980-01-01';
        app.primaryApplicant.MemberInformation.Email_Address = '<EMAIL>';
        app.primaryApplicant.MemberInformation.Phone = '************';
        
        // Step 3: Update address information
        app.primaryApplicant.PhysicalAddress.Physical_Street_Address = '123 Main St';
        app.primaryApplicant.PhysicalAddress.Physical_City = 'Anytown';
        app.primaryApplicant.PhysicalAddress.Physical_State = 'CA';
        app.primaryApplicant.PhysicalAddress.Physical_Zip_code = '12345';
        
        app.primaryApplicant.Mailing_Address_same_as_Physical = true;
        
        // Step 4: Update identity information
        app.primaryApplicant.IdentityInfo.Type = 'Driver License';
        app.primaryApplicant.IdentityInfo.ID_Number = '**********';
        app.primaryApplicant.IdentityInfo.ID_Issued_Date = '2020-01-01';
        app.primaryApplicant.IdentityInfo.ID_Expiration_Date = '2028-01-01';
        app.primaryApplicant.IdentityInfo.ID_State = 'CA';
        app.primaryApplicant.IdentityInfo.ID_Country = 'USA';
        
        // Convert to JSON and save
        String appJson = JSON.serialize(app);
        String result = rcuConsumerDAO.saveApplication(appJson);
        
        // Verify save was successful
        System.assertEquals('Success', result, 'Complete application save should succeed');
    }
}



