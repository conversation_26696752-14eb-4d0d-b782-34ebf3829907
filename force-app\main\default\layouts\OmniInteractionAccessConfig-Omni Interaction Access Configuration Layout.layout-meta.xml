<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Omni Interaction Access Config Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SetupOwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsCardApexRemoteDisabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsCardDataTfrmDisabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsCardIntegrationProcDisabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsCardRestApiDisabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsCardSoqlDisabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsCardSoslDisabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsCardStreamingApiDisabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsCardCacheDisabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsAsyncCardCachingEnabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsDataTfrmEncrpFieldsDisabled</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
