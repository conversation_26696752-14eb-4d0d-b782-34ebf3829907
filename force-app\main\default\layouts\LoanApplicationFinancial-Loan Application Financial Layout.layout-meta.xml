<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>LoanApplicationId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PurchasePriceAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RemodelAndRepairsAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LandAcquiredSeparatelyAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OutstandingLoanAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BorrowerClosingCostAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DiscountPointCostAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LoanAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalCashDueFromBorrower</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EstFirstMortgagePmtAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TotalDebtPaidOffAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EstSubordinateLienPmtAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EstHomeownersInsPmtAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EstSuppPropertyInsPmtAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EstPropertyTaxesPmtAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EstMortgageInsPmtAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EstOtherDuesPaymentAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EstOtherPaymentAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalEstPaymentAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LoanExclFinMortInsAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FinancedMortgageInsAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalMortgageLoansAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CreditFromSeller</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OtherCredit</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalCredit</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OtherNewMortgageLoanAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalMortgageLoanAndCreditAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CashFromOrToBorrowerAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HasHoEducation</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HoEducationFormat</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HudApprovedProvIdentifier</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CounselingAgencyProvName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HoEducationCompletedDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EdCompletedBorrowerName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HasCompletedCounseling</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HousingCounselingFormat</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HudApprAgencyIdentifier</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CounselingAgencyName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CounselingCompletionDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CounselingComplBorrName</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
