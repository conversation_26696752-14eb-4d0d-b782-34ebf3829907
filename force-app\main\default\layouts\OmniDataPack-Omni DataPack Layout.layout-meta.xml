<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>OmniDataPack</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Version</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MultiPackUpdatedDateTime</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MultiPackUpdatedById</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Component</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsAddedToMultiPack</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AsyncApexJobIdentifier</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ActivationStatus</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AsyncJobStatus</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Source</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ProcessStatus</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ActionType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
