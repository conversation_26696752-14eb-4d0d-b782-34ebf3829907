<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>OmniProcessSection</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RequiredPermission</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CustomJavaScript</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsMetadataCacheDisabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsIntegrationProcedure</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsWebCompEnabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PropertySetConfig</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CustomHtmlTemplates</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>VersionNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>WebComponentKey</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>OmniProcessType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>UniqueName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OverrideKey</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsIntegProcdSignatureAvl</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IntegrationProcedureOutput</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ElementTypeComponentMapping</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsActive</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ResponseCacheType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsOmniScriptEmbeddable</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OmniProcessKey</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SubType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LastPreviewPage</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsTestProcedure</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Language</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Namespace</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsManagedUsingStdDesigner</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IntegrationProcedureInput</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
