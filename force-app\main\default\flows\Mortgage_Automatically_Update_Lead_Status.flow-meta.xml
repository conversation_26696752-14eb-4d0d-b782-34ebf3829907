<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <constants>
        <name>true</name>
        <dataType>Boolean</dataType>
        <value>
            <booleanValue>true</booleanValue>
        </value>
    </constants>
    <decisions>
        <name>Check_for_required_fields</name>
        <label>Check for required fields</label>
        <locationX>314</locationX>
        <locationY>287</locationY>
        <defaultConnector>
            <targetReference>Set_Lead_status_to_working</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Application_decision_yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Mortgage_Application_Decision__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Yes</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Application_Date__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Insure_funded_fields_are_not_all_populated</targetReference>
            </connector>
            <label>Application decision &apos;yes&apos;</label>
        </rules>
        <rules>
            <name>Application_decision_no_or_future</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Mortgage_Application_Decision__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>No</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Mortgage_Application_Decision__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Future</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Lead_status_to_application_status</targetReference>
            </connector>
            <label>Application decision &apos;no&apos; or &apos;future&apos;</label>
        </rules>
    </decisions>
    <decisions>
        <name>Insure_funded_fields_are_not_all_populated</name>
        <label>Insure funded fields are not all populated</label>
        <locationX>50</locationX>
        <locationY>395</locationY>
        <defaultConnector>
            <targetReference>Copy_2_of_Set_Lead_status_to_funded</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Funding</defaultConnectorLabel>
        <rules>
            <name>Not_funding</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Loan_Funded_Date__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <elementReference>true</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Loan_Number__c</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.LeadSource</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Lead_status_to_application_status</targetReference>
            </connector>
            <label>Not funding</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Mortgage {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Mortgage Automatically Update Lead Status</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <name>Copy_2_of_Set_Lead_status_to_funded</name>
        <label>Copy 2 of Set Lead status to &apos;funded&apos;</label>
        <locationX>138</locationX>
        <locationY>503</locationY>
        <inputAssignments>
            <field>Mortgage_Lead_Status__c</field>
            <value>
                <stringValue>Funded</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Set_Lead_status_to_application_status</name>
        <label>Set Lead status to &apos;application status&apos;</label>
        <locationX>314</locationX>
        <locationY>887</locationY>
        <inputAssignments>
            <field>Mortgage_Lead_Status__c</field>
            <value>
                <stringValue>Application_Status</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Set_Lead_status_to_working</name>
        <label>Set Lead status to &apos;working&apos;</label>
        <locationX>578</locationX>
        <locationY>395</locationY>
        <inputAssignments>
            <field>Mortgage_Lead_Status__c</field>
            <value>
                <stringValue>Working</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>188</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_for_required_fields</targetReference>
        </connector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>Mortgage_Application_Decision__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Application_Date__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Mortgage_Is_Loan_Funded__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>Loan_Number__c</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>LeadSource</field>
            <operator>IsChanged</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <object>Lead</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
