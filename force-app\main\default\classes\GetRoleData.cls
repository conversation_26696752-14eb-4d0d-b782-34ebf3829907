public class GetRoleData {
    public List<RoleDataWrapper> roleDataList {get; set;}
    public String roleData {get; set;}
    public Boolean rolecheckBoxData {get; set;}
    public List<DAO_Roles__c> roleValues {get; set;}
    public GetRoleData(String roleDataValues) {
        roleData = roleDataValues;
    }
    public GetRoleData() {  
        String bId = ApexPages.CurrentPage().getParameters().get('AppId');
        roleValues = new List<DAO_Roles__c>();
        roleValues = [SELECT Id, Name, SSN_TIN__c, Email__c FROM DAO_Roles__c where DAO_Application__c =: bId];
    }
    
    public class RoleDataWrapper {
        public String DAORolesName { get; set; }
        public String Email5 { get; set; }
        public String SSNTIN { get; set; }
    }
    
    public List<RoleDataWrapper> parseJsonToList(String jsonString) {
        roleDataList = new List<RoleDataWrapper>();
        jsonString = jsonString.replace('Primary/HomePhone', 'Primary_HomePhone').replace('SSN/TIN', 'SSN_TIN');
        roleDataList = (List<RoleDataWrapper>) JSON.deserialize(jsonString, List<RoleDataWrapper>.class);
        return roleDataList;
    }    
}