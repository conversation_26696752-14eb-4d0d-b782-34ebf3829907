<template>
    <h1 class="slds-text-heading_large slds-text-align_center">
        Application Summary and Submission
    </h1>

    <div class="slds-p-around_medium">
        <!-- Business Information Section -->
        <h2 class="slds-text-heading_medium">Product Selectionss</h2>
        <div class="slds-grid slds-wrap">
            <div class="slds-col slds-size_1-of-3">
                <lightning-input type="checkbox" label="Business Essential Checking" checked={businessEssentialChecking} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-3">
                <lightning-input type="checkbox" label="Business Choice Checking" checked={businessChoiceChecking} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-3">
                <lightning-input type="checkbox" label="Business Savings" checked={businessSavings} disabled></lightning-input>
            </div>
        </div>
        <h2 class="slds-text-heading_medium slds-m-top_medium">Account Options</h2>
        <div class="slds-grid slds-wrap">
            <div class="slds-col slds-size_1-of-2">
                <lightning-input type="checkbox" label="Visa® Debit Card" checked={visaDebitCard} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-2">
                <lightning-input type="checkbox" label="eStatements" checked={eStatements} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-2">
                <lightning-input type="checkbox" label="Digital Banking" checked={onlineBanking} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-2">
                <lightning-input type="checkbox" label="Interested in a Business Visa® Credit Card" checked={businessVisaCreditCard} disabled></lightning-input>
            </div>
        </div>

        <!-- Overdraft Pay Advantage Section -->
        <h2 class="slds-text-heading_medium slds-m-top_medium">
            Overdraft Pay Advantage Selection for your Checking Account:
        </h2>
        <lightning-radio-group
            name="overdraftSelection"
            options={overdraftOptions}
            value={overdraftSelection}
            type="radio"
            disabled>
        </lightning-radio-group>

        <!-- Business Information Section -->
        <h2 class="slds-text-heading_medium slds-m-top_medium">Business Information</h2>
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_2-of-3 slds-p-right_small">
                <lightning-input type="text" label="Business Name" value={businessName} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-3 slds-align-top">
                <lightning-input type="checkbox" label="Fictitious Business Name(FBN)/Doing Business As(DBA)" checked={fbaOrDba} disabled></lightning-input>
            </div>
        </div>
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_2-of-12">
                <lightning-radio-group
                    label="Business Tax ID Type"
                    name="businessTaxIdType"
                    options={businessTaxIdTypeOptions}
                    value={businessTaxIdType}
                    type="button"
                    disabled>

                </lightning-radio-group>
            </div>
            <div class="slds-col slds-size_2-of-12">
                <lightning-input type="text" label={businessTaxIdLabel} value={businessTaxId} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_4-of-12 slds-p-left_small">
                <lightning-input type="text" label="Principal Location" value={principalLocation} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_2-of-12 slds-p-left_small">
                <lightning-input type="text" label="Preferred Branch" value={preferredBranch} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_2-of-12 slds-p-left_small">
                <lightning-input type="text" label="Business Structure" value={businessStructure} disabled></lightning-input>
            </div>
        </div>
        <template if:true={businessStructureCorpOrLLC}>
            <div class="slds-grid slds-wrap slds-m-bottom_small">
                <div class="slds-size_12-of-12 slds-m-bottom_small">
                    <lightning-radio-group
                        label="Is this a publicly traded company?"
                        name="isPublicCompany"
                        options={yesNoOptions}
                        value={isPublicCompany}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>
                <template if:true={isPublicCompanyValue}>
                    <div class="slds-size_12-of-12 slds-m-bottom_small">
                        <lightning-radio-group
                            label="Traded on the New York, American, or NASDAQ stock exchange?"
                            name="tradedOnStockExchange"
                            options={yesNoOptions}
                            value={tradedOnStockExchange}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>
                </template>
                <template if:true={tradedOnStockExchangeValue}>
                    <div class="slds-size_12-of-12 slds-m-bottom_small">
                        <lightning-radio-group
                            label="Is the company at least 51% owned by an entity listed on the New York, American, or NASDAQ stock exchange?"
                            name="ownedByListedEntity"
                            options={yesNoOptions}
                            value={ownedByListedEntity}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>
                </template>
            </div>
        </template>
        <template if:false={businessStructureSoleProprietorship}>
            <div class="slds-grid slds-wrap slds-m-bottom_small">
                <div class="slds-size_12-of-12 slds-m-bottom_small">
                    <lightning-radio-group
                        label="Is your business headquartered in the United States?"
                        name="isHeadquarteredInUS"
                        options={yesNoOptions}
                        value={isHeadquarteredInUS}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>
                <template if:false={isHeadquarteredInUSValue}>
                    <div class="slds-size_12-of-12 slds-m-bottom_small">
                        <lightning-input type="text" 
                            label="In what country is your business headquartered?" 
                            value={headquarteredCountry} 
                            disabled>
                        </lightning-input>
                    </div>
                </template>
                <div class="slds-size_12-of-12 slds-m-bottom_small">
                    <lightning-radio-group
                        label="Are you registered to do business in California?"
                        name="registeredInCalifornia"
                        options={yesNoOptions}
                        value={registeredInCalifornia}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>
            </div>
        </template>
        <template if:true={businessStructureUnincorpOrCorpOrLLC}>
            <div class="slds-grid slds-wrap slds-m-bottom_small">
                <div class="slds-size_12-of-12 slds-m-bottom_small">
                    <lightning-radio-group
                        label="Does your organization qualify for non-profit status and depend on charitable donations or voluntary service?"
                        name="qualifiesForNonProfitStatus"
                        options={yesNoOptions}
                        value={qualifiesForNonProfitStatus}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>
                <template if:true={qualifiesForNonProfitStatusValue}>
                    <div class="slds-size_12-of-12 slds-m-bottom_small">
                        <lightning-input type="text"
                            label="What are the objectives, programs, activities, and services that your organization provides related to its charitable function?"
                            value={charitableObjectives}
                            disabled>
                        </lightning-input>
                    </div>
                    <div class="slds-size_12-of-12 slds-m-bottom_small">
                        <lightning-input type="text"
                            label="In what country is your organization chartered?"
                            value={organizationCharteredCountry}
                            disabled>
                        </lightning-input>
                    </div>            
                    <div class="slds-size_12-of-12 slds-m-bottom_small">
                        <lightning-radio-group
                            label="Is your organization registered as a non-profit with the IRS, for example, as a 501(c) organization?"
                            name="registeredAs501c"
                            options={yesNoOptions}
                            value={registeredAs501c}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>                
                    <div class="slds-size_12-of-12 slds-m-bottom_small">
                        <label class="slds-form-element__label">What are the sources of funding for the organization?</label>
                        <div class="slds-form-element__control slds-grid slds-wrap">
                            <template for:each={fundingSourcesWithChecked} for:item="option">
                                <div key={option.value} class="slds-col slds-size_1-of-4 slds-p-around_xx-small">
                                    <lightning-input 
                                        type="checkbox" 
                                        label={option.label} 
                                        value={option.value}
                                        checked={option.checked}
                                        disabled>
                                    </lightning-input>
                                </div>
                            </template>
                        </div>
                    </div>
                    <template if:true={fundingSourcesWithCheckedOtherChecked}>
                        <div class="slds-size_12-of-12 slds-m-bottom_small">
                            <lightning-input type="text" label="Other Source Description" value={otherSourceDescription} disabled></lightning-input>
                        </div>
                    </template>            
                    <div class="slds-size_12-of-12 slds-m-bottom_small">
                        <label class="slds-form-element__label">What methods does your organization use to obtain its funding?</label>
                        <div class="slds-form-element__control slds-grid slds-wrap">
                            <template for:each={fundingMethodsWithChecked} for:item="option">
                                <div key={option.value} class="slds-col slds-size_1-of-4 slds-p-around_xx-small">
                                    <lightning-input 
                                        type="checkbox" 
                                        label={option.label} 
                                        value={option.value}
                                        checked={option.checked}
                                        disabled>
                                    </lightning-input>
                                </div>
                            </template>
                        </div>
                    </div>
                    <template if:true={fundingMethodsWithCheckedOtherChecked}>
                    <div class="slds-size_12-of-12 slds-m-bottom_small">
                        <lightning-input type="text" label="Other Funding Description" value={otherFundingDescription} disabled></lightning-input>
                    </div>
                    </template>
                
                    <div class="slds-size_12-of-12 slds-m-bottom_small">
                        <lightning-input type="text" label="What is the value of your organization's annual funding or gross receipts for the most recent year?" value={annualFundingValue} disabled></lightning-input>
                    </div>            
                    <div class="slds-size_12-of-12 slds-m-bottom_small">
                        <lightning-radio-group
                            label="Do you have donors or volunteers from non-US countries?"
                            name="nonUSDonorsOrVolunteers"
                            options={yesNoOptions}
                            value={nonUSDonorsOrVolunteers}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>            
                    <template if:true={nonUSDonorsOrVolunteersValue}>
                        <div class="slds-size_12-of-12 slds-m-bottom_small">
                            <lightning-input type="text" label="In what countries are your donors or volunteers located?" value={donorsCountries} disabled></lightning-input>
                        </div>
                    </template>            
                    <div class="slds-size_12-of-12 slds-m-bottom_small">
                        <lightning-radio-group
                            label="Will you provide charitable services to benefit individuals in foreign countries?"
                            name="charitableServicesAbroad"
                            options={yesNoOptions}
                            value={charitableServicesAbroad}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>
                    <template if:true={charitableServicesAbroadValue}>
                        <div class="slds-size_12-of-12 slds-m-bottom_small">
                            <lightning-input type="text" label="In what countries do you provide charitable services?" value={charitableServiceCountries} disabled></lightning-input>
                        </div>
                        <div class="slds-size_12-of-12 slds-m-bottom_small">
                            <lightning-input type="text" label="In what countries are your foreign beneficiaries located?" value={foreignBeneficiariesCountries} disabled></lightning-input>
                        </div>
                    </template>
                </template>
            </div>
        </template>
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-4 slds-p-right_small">
                <lightning-input type="text" label="Establish Date" value={establishDate} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4 slds-p-right_small">
                <lightning-input type="text" label="Annual Revenue" value={annualRevenue} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4 slds-p-right_small">
                <lightning-input type="text" label="State Registered" value={stateRegistered} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4">
                <lightning-input type="text" label="Number of Employees" value={numberOfEmployees} disabled></lightning-input>
            </div>
        </div>      
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1">
                <lightning-input type="text" label="Business Description" value={businessDescription} disabled></lightning-input>
            </div>
        </div>
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1">
                <lightning-input type="text" label="NAICS Code" value={naicsCode} disabled></lightning-input>
            </div>
        </div>
        <!-- Physical and Mailing Address Section -->
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-2 slds-p-right_small">
                <div style="height: 1.25rem;"></div>
                <h3 class="slds-text-heading_small">Physical Address</h3>
                <lightning-input type="text" label="Street" value={physicalStreet} disabled></lightning-input>
                <div class="slds-grid slds-wrap">
                    <div class="slds-col slds-size_1-of-2 slds-p-right_small">
                        <lightning-input type="text" label="City" value={physicalCity} disabled></lightning-input>
                    </div>
                    <div class="slds-col slds-size_1-of-2">
                        <lightning-input type="text" label="State" value={physicalState} disabled></lightning-input>
                    </div>
                    <div class="slds-col slds-size_1-of-2 slds-p-right_small">
                        <lightning-input type="text" label="Postal Code" value={physicalPostalCode} disabled></lightning-input>
                    </div>
                    <div class="slds-col slds-size_1-of-2">
                        <lightning-input type="text" label="Country" value={physicalCountry} disabled></lightning-input>
                    </div>
                </div>
            </div>
            <div class="slds-col slds-size_1-of-2">
                <lightning-input type="checkbox" label="Use different address for mailing" checked={useDifferentMailingAddressApplicant} disabled></lightning-input>
                <h3 class="slds-text-heading_small">Mailing Address</h3>
                <lightning-input type="text" label="Street" value={mailingStreet} disabled></lightning-input>
                <div class="slds-grid slds-wrap">
                    <div class="slds-col slds-size_1-of-2 slds-p-right_small">
                        <lightning-input type="text" label="City" value={mailingCity} disabled></lightning-input>
                    </div>
                    <div class="slds-col slds-size_1-of-2">
                        <lightning-input type="text" label="State" value={mailingState} disabled></lightning-input>
                    </div>
                    <div class="slds-col slds-size_1-of-2 slds-p-right_small">
                        <lightning-input type="text" label="Postal Code" value={mailingPostalCode} disabled></lightning-input>
                    </div>
                    <div class="slds-col slds-size_1-of-2">
                        <lightning-input type="text" label="Country" value={mailingCountry} disabled></lightning-input>
                    </div>
                </div>
            </div>
        </div>
        <!-- Occupancy and Business Contact Information Section -->
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-4 slds-p-right_small">
                <lightning-input type="text" label="Occupancy Status" value={occupancyStatus} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4 slds-p-right_small">
                <lightning-input type="text" label="Other Description" value={otheroccupancyStatus} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4 slds-p-right_small">
                <lightning-input type="text" label="Occupancy Duration - Years" value={occupancyDurationYears} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4">
                <lightning-input type="text" label="Occupancy Duration - Months" value={occupancyDurationMonths} disabled></lightning-input>
            </div>
        </div>
        <!-- previous addresses -->
         <template if:true={OccupancyYears}>
            <div class="slds-grid slds-wrap slds-m-bottom_small">
                <div class="slds-col slds-size_1-of-4 slds-p-right_small">
                    <lightning-input type="text" label="Previous Street" value={previousStreet} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-4 slds-p-right_small">
                    <lightning-input type="text" label="Previous City" value={previousCity} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-4 slds-p-right_small">
                    <lightning-input type="text" label="Previous State" value={previousState} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-4">
                    <lightning-input type="text" label="Previous Zipcode" value={previousZipcode} disabled></lightning-input>
                </div>
            </div>
        </template>

        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-2 slds-p-right_small">
                <lightning-input type="text" label="Business Email" value={businessEmail} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-2">
                <lightning-input type="text" label="Business Cell Phone" value={formattedBusinessCellPhone} disabled></lightning-input>
            </div>
        </div>
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-2 slds-p-right_small">
                <lightning-input type="text" label="Business Phone" value={formattedBusinessPhone} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-2">
                <lightning-input type="text" label="Business Fax" value={formattedBusinessFax} disabled></lightning-input>
            </div>
        </div>

        <!-- Business Roles Section -->
        <h2 class="slds-text-heading_medium slds-m-top_medium">Business Roles</h2>
        <table class="slds-table slds-table_cell-buffer slds-table_bordered">
            <thead>
                <tr>                    
                    <th>Name</th>
                    <th>Role</th>
                    <th>Beneficial Owner %</th>
                    <th>Email</th>
                    <th>ID Attached</th>
                    <th>Completed</th>
                    <th>Applicant</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <template for:each={businessRoles} for:item="role">
                    <tr key={role.id}>                        
                        <td>{role.name}</td>
                        <td>{role.role}</td>
                        <td>{role.ownerPercent}</td>
                        <td>{role.email}</td>
                        <td><lightning-input type="checkbox" checked={role.idAttached} disabled></lightning-input></td>
                        <td><lightning-input type="checkbox" checked={role.completed} disabled></lightning-input></td>
                        <td><lightning-input type="checkbox" checked={role.applicant} disabled></lightning-input></td>
                        <td>
                            <button class="slds-button slds-button_icon" onclick={toggleRow} data-id={role.id} title="Expand/Collapse">
                                <template if:true={role.isExpanded}>
                                    <lightning-icon icon-name="utility:chevrondown" size="x-small"></lightning-icon>
                                </template>
                                <template if:false={role.isExpanded}>
                                    <lightning-icon icon-name="utility:chevronright" size="x-small"></lightning-icon>
                                </template>
                            </button>
                        </td>
                    </tr>
                    <template if:true={role.isExpanded}>
                        <tr key={role.detailKey}>
                            <td colspan="8">
                                <div class="slds-grid slds-wrap slds-p-around_x-small">
                                    <!-- First Row -->
                                    <div class="slds-col slds-size_1-of-4 slds-p-around_x-small">
                                        <lightning-input type="text" label="First Name" value={role.firstName} disabled></lightning-input>
                                    </div>
                                    <div class="slds-col slds-size_1-of-4 slds-p-around_x-small">
                                        <lightning-input type="text" label="Middle Name" value={role.middleName} disabled></lightning-input>
                                    </div>
                                    <div class="slds-col slds-size_1-of-4 slds-p-around_x-small">
                                        <lightning-input type="text" label="Last Name" value={role.lastName} disabled></lightning-input>
                                    </div>
                                    <div class="slds-col slds-size_1-of-4 slds-p-around_x-small">
                                        <lightning-input type="text" label="Suffix" value={role.suffix} disabled></lightning-input>
                                    </div>

                                    <!-- Second Row -->
                                    <div class="slds-col slds-size_1-of-4 slds-p-around_x-small">
                                        <lightning-input type="text" label="SSN" value={role.maskedSSN} disabled></lightning-input>
                                    </div>
                                    <div class="slds-col slds-size_1-of-4 slds-p-around_x-small">
                                        <lightning-input type="text" label="Date of Birth" value={role.dateOfBirth} disabled></lightning-input>
                                    </div>
                                    <div class="slds-col slds-size_1-of-4 slds-p-around_x-small">
                                        <lightning-input type="text" label="Citizenship Status" value={role.citizenshipStatus} disabled></lightning-input>
                                    </div>
                                </div>
                                <template if:true={businessStructureSoleProprietorship}>
                                    <div class="slds-grid slds-wrap slds-p-around_x-small">
                                        <!-- Third Row -->
                                        <div class="slds-col slds-size_1-of-4 slds-p-around_x-small">
                                            <lightning-radio-group
                                                label="Frequent Traveler"
                                                name="frequentTraveler"
                                                options={yesNoOptions}
                                                value={role.frequentTraveler}
                                                type="radio"
                                                disabled>
                                            </lightning-radio-group>
                                        </div>
                                        <div class="slds-col slds-size_1-of-4 slds-p-around_x-small">
                                            <lightning-radio-group
                                                label="Outside United States"
                                                name="outsideUnitedStates"
                                                options={yesNoOptions}
                                                value={role.outsideUnitedStates}
                                                type="radio"
                                                disabled>
                                            </lightning-radio-group>
                                        </div>
                                        <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                            <lightning-input
                                                type="text"
                                                label="Which Countries"
                                                value={role.whichCountries}
                                                disabled>
                                            </lightning-input>
                                        </div>
                                    </div>
                                </template>
                                <div class="slds-grid slds-wrap slds-p-around_x-small">
                                    <div class="slds-col slds-size_1-of-2 slds-p-right_small">
                                        <div style="height: 1.25rem;"></div>
                                        <h3 class="slds-text-heading_small">Physical Address</h3>
                                        <lightning-input type="text" label="Street" value={role.physicalStreet} disabled></lightning-input>
                                        <div class="slds-grid slds-wrap">
                                            <div class="slds-col slds-size_1-of-2 slds-p-right_small">
                                                <lightning-input type="text" label="City" value={role.physicalCity} disabled></lightning-input>
                                            </div>
                                            <div class="slds-col slds-size_1-of-2">
                                                <lightning-input type="text" label="State" value={role.physicalState} disabled></lightning-input>
                                            </div>
                                            <div class="slds-col slds-size_1-of-2 slds-p-right_small">
                                                <lightning-input type="text" label="Postal Code" value={role.physicalPostalCode} disabled></lightning-input>
                                            </div>
                                            <div class="slds-col slds-size_1-of-2">
                                                <lightning-input type="text" label="Country" value={role.physicalCountry} disabled></lightning-input>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="slds-col slds-size_1-of-2">
                                        <lightning-input type="checkbox" label="Use different address for mailing" checked={role.useDifferentMailingAddress} disabled></lightning-input>
                                        <h3 class="slds-text-heading_small">Mailing Address</h3>
                                        <lightning-input type="text" label="Street" value={role.mailingStreet} disabled></lightning-input>
                                        <div class="slds-grid slds-wrap">
                                            <div class="slds-col slds-size_1-of-2 slds-p-right_small">
                                                <lightning-input type="text" label="City" value={role.mailingCity} disabled></lightning-input>
                                            </div>
                                            <div class="slds-col slds-size_1-of-2">
                                                <lightning-input type="text" label="State" value={role.mailingState} disabled></lightning-input>
                                            </div>
                                            <div class="slds-col slds-size_1-of-2 slds-p-right_small">
                                                <lightning-input type="text" label="Postal Code" value={role.mailingPostalCode} disabled></lightning-input>
                                            </div>
                                            <div class="slds-col slds-size_1-of-2">
                                                <lightning-input type="text" label="Country" value={role.mailingCountry} disabled></lightning-input>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="slds-grid slds-wrap slds-p-around_x-small">
                                    <div class="slds-col slds-size_1-of-1 slds-p-right_small">
                                        <h3 class="slds-text-heading_small">Contact Information</h3>
                                        <div class="slds-grid slds-wrap">
                                            <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                <template if:true={role.isControling}>
                                                    <lightning-input type="text" label="Preferred Contact Method" value={role.preferredContactMethod} disabled></lightning-input>
                                                </template>
                                                <lightning-input type="text" label="Email" value={role.contactEmail} disabled></lightning-input>
                                            </div>
                                            <!-- <template if:true={role.isControling}> -->
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="Primary" value={role.formattedPrimaryPhone} disabled></lightning-input>
                                                    <lightning-input type="checkbox" label="Mobile Number" checked={role.primaryIsMobile} disabled></lightning-input>
                                                    <lightning-input type="text" label="Work Phone" value={role.formattedWorkPhone} disabled></lightning-input>                                            
                                                    <lightning-input type="text" label="Secondary" value={role.formattedSecondaryPhone} disabled></lightning-input>
                                                    <lightning-input type="checkbox" label="Mobile Number" checked={role.secondaryIsMobile} disabled></lightning-input>
                                                </div>
                                            <!-- </template> -->
                                        </div>
                                    </div>
                                </div>
                                <template if:true={role.hasPercentage}>
                                    <div class="slds-grid slds-wrap slds-p-around_x-small">
                                        <div class="slds-col slds-size_1-of-1 slds-p-right_small">
                                            <h3 class="slds-text-heading_small">Beneficial Owner Status</h3>
                                            <div class="slds-grid slds-wrap">
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="% Business Owned" value={role.ownerPercent} disabled></lightning-input>
                                                </div>
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="Has Control" value={role.hasControl} disabled></lightning-input>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                                <template if:true={role.isControling}>
                                    <div class="slds-grid slds-wrap slds-p-around_x-small">
                                        <div class="slds-col slds-size_1-of-1 slds-p-right_small">
                                            <h3 class="slds-text-heading_small">Employment Information</h3>
                                            <div class="slds-grid slds-wrap">
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="Employment Status" value={role.employmentStatus} disabled></lightning-input>
                                                </div>
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="Profession/Job Title" value={role.profession} disabled></lightning-input>
                                                </div>
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="Employer" value={role.employer} disabled></lightning-input>
                                                </div>
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="Employment Duration" value={role.employmentDuration} disabled></lightning-input>
                                                </div>
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="Gross Monthly Income (before taxes)" value={role.grossMonthlyIncome} disabled></lightning-input>
                                                </div>
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="Pay Grade" value={role.payGrade} disabled></lightning-input>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="slds-grid slds-wrap slds-p-around_x-small">
                                        <div class="slds-col slds-size_1-of-1 slds-p-right_small">
                                            <h3 class="slds-text-heading_small">Identification Information</h3>
                                            <div class="slds-grid slds-wrap">
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="ID Type" value={role.idType} disabled></lightning-input>
                                                </div>
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="ID State" value={role.idState} disabled></lightning-input>
                                                </div>
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="ID Number" value={role.idNumber} disabled></lightning-input>
                                                </div>
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="ID Expiration Date" value={role.idExpirationDate} disabled></lightning-input>
                                                </div>
                                                <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                                    <lightning-input type="text" label="ID Date Issued" value={role.idDateIssued} disabled></lightning-input>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </td>
                        </tr>
                    </template>
                </template>
            </tbody>
        </table>

        <!-- Due Diligence Activities Section -->
        <h2 class="slds-text-heading_medium slds-m-top_medium">Due Diligence Activities</h2>
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-4 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="General Operation Funds" checked={generalOperationFunds} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="Payroll" checked={payroll} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="Savings" checked={savings} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="Credit Card Processing" checked={creditCardProcessing} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="IOLTA/IOLA" checked={ioltaIola} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="Lottery" checked={lottery} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="MSB Activity" checked={msbActivity} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="Private Banking" checked={privateBanking} disabled></lightning-input>
            </div>
            <div class="slds-col slds-size_1-of-4 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="Other" checked={otherDueDiligence} disabled></lightning-input>
            </div>
            <template if:true={otherDueDiligenceValue}>
                <div class="slds-col slds-size_3-of-4 slds-p-around_xx-small">
                    <lightning-input type="text" label="Other Notes" value={otherDueDiligenceNotes} disabled></lightning-input>
                </div>
            </template>
        </div>
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="Private-label Credit Card Account" checked={privateLabelCardAccount} disabled></lightning-input>
            </div>
            <template if:true={privateLabelCardAccount}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Are accounts established at the point-of-sale, solely for the purchase of retail goods and/or services at the issuing retailer, and have a credit limit of no more than $50,000?"
                        name="retailAccountDetails"
                        options={yesNoOptions}
                        value={retailAccountDetails}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>
            </template>
        </div>
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="Postage Remittance" checked={postageRemittance} disabled></lightning-input>
            </div>
            <template if:true={postageRemittanceValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Is the account solely used to finance the purchase of postage, for which payments are remitted directly by the financial institution to the provider of the postage products?"
                        name="postagePurchaseUsage"
                        options={yesNoOptions}
                        value={postagePurchaseUsage}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>
                <template if:true={postagePurchaseUsageValue}>
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Can the account be used to make payments to, or receive payments from, third parties?"
                            name="thirdPartyPayments"
                            options={yesNoOptions}
                            value={thirdPartyPayments}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Is there a possibility of a cash refund on the account activity?"
                            name="cashRefundPossible"
                            options={yesNoOptions}
                            value={cashRefundPossible}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>
                </template>
            </template>
        </div>
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="Equipment Purchase or Lease" checked={equipmentPurchaseOrLease} disabled></lightning-input>
            </div>
            <template if:true={equipmentPurchaseOrLeaseValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Is the account solely used to finance the purchase or leasing of equipment, for which payments are remitted directly by the financial institution to the provider of the equipment?"
                        name="equipmentFinanceUsage"
                        options={yesNoOptions}
                        value={equipmentFinanceUsage}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>
                <template if:true={equipmentFinanceUsageValue}>
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Can the account be used to make payments to, or receive payments from, third parties?"
                            name="equipmentThirdPartyPayments"
                            options={yesNoOptions}
                            value={equipmentThirdPartyPayments}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Is there a possibility of a cash refund on the account activity?"
                            name="equipmentCashRefundPossible"
                            options={yesNoOptions}
                            value={equipmentCashRefundPossible}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>
                </template>
            </template>
        </div>
        <div class="slds-grid slds-wrap slds-m-bottom_small">            
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="Pooled Investment Vehicle" checked={pooledInvestmentVehicle} disabled></lightning-input>
            </div>
            <template if:true={pooledInvestmentVehicleValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Is the pooled investment vehicle operated or advised by an excluded financial institution?"
                        name="pooledInvestmentExcludedAdvisor"
                        options={yesNoOptions}
                        value={pooledInvestmentExcludedAdvisor}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>
            </template>
            <template if:true={businessStructureSoleProprietorship}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Account(s) also be used for your personal (i.e. non-business) banking activity?"
                        name="pooledPersonalUse"
                        options={yesNoOptions}
                        value={pooledPersonalUse}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>
            </template>
            <template if:false={businessStructureSoleProprietorship}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input type="text" label="Major customers or Vendors" value={pooledMajorCustomerVendor} disabled></lightning-input>
                </div>
            </template>
        </div>

        <!-- Due Diligence Transactions Section -->
        <h2 class="slds-text-heading_medium slds-m-top_medium">Due Diligence Transactions</h2>
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <!-- Row 1 -->
            <template if:false={cashDepositsInPersonValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Cash Deposits in Person" checked={cashDepositsInPerson} disabled></lightning-input>
                </div>
            </template>
            <template if:true={cashDepositsInPersonValue}>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Cash Deposits in Person" checked={cashDepositsInPerson} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="text" label="Average Monthly Amount:" value={cashDepositsInPersonAmount} disabled></lightning-input>
                </div>
            </template>

            <!-- Row 2 -->
            <template if:false={atmCashDepositsValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="ATM Cash Deposits" checked={atmCashDeposits} disabled></lightning-input>
                </div>
            </template>
            <template if:true={atmCashDepositsValue}>
                 <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="ATM Cash Deposits" checked={atmCashDeposits} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="text" label="Average Monthly Amount:" value={atmCashDepositsAmount} disabled></lightning-input>
                </div>
            </template>

            <!-- Row 3 -->
            <template if:false={cashWithdrawalsInPersonValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Cash Withdrawals in Person" checked={cashWithdrawalsInPerson} disabled></lightning-input>
                </div>
            </template>
            <template if:true={cashWithdrawalsInPersonValue}>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Cash Withdrawals in Person" checked={cashWithdrawalsInPerson} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="text" label="Average Monthly Amount:" value={cashWithdrawalsInPersonAmount} disabled></lightning-input>
                </div>
            </template>

            <!-- Row 4 -->
            <template if:false={atmCashWithdrawalsValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="ATM Cash Withdrawals" checked={atmCashWithdrawals} disabled></lightning-input>
                </div>
            </template>
            <template if:true={atmCashWithdrawalsValue}>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="ATM Cash Withdrawals" checked={atmCashWithdrawals} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="text" label="Average Monthly Amount:" value={atmCashWithdrawalsAmount} disabled></lightning-input>
                </div>
            </template>

            <!-- Row 5 -->
            <template if:false={checkDepositsValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Check Deposits" checked={checkDeposits} disabled></lightning-input>
                </div>
            </template>
            <template if:true={checkDepositsValue}>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Check Deposits" checked={checkDeposits} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="text" label="Average Monthly Amount:" value={checkDepositsAmount} disabled></lightning-input>
                </div>
            </template>

            <!-- Row 6 -->
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-input type="checkbox" label="Remote Deposit Capture" checked={remoteDepositCapture} disabled></lightning-input>
            </div>

            <!-- Row 7 -->
            <template if:false={checkWithdrawalsValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Check Withdrawals" checked={checkWithdrawals} disabled></lightning-input>
                </div>
            </template>
            <template if:true={checkWithdrawalsValue}>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Check Withdrawals" checked={checkWithdrawals} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="text" label="Average Monthly Amount:" value={checkWithdrawalsAmount} disabled></lightning-input>
                </div>
            </template>

            <!-- Row 8 -->
            <template if:false={cashiersChecksValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Official Checks/Cashier's Checks" checked={cashiersChecks} disabled></lightning-input>
                </div>
            </template>
            <template if:true={cashiersChecksValue}>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Official Checks/Cashier's Checks" checked={cashiersChecks} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="text" label="Average Monthly Amount:" value={cashiersChecksAmount} disabled></lightning-input>
                </div>
            </template>

            <!-- Row 9 -->
            <template if:false={incomingWireTransfersValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Incoming Wire Transfers" checked={incomingWireTransfers} disabled></lightning-input>
                </div>
            </template>
            <template if:true={incomingWireTransfersValue}>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Incoming Wire Transfers" checked={incomingWireTransfers} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="text" label="Average Monthly Amount:" value={incomingWireTransferAmount} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Will you receive transfers from non-US locations?"
                        name="receiveIntlTransfers"
                        options={yesNoOptions}
                        value={receiveIntlTransfers}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>
                <template if:true={receiveIntlTransferCountries}>
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input type="text" label="Whichs countries:" value={receiveIntlTransferCountries} disabled></lightning-input>
                    </div>
                </template>
            </template>

            <!-- Row 10 -->
            <template if:false={outgoingWireTransfersValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Outgoing Wire Transfers" checked={outgoingWireTransfers} disabled></lightning-input>
                </div>
            </template>
            <template if:true={outgoingWireTransfersValue}>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Outgoing Wire Transfers" checked={outgoingWireTransfers} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="text" label="Average Monthly Amount:" value={outgoingWireTransferAmount} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Will you send transfers to non-US locations?"
                        name="sendIntlTransfers"
                        options={yesNoOptions}
                        value={sendIntlTransfers}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>
                <template if:true={sendIntlTransferCountries}>
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input type="text" label="Which countries:" value={sendIntlTransferCountries} disabled></lightning-input>
                    </div>
                </template>
            </template>

            <!-- Row 11 -->
            <template if:false={incomingNonWireTransfersValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Incoming (Non-Wire) Electronic Transfers" checked={incomingNonWireTransfers} disabled></lightning-input>
                </div>
            </template>
            <template if:true={incomingNonWireTransfersValue}>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Incoming (Non-Wire) Electronic Transfers" checked={incomingNonWireTransfers} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="text" label="Average Monthly Amount:" value={incomingNonWireTransferAmount} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Will you receive transfers from non-US locations?"
                        name="receiveIntlNonWireTransfers"
                        options={yesNoOptions}
                        value={receiveIntlNonWireTransfers}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>
                <template if:true={receiveIntlNonWireCountries}>
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input type="text" label="Which countries:" value={receiveIntlNonWireCountries} disabled></lightning-input>
                    </div>
                </template>
            </template>

            <!-- Row 12 -->
            <template if:false={outgoingNonWireTransfersValue}>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Outgoing (Non-Wire) Electronic Transfers" checked={outgoingNonWireTransfers} disabled></lightning-input>
                </div>
            </template>
            <template if:true={outgoingNonWireTransfersValue}>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="checkbox" label="Outgoing (Non-Wire) Electronic Transfers" checked={outgoingNonWireTransfers} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                    <lightning-input type="text" label="Average Monthly Amount:" value={outgoingNonWireTransferAmount} disabled></lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Will you send transfers to non-US locations?"
                        name="sendIntlNonWireTransfers"
                        options={yesNoOptions}
                        value={sendIntlNonWireTransfers}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>
                <template if:true={sendIntlNonWireCountries}>
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input type="text" label="Which countries:" value={sendIntlNonWireCountries} disabled></lightning-input>
                    </div>
                </template>
            </template>
        </div>

        <!-- Due Diligence Questions Section -->
        <h2 class="slds-text-heading_medium slds-m-top_medium">Due Diligence Questions</h2>
        <!-- Q1 -->
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-radio-group
                    label="Does your business engage in any illegal activities under federal or state law?"
                    name="illegalBusiness"
                    options={yesNoOptions}
                    value={illegalBusiness}
                    type="radio"
                    disabled>
                </lightning-radio-group>
            </div>
            <template if:true={illegalBusinessValue}></template>
        </div> 
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-radio-group
                    label="Does a portion of your income come from Internet Gambling?"
                    name="internetGamblingIncome"
                    options={yesNoOptions}
                    value={internetGamblingIncome}
                    type="radio"
                    disabled>
                </lightning-radio-group>
            </div>
            <template if:true={internetGamblingIncomeValue}></template>
        </div>
        <!-- Q2 -->
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-radio-group
                    label="Is this a marijuana related business?"
                    name="marijuanaRelatedBusiness"
                    options={yesNoOptions}
                    value={marijuanaRelatedBusiness}
                    type="radio"
                    disabled>
                </lightning-radio-group>
            </div>
            <template if:true={marijuanaRelatedBusinessValue}>
                <div class="slds-grid slds-wrap slds-m-bottom_medium">
                    <!-- Q2.1: Are you licensed by the state? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Are you licensed by the state?"
                            name="MarijuanaLicensed__c"
                            options={yesNoOptions}
                            value={marijuanaLicensedValue}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>

                    <!-- Q2.2: % of revenue -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="What percentage of your revenue is derived from marijuana-related activity?"
                            value={marijuanaRevenuePercentageValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q2.3: Activity type -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="What type of marijuana-related activity does your business engage in?"
                            value={marijuanaActivityTypeValue}
                            disabled>
                        </lightning-input>
                    </div>
                </div>
            </template>
        </div>
        <!-- Q3 -->
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-radio-group
                    label="Do you act as a Professional Service Provider - i.e. act as an intermediary between your clients and the bank, performing services or arranging for services to be performed on your client's behalf?"
                    name="professionalServiceProvider"
                    options={yesNoOptions}
                    value={professionalServiceProvider}
                    type="radio"
                    disabled>
                </lightning-radio-group>
            </div>
            <template if:true={professionalServiceProviderValue}>
                <div class="slds-grid slds-wrap slds-m-bottom_medium">
                    <!-- Q3.1: What is the primary type of Professional Service Provider? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="What is the primary type of Professional Service Provider that best matches your business type?"
                            value={professionalTypeValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q3.2: Description (if 'Other' selected) -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="Description"
                            value={professionalTypeOtherTextValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q3.3: What services do you provide? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="What services do you provide?"
                            value={servicesTypesValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q3.4: Will other professionals be using this account? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Will other professionals be using this account?"
                            name="ProfessionalOthersUsing__c"
                            options={yesNoOptions}
                            value={professionalOthersUsingValue}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>
                </div>
            </template>
        </div>
        <!-- Q4 -->
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-radio-group
                    label="Does your company offer courier services or armored car services to ship currency on your customer’s behalf?"
                    name="courierServices"
                    options={yesNoOptions}
                    value={courierServices}
                    type="radio"
                    disabled>
                </lightning-radio-group>
            </div>
            <template if:true={courierServicesValue}></template>
        </div>
        <!-- Q5 -->
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-radio-group
                    label="Will you be processing transactions that benefit a third party as a payment processor?"
                    name="paymentProcessor"
                    options={yesNoOptions}
                    value={paymentProcessor}
                    type="radio"
                    disabled>
                </lightning-radio-group>
            </div>
            <template if:true={paymentProcessorValue}>
                <div class="slds-grid slds-wrap slds-m-bottom_medium">
                    <!-- Q5.1: Do you send payments on behalf of your clients? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Do you send payments on behalf of your clients?"
                            checked={transactionsSendPaymentsChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q5.2: Do you receive payments on behalf of your clients? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Do you receive payments on behalf of your clients?"
                            checked={transactionsReceivePaymentsChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q5.3: What types of payment services do you offer? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="What types of payment services do you offer?"
                            value={paymentServicesValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q5.4: Description (if other selected) -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="Description"
                            value={paymentServicesOtherTextValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q5.5: Will client payments run through your accounts with us? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Will client payments run through your accounts with us?"
                            name="PaymentsThroughAccounts__c"
                            options={yesNoOptions}
                            value={paymentsThroughAccountsValue}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>

                    <!-- Q5.6: How are client transactions processed through your account? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="How are client transactions processed through your account?"
                            value={paymentsHowProcessedValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q5.7: Do you create checks remotely on behalf of your clients? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Do you create checks remotely on behalf of your clients?"
                            name="CreateChecksRemotely__c"
                            options={yesNoOptions}
                            value={createChecksRemotelyValue}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>

                    <!-- Q5.8: Do you have any restrictions on the types of businesses you accept as customers? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Do you have any restrictions on the types of businesses you accept as customers?"
                            name="BusinessTypeRestrictions__c"
                            options={yesNoOptions}
                            value={businessTypeRestrictionsValue}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>

                    <!-- Q5.9: What are your restrictions? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="What are your restrictions?"
                            value={businessTypeRestrictionsTextValue}
                            disabled>
                        </lightning-input>
                    </div>
                </div>
            </template>
        </div>
        <!-- Q6 -->
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-radio-group
                    label="Do you own, operate, or replenish an ATM?"
                    name="atmOperator"
                    options={yesNoOptions}
                    value={atmOperator}
                    type="radio"
                    disabled>
                </lightning-radio-group>
            </div>
            <template if:true={atmOperatorValue}>
                <div class="slds-grid slds-wrap slds-m-bottom_medium">
                    <!-- Q6.1: How many ATMs do you own, operate, or replenish? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="How many ATMs do you own, operate, or replenish?"
                            value={numberOfAtmValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q6.2: Do you have access to replenish the cash within the ATM? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Do you have access to replenish the cash within the ATM?"
                            name="ReplenishAtmCash__c"
                            options={yesNoOptions}
                            value={replenishAtmCashValue}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>

                    <!-- Q6.3: What is the source of cash? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="What is the source of cash?"
                            value={sourceOfAtmCashValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q6.4: What is the maximum amount that any one of your ATM(s) can hold? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="What is the maximum amount that any one of your ATM(s) can hold?"
                            value={atmMaxHoldingValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q6.5: What denominations are dispensed from your machines? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="What denominations are dispensed from your machines?"
                            value={atmDenominationValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q6.6: What is the type of your private ATM? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="What is the type of your private ATM?"
                            value={privateAtmTypeValue}
                            disabled>
                        </lightning-input>
                    </div>
                </div>
            </template>
        </div>
        <!-- Q7 -->
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-radio-group
                    label="Could your business be considered a Non-Bank Financial Institution?"
                    name="nbfi"
                    options={yesNoOptions}
                    value={nbfi}
                    type="radio"
                    disabled>
                </lightning-radio-group>
            </div>
            <template if:true={nbfiValue}>
                <div class="slds-grid slds-wrap slds-m-bottom_medium">
                    <!-- Q7.1: Casinos, card clubs or gaming establishments -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Does your business involve casinos, card clubs or gaming establishments (with annual revenues greater than one million dollars)?"
                            checked={involveCasinosChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.2: Securities, futures commissions, or commodity trading -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Does your business involve securities, futures commissions or commodity trading?"
                            checked={involveSecuritiesChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.2.1: Is the institution one of the following? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="Is the Nonbank financial institution one of the following?"
                            value={securitiesFinancialInstitutionValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.2.2: How is your business registered? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="How is your business registered?"
                            value={securitiesHowBusinessRegisteredValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.2.3: Do you invest your clients' funds? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Do you invest your clients' funds?"
                            name="SecuritiesInvolveSecurities__c"
                            options={yesNoOptions}
                            value={securitiesInvolveSecuritiesValue}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>

                    <!-- Q7.2.4: In what types of products do you invest? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="In what types of products do you invest?"
                            value={securitiesProductTypesValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.2.5: Do you invest internationally? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Do you invest your clients' funds internationally?"
                            name="SecuritiesInvestFundsInternationally__c"
                            options={yesNoOptions}
                            value={securitiesInvestFundsInternationallyValue}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>

                    <!-- Q7.2.6: In what countries? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="In what countries do you invest your clients' funds internationally?"
                            value={securitiesCountriesTextValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.2.7: What types of services apply to your business? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="What types of services apply to your business?"
                            value={securitiesServiceTypesValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.3: Insurance -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Insurance"
                            checked={involveInsuranceChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.3.1: State-regulated insurance company? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Is this a State-regulated insurance company?"
                            name="InvolveInsuranceStateRegIns__c"
                            options={yesNoOptions}
                            value={involveInsuranceStateRegInsValue}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>

                    <!-- Q7.4: Loan/Finance -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Loan/Finance"
                            checked={involveLoanFinanceChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.5: Credit cards system operation -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Credit cards system operation"
                            checked={involveCreditCardsChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.6: Precious metals, stones, or jewels -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Precious metals, stones, or jewels (with purchases or sales of more than $50,000 per year)"
                            checked={involvePreciousMetalsChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.6.1: Did you buy > $50k from non-US sources last year? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="In the previous year, did your business purchase more than $50,000 of jewels, precious metals, or precious stones from anyone other than US-based dealers or retailers?"
                            name="InvolvePreciousMetalsBuy50k__c"
                            options={yesNoOptions}
                            value={involvePreciousMetalsBuy50kValue}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>

                    <!-- Q7.6.2: Did you sell > $50k last year? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Did your business sell more than $50,000 of jewels, precious metals, or precious stones in the previous year?"
                            name="InvolvePreciousMetalsSell50k__c"
                            options={yesNoOptions}
                            value={involvePreciousMetalsSell50kValue}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>

                    <!-- Q7.7: Pawn brokerage -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Pawn brokerage"
                            checked={involvePawnBrokerageChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.8: Travel agency -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Travel agency"
                            checked={involveTravelAgencyChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.9: Telegraph company -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Telegraph company"
                            checked={involveTelegraphCompanyChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.10: Vehicle sales -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Vehicle sales"
                            checked={involveVehicleSalesChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.10.1: What types of vehicles do you sell? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="What types of transportation vehicles do you sell?"
                            value={involveVehicleTypesValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.11: Real estate closing and settlement -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Real estate closing and settlement"
                            checked={involveRealEstateClosingChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.12: U.S. Postal Service -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="U.S. Postal Service"
                            checked={involvePostalServiceChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.13: Government agency -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Federal, state or local government agency carrying out a duty or power of a business described above"
                            checked={involveGovAgencyChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q7.14: BSA/AML program -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-radio-group
                            label="Do you have a documented BSA/AML program?"
                            name="DocBsaAmlProgram__c"
                            options={yesNoOptions}
                            value={docBsaAmlProgramValue}
                            type="radio"
                            disabled>
                        </lightning-radio-group>
                    </div>

                </div>
            </template>
        </div>
        <!-- Q8 -->
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                <lightning-radio-group
                    label="Could your business be considered a Money Service Business?"
                    name="msb"
                    options={yesNoOptions}
                    value={msb}
                    type="radio"
                    disabled>
                </lightning-radio-group>
            </div>
            <template if:true={msbValue}>
                <div class="slds-grid slds-wrap slds-m-bottom_medium slds-m-left_medium">
                    <!-- Q8.1: Foreign currency exchange -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Foreign currency exchange in amounts greater than $1,000 for any one person in any one day"
                            checked={involveCurrencyExchangeChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q8.1.1: Do you act as an Agent? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Do you act as an Agent?"
                            checked={currencyExchangeAgentValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q8.1.2: Do you act as a Principal? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Do you act as a Principals?"
                            checked={currencyExchangePrincipalValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q8.2: Cash checks -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Cash checks in amounts greater than $1,000 for any one person in any one day"
                            checked={involveCashChecksChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q8.2.1: What type of checks does your business cash? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="text"
                            label="What type of checks does your business cash?"
                            value={involveCheckTypesValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q8.3: Issue or sell money orders -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Issue or sell money orders in amounts greater than $1,000 to any one person in any one day"
                            checked={involveMoneyOrdersChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q8.3.1: Do you act as an Agent? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Do you act as an Agent?"
                            value={moneyOrdersAgentValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q8.3.2: Do you act as a Principal? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Do you act as a Principal?"
                            value={moneyOrdersPrincipalValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q8.4: Transmit money electronically -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Transmit money on your customer's behalf electronically from one location to another"
                            checked={involveTransmitMoneyChecked}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q8.4.1: Do you act as an Agent? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Do you act as an Agent?"
                            value={transmitMoneyAgentValue}
                            disabled>
                        </lightning-input>
                    </div>

                    <!-- Q8.4.2: Do you act as a Principal? -->
                    <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                        <lightning-input
                            type="checkbox"
                            label="Do you act as a Principal?"
                            value={transmitMoneyPrincipalValue}
                            disabled>
                        </lightning-input>
                    </div>
                </div>

                <!-- Q8.4.3: Will you transmit to non-US locations? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Will you transmit to non-US locations?"
                        name="TransmitMoneyNonUsLocations__c"
                        options={yesNoOptions}
                        value={transmitMoneyNonUsLocationsValue}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>

                <!-- Q8.4.4: To which foreign countries will transactions be sent? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input
                        type="text"
                        label="To which foreign countries will transactions be sent?"
                        value={transmitForeignCountriesTextValue}
                        disabled>
                    </lightning-input>
                </div>

                <!-- Q8.4.5: What type of money will be transmitted? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input
                        type="text"
                        label="What type of money will be transmitted?"
                        value={transmitMoneyTypesValue}
                        disabled>
                    </lightning-input>
                </div>

                <!-- Q8.4.6: Does your business involve convertible virtual currency (CVC)? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Does your business involve convertible virtual currency (CVC)?"
                        name="TransmitMoneyCvc__c"
                        options={yesNoOptions}
                        value={transmitMoneyCvcValue}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>

                <!-- Q8.4.7: Which CVC-related activities does your business include? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input
                        type="text"
                        label="Which CVC-related activities does your business include?"
                        value={transmitMoneyActivitiesValue}
                        disabled>
                    </lightning-input>
                </div>

                <!-- Q8.5: Administer or exchange virtual currency -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input
                        type="checkbox"
                        label="Administer or exchange virtual currency"
                        checked={involveVirtualCurrencyChecked}
                        disabled>
                    </lightning-input>
                </div>

                <!-- Q8.6: Provide or sell prepaid access to funds (e.g., gift cards) -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input
                        type="checkbox"
                        label="Provide or sell prepaid access to funds, such as gift cards or other devices used to transfer value"
                        checked={involveGiftCardsChecked}
                        disabled>
                    </lightning-input>
                </div>

                <!-- Q8.6.1: Do you act as an Agent? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input
                        type="checkbox"
                        label="Do you act as an Agent?"
                        value={giftCardAgentValue}
                        disabled>
                    </lightning-input>
                </div>

                <!-- Q8.6.2: Do you act as a Principal? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input
                        type="checkbox"
                        label="Do you act as a Principal?"
                        value={giftCardPrincipalValue}
                        disabled>
                    </lightning-input>
                </div>

                <!-- Q8.6.3: Do you sell non-network-branded prepaid access that exceeds $2,000/day? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Do you sell non-network-branded prepaid access to funds that exceed $2,000 per device during any one day?"
                        name="GiftCardExceedDailyMax__c"
                        options={yesNoOptions}
                        value={giftCardExceedDailyMaxValue}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>

                <!-- Q8.6.4: Does access to funds require activation + customer ID? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Does access to funds require an activation process that includes customer identification?"
                        name="GiftCardActivationProcess__c"
                        options={yesNoOptions}
                        value={giftCardActivationProcessValue}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>

                <!-- Q8.6.5: Do you sell network-branded prepaid access? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Do you sell network-branded prepaid access (e.g., Visa/Mastercard gift cards)?"
                        name="GiftCardNetworkBranded__c"
                        options={yesNoOptions}
                        value={giftCardNetworkBrandedValue}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>

                <!-- Q8.6.6: Do devices permit access to funds exceeding $1,000/day? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Do devices permit access to funds that exceed $1,000 maximum value per device during any one day?"
                        name="GiftCardAccessDailyMax__c"
                        options={yesNoOptions}
                        value={giftCardAccessDailyMaxValue}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>

                <!-- Q8.6.7: Can devices be reloaded from non-depository sources? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Can devices be reloaded from non-depository sources?"
                        name="GiftCardReloaded__c"
                        options={yesNoOptions}
                        value={giftCardReloadedValue}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>

                <!-- Q8.6.8: Can devices be used to transfer funds from one person to another? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Can devices be used to transfer funds from one person to another?"
                        name="GiftCardTransferFunds__c"
                        options={yesNoOptions}
                        value={giftCardTransferFundsValue}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>

                <!-- Q8.6.9: Can devices transmit funds internationally? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Can devices be used to transmit funds internationally?"
                        name="GiftCardTransferFundsInternationally__c"
                        options={yesNoOptions}
                        value={giftCardTransferFundsInternationallyValue}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>

                <!-- Q8.6.10: Does access to funds require customer identification? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Does access to funds require an activation process that includes customer identification?"
                        name="GiftCardRequireCustomerId__c"
                        options={yesNoOptions}
                        value={giftCardRequireCustomerIdValue}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>

                <!-- Q8.7: $10,000 daily sales policy in place? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Are there policies and procedures in place to reasonably prevent the sale of more than $10,000 of any type of prepaid access to any one person during any one day?"
                        name="GiftCardPreventSales__c"
                        options={yesNoOptions}
                        value={giftCardPreventSalesValue}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>

                <!-- Q8.8: Do you have a documented BSA/AML program? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Do you have a documented BSA/AML program?"
                        name="GiftCardBsaAmlProg__c"
                        options={yesNoOptions}
                        value={giftCardBsaAmlProgValue}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>

                <!-- Q8.9: Are you registered with FinCEN and State? -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-radio-group
                        label="Are you registered with FinCEN and (if required) by the State?"
                        name="RegisteredFinCen__c"
                        options={yesNoOptions}
                        value={registeredFinCenValue}
                        type="radio"
                        disabled>
                    </lightning-radio-group>
                </div>

                <!-- Q8.10: None of the above -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_xx-small">
                    <lightning-input
                        type="checkbox"
                        label="None of the above"
                        checked={involveNoneChecked}
                        disabled>
                    </lightning-input>
                </div>

            </template>
        </div>

        <!-- Funding Source Section -->
        <h2 class="slds-text-heading_medium slds-m-top_medium">Funding Source</h2>
        <div class="slds-grid slds-wrap slds-m-bottom_small">
            <!-- Left side: Deposit amounts -->
            <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                <lightning-input type="text" label="Business Savings ($5.00 minimum)" value={businessSavingsDeposit} disabled></lightning-input>
                <lightning-input type="text" label="Business Essential Checking ($100.00 minimum)" value={businessCheckingDeposit} disabled></lightning-input>
                <lightning-input type="text" label="Total Deposit" value={totalDeposit} disabled></lightning-input>
            </div>

            <!-- Right side: Funding details -->
            <div class="slds-col slds-size_1-of-2 slds-p-around_xx-small">
                <lightning-input type="text" label="Funding Type" value={fundingType} disabled></lightning-input>
                <template if:true={fundingTypeAccounts}>
                    <lightning-input type="text" label="Account Type" value={fundingAccountType} disabled></lightning-input>
                    <lightning-input type="text" label="Name On Account" value={nameOnAccount} disabled></lightning-input>
                    <lightning-input type="text" label="Account Number" value={accountNumber} disabled></lightning-input>
                    <template if:true={fundingTypeTransferAnotherAccount}>
                        <lightning-input type="text" label="Routing Number" value={routingNumber} disabled></lightning-input>
                        <lightning-input type="text" label="Financial Institution" value={financialInstitution} disabled></lightning-input>
                        <lightning-input type="text" label="Financial Institution State" value={financialInstitutionState} disabled></lightning-input>
                    </template>
                </template>
            </div>
        </div>
    </div>

    <lightning-button
        label="Download PDF"
        onclick={generatePdf}
        variant="brand">
    </lightning-button>
</template>