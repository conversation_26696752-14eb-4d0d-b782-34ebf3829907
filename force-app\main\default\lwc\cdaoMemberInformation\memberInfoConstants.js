// Field definitions with validation rules
export const FIELD_DEFINITIONS = {
    First_Name: { 
        required: true, 
        validationFn: 'validateName', 
        validationArgs: [true, 'First name'],
        label: 'First Name',
        type: 'text',
        maxLength: 40
    },
    Middle_Name: { 
        required: false, 
        validationFn: 'validateName', 
        validationArgs: [false, 'Middle name'],
        label: 'Middle Name',
        type: 'text',
        maxLength: 40
    },
    Last_Name: { 
        required: true, 
        validationFn: 'validateName', 
        validationArgs: [true, 'Last name'],
        label: 'Last Name',
        type: 'text',
        maxLength: 80
    },
    Suffix: { 
        required: false,
        label: 'Suffix',
        type: 'picklist'
    },
    Date_of_Birth: { 
        required: true, 
        validationFn: 'validateDOB',
        label: 'Date of Birth',
        type: 'date'
    },
    SSN_TIN: { 
        required: true, 
        validationFn: 'validateSSN',
        label: 'Social Security Number',
        type: 'text',
        maxLength: 11,
        pattern: '###-##-####',
        sensitive: true
    },
    Email_Address: { 
        required: true, 
        validationFn: 'validateEmail',
        label: 'Email Address',
        type: 'email',
        maxLength: 80
    },
    Phone: { 
        required: false, 
        validationFn: 'validatePhone',
        label: 'Phone Number',
        type: 'tel',
        maxLength: 14,
        pattern: '(###) ###-####'
    },
    Individual_Role: { 
        required: true,
        label: 'Role',
        type: 'hidden',
        defaultValue: 'Primary'
    }
};

// Heading text
export const HEADINGS = {
    PRIMARY_MEMBER_TYPE: 'Please provide us with a few details to get started.',
    PRIMARY_MEMBER_INFO: 'Member Information (Primary)',
    JOINT_MEMBER_TYPE: 'Please provide details for the joint applicant.',
    JOINT_MEMBER_INFO: 'Member Information (Joint)'
};

// Error messages
export const ERROR_MESSAGES = {
    REQUIRED_FIELD: 'This field is required.',
    INVALID_NAME: 'Please enter a valid name (letters, hyphens, and apostrophes only).',
    INVALID_EMAIL: 'Please enter a valid email address.',
    INVALID_PHONE: 'Please enter a valid phone number.',
    INVALID_SSN: 'Please enter a valid Social Security Number (XXX-XX-XXXX).',
    INVALID_DOB: 'Please enter a valid date of birth.',
    FUTURE_DATE: 'Date of birth cannot be in the future.',
    MINOR_AGE: 'Applicant must be at least 18 years old.'
};

// Regular expressions for validation
export const VALIDATION_PATTERNS = {
    NAME: /^[a-zA-Z\s\-']+$/,
    EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
    PHONE: /^\(\d{3}\) \d{3}-\d{4}$/,
    SSN: /^\d{3}-\d{2}-\d{4}$/
};


// Event names
export const EVENTS = {
    NEXT: 'next',
    BACK: 'back',
    SAVE: 'save'
};