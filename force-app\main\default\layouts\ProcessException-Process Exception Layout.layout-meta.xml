<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>ProcessExceptionInformation</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Severity</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>AttachedToId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CaseId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalReference</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ProcessExceptionNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Category</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Priority</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Message</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
