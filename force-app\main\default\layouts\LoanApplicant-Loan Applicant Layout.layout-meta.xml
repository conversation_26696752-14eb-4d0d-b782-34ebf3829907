<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>LoanApplicationId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ContactId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BorrowerType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MaritalStatus</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DependentCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DependentsAge</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsJointCredit</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EthnicityType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RaceType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>GenderType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CitizenshipStatus</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HasMilitaryService</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsActiveMilitary</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MilitaryTourExpirationMonth</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MilitaryTourExpirationYear</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>WasActiveMilitary</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>WasNonActivatedService</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsSurvivingMilitarySpouse</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PreferredLanguage</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EthnicitySubType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OtherEthnicitySubType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AsianRaceSubType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OtherAsianRaceSubType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PacIslandSubType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OtherPacIslandSubType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TribeName</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
