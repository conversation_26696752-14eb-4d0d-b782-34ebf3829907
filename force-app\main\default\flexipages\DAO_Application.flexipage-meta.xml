<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Applicant_Read_Checkmark__c</fieldItem>
                <identifier>RecordApplicant_Read_Checkmark__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Applicant_Agreement__c</fieldItem>
                <identifier>RecordApplicant_Agreement__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Fictitious_Business_Name_DBA__c</fieldItem>
                <identifier>RecordFictitious_Business_Name_DBA__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Work_Phone__c</fieldItem>
                <identifier>RecordWork_Phone__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Secondary_Phone__c</fieldItem>
                <identifier>RecordSecondary_Phone__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Business_Annual_Revenue__c</fieldItem>
                <identifier>RecordBusiness_Annual_Revenue__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.DAO_Application_Status__c</fieldItem>
                <identifier>RecordDAO_Application_Status__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Application_Submitted__c</fieldItem>
                <identifier>RecordApplication_Submitted__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Fraud_Review_Needed__c</fieldItem>
                <identifier>RecordFraud_Review_Needed__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Previous_State__c</fieldItem>
                <identifier>RecordPrevious_State__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Previous_Zip__c</fieldItem>
                <identifier>RecordPrevious_Zip__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Withdrawal_Cancellation_Reason__c</fieldItem>
                <identifier>RecordDeclined_Reason__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.MarijuanaLicensed__c</fieldItem>
                <identifier>RecordMarijuanaLicensed__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.MarijuanaPercentage__c</fieldItem>
                <identifier>RecordMarijuanaPercentage__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.MarijuanaActivity__c</fieldItem>
                <identifier>RecordMarijuanaActivity__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.ProfessionalType__c</fieldItem>
                <identifier>RecordProfessionalType__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.ServicesTypes__c</fieldItem>
                <identifier>RecordServicesTypes__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.ProfessionalOthersUsing__c</fieldItem>
                <identifier>RecordProfessionalOthersUsing__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.TransactionsSendPayments__c</fieldItem>
                <identifier>RecordTransactionsSendPayments__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.TransactionsReceivePayments__c</fieldItem>
                <identifier>RecordTransactionsReceivePayments__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.PaymentServices__c</fieldItem>
                <identifier>RecordPaymentServices__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.PaymentsThroughAccounts__c</fieldItem>
                <identifier>RecordPaymentsThroughAccounts__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.PaymentsHowProcessed__c</fieldItem>
                <identifier>RecordPaymentsHowProcessed__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.CreateChecksRemotely__c</fieldItem>
                <identifier>RecordCreateChecksRemotely__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.BusinessTypeRestrictions__c</fieldItem>
                <identifier>RecordBusinessTypeRestrictions__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.BusinessTypeRestrictionsText__c</fieldItem>
                <identifier>RecordBusinessTypeRestrictionsText__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.NumberOfAtm__c</fieldItem>
                <identifier>RecordNumberOfAtm__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.ReplenishAtmCash__c</fieldItem>
                <identifier>RecordReplenishAtmCash__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.SourceOfAtmCash__c</fieldItem>
                <identifier>RecordSourceOfAtmCash__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.AtmMaxHolding__c</fieldItem>
                <identifier>RecordAtmMaxHolding__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.AtmDenomination__c</fieldItem>
                <identifier>RecordAtmDenomination__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.PrivateAtmType__c</fieldItem>
                <identifier>RecordPrivateAtmType__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveCasinos__c</fieldItem>
                <identifier>RecordInvolveCasinos__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveSecurities__c</fieldItem>
                <identifier>RecordInvolveSecurities__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.SecuritiesFinancialInstitution__c</fieldItem>
                <identifier>RecordSecuritiesFinancialInstitution__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.SecuritiesHowBusinessRegistered__c</fieldItem>
                <identifier>RecordSecuritiesHowBusinessRegistered__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.SecuritiesInvolveSecurities__c</fieldItem>
                <identifier>RecordSecuritiesInvolveSecurities__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.SecuritiesProductTypes__c</fieldItem>
                <identifier>RecordSecuritiesProductTypes__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.SecuritiesInvestFundsInternationally__c</fieldItem>
                <identifier>RecordSecuritiesInvestFundsInternationally__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.SecuritiesCountriesText__c</fieldItem>
                <identifier>RecordSecuritiesCountriesText__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.SecuritiesServiceTypes__c</fieldItem>
                <identifier>RecordSecuritiesServiceTypes__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveInsurance__c</fieldItem>
                <identifier>RecordInvolveInsurance__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveInsuranceStateRegIns__c</fieldItem>
                <identifier>RecordInvolveInsuranceStateRegIns__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveLoanFinance__c</fieldItem>
                <identifier>RecordInvolveLoanFinance__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveCreditCards__c</fieldItem>
                <identifier>RecordInvolveCreditCards__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolvePreciousMetals__c</fieldItem>
                <identifier>RecordInvolvePreciousMetals__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolvePreciousMetalsBuy50k__c</fieldItem>
                <identifier>RecordInvolvePreciousMetalsBuy50k__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolvePreciousMetalsSell50k__c</fieldItem>
                <identifier>RecordInvolvePreciousMetalsSell50k__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolvePawnBrokerage__c</fieldItem>
                <identifier>RecordInvolvePawnBrokerage__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveTravelAgency__c</fieldItem>
                <identifier>RecordInvolveTravelAgency__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveTelegraphCompany__c</fieldItem>
                <identifier>RecordInvolveTelegraphCompany__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveVehicleSales__c</fieldItem>
                <identifier>RecordInvolveVehicleSales__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveVehicleTypes__c</fieldItem>
                <identifier>RecordInvolveVehicleTypes__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveRealEstateClosing__c</fieldItem>
                <identifier>RecordInvolveRealEstateClosing__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolvePostalService__c</fieldItem>
                <identifier>RecordInvolvePostalService__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveGovAgency__c</fieldItem>
                <identifier>RecordInvolveGovAgency__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveNone__c</fieldItem>
                <identifier>RecordInvolveNone__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.DocBsaAmlProgram__c</fieldItem>
                <identifier>RecordDocBsaAmlProgram__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveCurrencyExchange__c</fieldItem>
                <identifier>RecordInvolveCurrencyExchange__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveCurencyExchangeAgentPrincipal__c</fieldItem>
                <identifier>RecordInvolveCurencyExchangeAgentPrincipal__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveCashChecks__c</fieldItem>
                <identifier>RecordInvolveCashChecks__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveCheckTypes__c</fieldItem>
                <identifier>RecordInvolveCheckTypes__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveMoneyOrders__c</fieldItem>
                <identifier>RecordInvolveMoneyOrders__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveMoneyOrdersAgentPrincipal__c</fieldItem>
                <identifier>RecordInvolveMoneyOrdersAgentPrincipal__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveTransmitMoney__c</fieldItem>
                <identifier>RecordInvolveTransmitMoney__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.TransmitMoneyAgentPrincipal__c</fieldItem>
                <identifier>RecordTransmitMoneyAgentPrincipal__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.TransmitMoneyNonUsLocations__c</fieldItem>
                <identifier>RecordTransmitMoneyNonUsLocations__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.TransmitForeignCountriesText__c</fieldItem>
                <identifier>RecordTransmitForeignCountriesText__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.TransmitMoneyTypes__c</fieldItem>
                <identifier>RecordTransmitMoneyTypes__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.TransmitMoneyCvc__c</fieldItem>
                <identifier>RecordTransmitMoneyCvc__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.TransmitMoneyActivities__c</fieldItem>
                <identifier>RecordTransmitMoneyActivities__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.InvolveGiftCards__c</fieldItem>
                <identifier>RecordInvolveGiftCards__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.GiftCardAgentPrincipal__c</fieldItem>
                <identifier>RecordGiftCardAgentPrincipal__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.GiftCardExceedDailyMax__c</fieldItem>
                <identifier>RecordGiftCardExceedDailyMax__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.GiftCardActivationProcess__c</fieldItem>
                <identifier>RecordGiftCardActivationProcess__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.GiftCardNetworkBranded__c</fieldItem>
                <identifier>RecordGiftCardNetworkBranded__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.GiftCardAccessDailyMax__c</fieldItem>
                <identifier>RecordGiftCardAccessDailyMax__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.GiftCardReloaded__c</fieldItem>
                <identifier>RecordGiftCardReloaded__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.GiftCardTransferFunds__c</fieldItem>
                <identifier>RecordGiftCardTransferFunds__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.GiftCardTransferFundsInternationally__c</fieldItem>
                <identifier>RecordGiftCardTransferFundsInternationally__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.GiftCardRequireCustomerId__c</fieldItem>
                <identifier>RecordGiftCardRequireCustomerId__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.GiftCardPreventSales__c</fieldItem>
                <identifier>RecordGiftCardPreventSales__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.GiftCardBsaAmlProg__c</fieldItem>
                <identifier>RecordGiftCardBsaAmlProg__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.RegisteredFinCen__c</fieldItem>
                <identifier>RecordRegisteredFinCen__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.PaymentServicesOtherText__c</fieldItem>
                <identifier>RecordPaymentServicesOtherText__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.ProfessionalTypeOtherText__c</fieldItem>
                <identifier>RecordProfessionalTypeOtherText__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Involve_Curency_Exchange_Agent__c</fieldItem>
                <identifier>RecordInvolve_Curency_Exchange_Agent__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Involve_Curency_Exchange_Principal__c</fieldItem>
                <identifier>RecordInvolve_Curency_Exchange_Principal__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Involve_Money_Orders_Agent__c</fieldItem>
                <identifier>RecordInvolve_Money_Orders_Agent__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Involve_Money_Orders_Principal__c</fieldItem>
                <identifier>RecordInvolve_Money_Orders_Principal__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Transmit_Money_Agent__c</fieldItem>
                <identifier>RecordTransmit_Money_Agent__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Transmit_Money_Principal__c</fieldItem>
                <identifier>RecordTransmit_Money_Principal__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Gift_Card_Agent__c</fieldItem>
                <identifier>RecordGift_Card_Agent__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Gift_Card_Principal__c</fieldItem>
                <identifier>RecordGift_Card_Principal__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Meridian_Link_Loan_Id__c</fieldItem>
                <identifier>RecordMeridian_Link_Loan_Id__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Meridian_Link_Loan_Number__c</fieldItem>
                <identifier>RecordMeridian_Link_Loan_Number__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.NAICS_Title__c</fieldItem>
                <identifier>RecordNAICS_Title__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.NAICS_Code_Text__c</fieldItem>
                <identifier>RecordNAICS_Code_Text__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldItem>Record.Other_Occupancy_Status__c</fieldItem>
                <identifier>RecordOther_Occupancy_Status__cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-cff7a320-e3ff-494f-94df-603cd78e29d8</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>Facet-c2c1efa2-a51b-46e7-b869-4c302582cf93</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-cff7a320-e3ff-494f-94df-603cd78e29d8</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-c2c1efa2-a51b-46e7-b869-4c302582cf93</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-365d4bad-7847-491e-91b9-1b2e590bc31c</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Account_Type_Checking_or_Savings__c</fieldItem>
                <identifier>RecordAccount_Type_Checking_or_Savings_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Business_Essential_Checking__c</fieldItem>
                <identifier>RecordBusiness_Essential_Checking_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-d9cae030-c35b-490d-b972-4e12c62b2a24</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Account_Type_Checking_or_Savings__c</fieldItem>
                <identifier>RecordAccount_Type_Checking_or_Savings_cField2</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-c2b43b44-99d4-4b5d-be75-d689e308a0fe</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-d9cae030-c35b-490d-b972-4e12c62b2a24</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-c2b43b44-99d4-4b5d-be75-d689e308a0fe</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column4</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-6ac3a9a5-3c5e-4bb3-ab1a-84d8f39b9c7b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Billing_Address__c</fieldItem>
                <identifier>RecordBilling_Address_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Billing_City__c</fieldItem>
                <identifier>RecordBilling_City_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Billing_State__c</fieldItem>
                <identifier>RecordBilling_State_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Billing_Zip__c</fieldItem>
                <identifier>RecordBilling_Zip_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-744f818b-7e80-4d67-abf4-26cb61fe9c6b</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Mailing_Street_Address__c</fieldItem>
                <identifier>RecordMailing_Street_Address_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.MailingCity__c</fieldItem>
                <identifier>RecordMailingCity_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.MailingState__c</fieldItem>
                <identifier>RecordMailingState_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Mailing_Zip__c</fieldItem>
                <identifier>RecordMailing_Zip_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-01fab674-037d-4c51-8ab9-5e1a7c9881fa</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-744f818b-7e80-4d67-abf4-26cb61fe9c6b</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column5</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-01fab674-037d-4c51-8ab9-5e1a7c9881fa</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column6</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-9355ebba-dec6-4c2a-9bc5-83dd761761e4</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-365d4bad-7847-491e-91b9-1b2e590bc31c</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Applicant Information</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-6ac3a9a5-3c5e-4bb3-ab1a-84d8f39b9c7b</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Product Selection</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-9355ebba-dec6-4c2a-9bc5-83dd761761e4</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Section</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection3</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>DAO Application</masterLabel>
    <sobjectType>DAO_Application__c</sobjectType>
    <template>
        <name>flexipage:recordHomeTemplateDesktop</name>
        <properties>
            <name>enablePageActionConfig</name>
            <value>false</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
