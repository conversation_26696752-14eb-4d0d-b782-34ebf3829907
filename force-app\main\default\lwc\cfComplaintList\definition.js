let definition =
      {"states":[{"fields":[],"conditions":{"id":"state-condition-object","isParent":true,"group":[]},"definedActions":{"actions":[]},"name":"Active","isSmartAction":false,"smartAction":{},"styleObject":{"padding":[],"margin":[{"type":"around","size":"xxx-small","label":"around:xxx-small"}],"container":{"class":"slds-card"},"size":{"isResponsive":false,"default":"12"},"sizeClass":"slds-size_12-of-12 ","class":"slds-card slds-m-around_xxx-small ","background":{"color":"","image":"","size":"","repeat":"","position":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"      \n         "},"components":{"layer-0":{"children":[{"name":"Block","element":"block","size":{"isResponsive":true,"default":"12","large":"12","medium":"12","small":"12"},"stateIndex":0,"class":"slds-col ","property":{"label":"Block","collapsible":false,"record":"{record}","collapsedByDefault":false,"card":"{card}","action":{"label":"Action","iconName":"standard-default","eventType":"onclick","actionList":[{"key":"1675671675260-u64e7wqym","label":"Action","draggable":false,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-1681365634805","type":"Custom","displayName":"Action","vlocityIcon":"standard-default","targetType":"Web Page","openUrlIn":"New Tab/Window","Web Page":{"targetName":"{caseLink}"}},"actionIndex":0}],"showSpinner":"false"}},"type":"block","styleObject":{"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small slds-scrollable_y","sizeClass":"slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 ","size":{"isResponsive":true,"default":"12","large":"12","medium":"12","small":"12"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":["border_top","border_right","border_bottom","border_left"],"width":"0.2","color":"#E5E5E5","radius":"4px","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"     border-top: #E5E5E5 0.2px solid;border-right: #E5E5E5 0.2px solid;border-bottom: #E5E5E5 0.2px solid;border-left: #E5E5E5 0.2px solid; \n    border-radius:4px;     ","customClass":"slds-scrollable_y"},"children":[{"name":"Text","element":"outputField","size":{"isResponsive":true,"default":"2","large":"2","medium":"2","small":"2"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22color:%20#3598db;%22%3E&nbsp;%20&nbsp;%20&nbsp;%7BcaseNumber%7D%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 ","size":{"isResponsive":true,"default":"2","large":"2","medium":"2","small":"2"},"padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","class":"","style":"      \n         "},"elementLabel":"Text-1","key":"element_element_block_0_0_outputField_0_0","parentElementKey":"element_block_0_0","styleObjects":[{"key":0,"conditions":"default","styleObject":{"sizeClass":"slds-large-size_2-of-12 slds-medium-size_2-of-12 slds-small-size_2-of-12 slds-size_2-of-12 ","size":{"isResponsive":true,"default":"2","large":"2","medium":"2","small":"2"},"padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","class":"","style":"      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}]},{"name":"Text","element":"outputField","size":{"isResponsive":true,"default":"12","large":"5","medium":"12","small":"12"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E&nbsp;%7Bsubject%7D&nbsp;%20&nbsp;%20&nbsp;%20%3Cstrong%3E&nbsp;%20&nbsp;&nbsp;%3C/strong%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-large-size_5-of-12  slds-medium-size_12-of-12  slds-small-size_12-of-12  slds-size_12-of-12 ","size":{"isResponsive":true,"default":"12","large":"5","medium":"12","small":"12"},"padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":[],"width":"","color":"#cccccc","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","class":" ","style":"      \n         "},"elementLabel":"Block-0-Text-1","styleObjects":[{"key":0,"conditions":"default","styleObject":{"sizeClass":"slds-large-size_5-of-12  slds-medium-size_12-of-12  slds-small-size_12-of-12  slds-size_12-of-12 ","size":{"isResponsive":true,"default":"12","large":"5","medium":"12","small":"12"},"padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":[],"width":"","color":"#cccccc","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","class":" ","style":"      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}],"key":"element_element_block_0_0_outputField_1_0","parentElementKey":"element_block_0_0"},{"name":"Text","element":"outputField","size":{"isResponsive":true,"default":"12","large":"2","medium":"12","small":"12"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E%3Cspan%20style=%22background-color:%20initial;%20font-size:%200.8125rem;%22%3E&nbsp;%20%7BcreatedDate%7D%3C/span%3E%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-large-size_2-of-12  slds-medium-size_12-of-12  slds-small-size_12-of-12  slds-size_12-of-12 ","size":{"isResponsive":true,"default":"12","large":"2","medium":"12","small":"12"},"padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","class":"","style":"      \n         "},"elementLabel":"Block-0-Text-2","styleObjects":[{"key":0,"conditions":"default","styleObject":{"sizeClass":"slds-large-size_2-of-12  slds-medium-size_12-of-12  slds-small-size_12-of-12  slds-size_12-of-12 ","size":{"isResponsive":true,"default":"12","large":"2","medium":"12","small":"12"},"padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":"","width":"","color":"","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","class":"","style":"      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}],"key":"element_element_block_0_0_outputField_2_0","parentElementKey":"element_block_0_0"},{"name":"Text","element":"outputField","size":{"isResponsive":false,"default":"2"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E&nbsp;%20&nbsp;%20&nbsp;%7Bstatus%7D%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-size_2-of-12 ","size":{"isResponsive":false,"default":"2"}},"elementLabel":"Block-0-Text-4","key":"element_element_block_0_0_outputField_3_0","parentElementKey":"element_block_0_0"},{"name":"Text","element":"outputField","size":{"isResponsive":true,"default":"12","large":"1","medium":"12","small":"12"},"stateIndex":0,"class":"slds-col ","property":{"record":"{record}","mergeField":"%3Cdiv%3E&nbsp;%20&nbsp;%20&nbsp;%20&nbsp;%7Bpriority%7D&nbsp;%20&nbsp;%20&nbsp;%20&nbsp;%3C/div%3E","card":"{card}"},"type":"text","styleObject":{"sizeClass":"slds-large-size_1-of-12  slds-medium-size_12-of-12  slds-small-size_12-of-12  slds-size_12-of-12 ","size":{"isResponsive":true,"default":"12","large":"1","medium":"12","small":"12"},"padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":[],"width":"","color":"#cccccc","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","class":" ","style":"      \n         "},"elementLabel":"Block-0-Text-3","styleObjects":[{"key":0,"conditions":"default","styleObject":{"sizeClass":"slds-large-size_1-of-12  slds-medium-size_12-of-12  slds-small-size_12-of-12  slds-size_12-of-12 ","size":{"isResponsive":true,"default":"12","large":"1","medium":"12","small":"12"},"padding":[],"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":[],"width":"","color":"#cccccc","radius":"","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","class":" ","style":"      \n         "},"label":"Default","name":"Default","conditionString":"","draggable":false}],"key":"element_element_block_0_0_outputField_4_0","parentElementKey":"element_block_0_0"}],"elementLabel":"Block-0","styleObjects":[{"key":0,"conditions":"default","styleObject":{"padding":[{"type":"around","size":"x-small","label":"around:x-small"}],"class":"slds-border_top slds-border_right slds-border_bottom slds-border_left slds-p-around_x-small slds-scrollable_y","sizeClass":"slds-large-size_12-of-12 slds-medium-size_12-of-12 slds-small-size_12-of-12 slds-size_12-of-12 ","size":{"isResponsive":true,"default":"12","large":"12","medium":"12","small":"12"},"margin":[],"background":{"color":"","image":"","size":"","repeat":"","position":""},"container":{"class":""},"border":{"type":["border_top","border_right","border_bottom","border_left"],"width":"0.2","color":"#E5E5E5","radius":"4px","style":""},"elementStyleProperties":{},"text":{"align":"","color":""},"inlineStyle":"","style":"     border-top: #E5E5E5 0.2px solid;border-right: #E5E5E5 0.2px solid;border-bottom: #E5E5E5 0.2px solid;border-left: #E5E5E5 0.2px solid; \n    border-radius:4px;     ","customClass":"slds-scrollable_y"},"label":"Default","name":"Default","conditionString":"","draggable":false}]}]}},"childCards":[],"actions":[],"omniscripts":[],"documents":[]}],"dataSource":{"type":"Custom","value":{"dsDelay":"","body":"[\n      {\n        \"status\": \"In Review\",\n        \"priority\": \"Medium\",\n        \"id\": \"0fhRO00000000sAYAQ\",\n        \"caseNumber\": \"********\",\n        \"caseId\":\"500RO000002OYRGYA4\",\n        \"complaintType\": \"Money transfers, virtual currency, and money services\",\n        \"subject\": \"test the complaint intake , transaction malicious and not correct Statement contains \",\n        \"createdDate\": \"2023-01-19T12:22:56.000Z\",\n        \"accountId\": \"001RO000003fsVBYAY\",\n        \"complaintSubType\": \"Isolation\"\n      },\n      {\n        \"status\": \"Submitted\",\n        \"priority\": \"Low\",\n        \"id\": \"0fhRO00000000sUYAQ\",\n        \"description\": \"not able to ask checkbook\",\n        \"caseNumber\": \"0012365\",\n        \"complaintType\": \"Mobile / electronic banking\",\n        \"subject\": \"banking issue\",\n        \"createdDate\": \"2023-02-01T08:14:06.000Z\",\n        \"accountId\": \"001RO000003fsVBYAY\",\n        \"complaintSubType\": \"Rough Treatment\"\n      }\n    ]","resultVar":""},"orderBy":{"name":"","isReverse":""},"contextVariables":[]},"title":"ComplaintList","enableLwc":true,"isFlex":true,"theme":"slds","selectableMode":"Multi","lwc":{"DeveloperName":"cfComplaintList_1_Salesforce","Id":"0RbRO00000075vu0AA","MasterLabel":"cfComplaintList_1_Salesforce","NamespacePrefix":"myPackage1","ManageableState":"unmanaged"},"osSupport":true,"dynamicCanvasWidth":{"type":"desktop"},"Name":"ComplaintList","uniqueKey":"ComplaintList_Salesforce_1","Id":"flexmetadata0.80578506323955511721917718376","OmniUiCardType":"Child"};
  export default definition