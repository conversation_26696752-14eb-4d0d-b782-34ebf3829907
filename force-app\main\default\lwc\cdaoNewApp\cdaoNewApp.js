import { LightningElement, api, wire } from 'lwc';
import { getPicklistValues } from 'lightning/uiObjectInfoApi';
import { getObjectInfo } from 'lightning/uiObjectInfoApi';
import APPLICATION_OBJECT from '@salesforce/schema/Application__c';
import MEMBERSHIP_STATUS_FIELD from '@salesforce/schema/Application__c.MembershipStatus__c';
import ACCOUNT_TYPE_FIELD from '@salesforce/schema/Application__c.ExistingAccountType__c';
import SHARED_STYLES from '@salesforce/resourceUrl/CDAO_SharedStyles';
import { loadStyle } from 'lightning/platformResourceLoader';

export default class CdaoNewApp extends LightningElement {
    @api applicationData;

    membershipStatus = '';
    selectedAccountType = '';
    @api dateError = '';
    @api dateValue = '';
    
    membershipStatusOptions = [];
    accountTypeOptions = [];

    @wire(getObjectInfo, { objectApiName: APPLICATION_OBJECT })
    objectInfo;

    @wire(getPicklistValues, { recordTypeId: '$objectInfo.data.defaultRecordTypeId', fieldApiName: MEMBERSHIP_STATUS_FIELD })
    wiredMembershipStatus({ error, data }) {
        if (data) {
            this.membershipStatusOptions = data.values;
        } else if (error) {
            //logic here
        }
    }

    @wire(getPicklistValues, { recordTypeId: '$objectInfo.data.defaultRecordTypeId', fieldApiName: ACCOUNT_TYPE_FIELD })
    wiredAccountType({ error, data }) {
        if (data) {
            this.accountTypeOptions = data.values;
        } else if (error) {
            //logic here
        }
    }

    connectedCallback() {
        loadStyle(this, SHARED_STYLES)
            .then(() => {
                //logic here
            })
            .catch(error => {
                //logic here
            });

        if (this.applicationData) {
            this.membershipStatus = this.applicationData.membershipStatus || '';
            this.selectedAccountType = this.applicationData.selectedAccountType || '';
            this.dateValue = this.applicationData.dateValue || '';
        }
    }

    get isExistingMember() {
        return this.membershipStatus === 'Existing Member';
    }

    get isNewMember() {
        return this.membershipStatus === 'New Member';
    }

    get isNextDisabled() {
        if (this.membershipStatus === 'New Member') {
            return false;
        }
        if (this.membershipStatus === 'Existing Member') {
            return !this.selectedAccountType;
        }
        return true;
    }

    handleMembershipChange(event) {
        this.membershipStatus = event.detail.value;
        this.selectedAccountType = '';
    }

    handleAccountTypeChange(event) {
        this.selectedAccountType = event.detail.value;
    }

    handleBack() {
        this.dispatchEvent(new CustomEvent('back'));
    }

    handleNext() {
        const questionData = {
            membershipStatus: this.membershipStatus,
            selectedAccountType: this.selectedAccountType,
            dateValue: this.dateValue
        };
        
        this.dispatchEvent(new CustomEvent('next', {
            detail: questionData
        }));
    }

    
}


