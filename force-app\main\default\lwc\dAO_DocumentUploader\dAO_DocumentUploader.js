import { LightningElement, track, wire } from 'lwc';
import getFirstPicklistValues from '@salesforce/apex/PicklistMetadataController.getFirstPicklistValues';
import uploadFilesToApplication from '@salesforce/apex/PicklistMetadataController.uploadFilesToApplication';
import getFileSizeByDocumentId from '@salesforce/apex/PicklistMetadataController.getFileSizeByDocumentId';
import getRolesForApplication from '@salesforce/apex/PicklistMetadataController.getRolesForApplication';
import deleteFile from '@salesforce/apex/PicklistMetadataController.deleteFile';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { OmniscriptBaseMixin } from "omnistudio/omniscriptBaseMixin";

export default class DAO_DocumentUploader extends OmniscriptBaseMixin(LightningElement) {
    @track isModalOpen = false;
    @track documents = []; 
    @track documentsData = [];
    @track firstPicklistValue = '';
    @track secondPicklistValue = '';
    @track rolesPicklistOptions = '';
    @track description = '';
    @track applicationId;
    @track isId=false;
    @track uploadKey = 0;
    @track selectedRole = '';
    @track uniqueKeyId = 0;
    uploadedFiles = [];
    @track hasError = false;

    connectedCallback() {
        this.isId=false;
        this.applicationId = this.omniJsonData.DRId_DAO_Application__c || this.omniJsonData.applicationId;
    }

    acceptedFormats = ['.pdf', '.jpeg'];

    firstPicklistOptions = [];
    secondPicklistOptions = [
        { label: 'Controlling', value: 'Controlling' },
        { label: 'Authorized Signer', value: 'Authorized Signer' }
    ];

    columns = [
        { label: 'Document Type', fieldName: 'documentType' },
        { label: 'Description', fieldName: 'description' },
        { label: 'Date/Time', fieldName: 'dateTime' },
        { type: 'button', typeAttributes: { label: 'Remove', name: 'remove', variant: 'destructive' } }
    ];

    @wire(getFirstPicklistValues)
    wiredPicklistValues({ error, data }) {
        if (data) {
            const dataString = data[0];
            const valuesArray = dataString.split(',').map(value => value.trim());
            this.firstPicklistOptions = valuesArray.map(value => ({ label: value, value }));
        } else if (error) {
            this.showToast('Error', 'Failed to load picklist values', 'error');
        }
    }

    fetchRolesForApplication() {
        getRolesForApplication({ applicationId: this.applicationId })
            .then(result => {
                this.rolesPicklistOptions = result.map(role => ({
                    label: role.Name,
                    value: role.Id
                }));     
                console.log('OUTPUT : ',this.rolesPicklistOptions);
            })
            .catch(error => {
                this.showToast('Error', 'Failed to load roles', 'error');
                console.error('Error fetching roles:', error);
            });
    }

    handlePicklistChange(event) {
        const name = event.target.name;
        if (name === 'firstPicklist') {
            this.firstPicklistValue = event.detail.value;
             console.log("SelectedRole",this.firstPicklistValue)
            if(this.firstPicklistValue == "Identification")
            {
                console.log("entered if")
                this.isId=true;
                //eval("$A.get('e.force:refreshView').fire();");
            }
            else{
                this.isId=false;
            }
        } else if (name === 'secondPicklist') {
            this.secondPicklistValue = event.detail.value;
        }
    }

     handleRolePicklistChange(event) {
        this.selectedRole = event.detail.value;
        console.log("SlectedRole",this.selectedRole);
       
    }

    handleDescriptionChange(event) {
        this.description = event.target.value;
    }
    handleUploadFinished(event) {
        console.log('Upload finished');
        console.log('Upload finished:', event);
        console.log('Upload finished:', event.detail);
        console.log('Upload finished:', event.detail.files);
        this.uploadedFiles = event.detail.files;
        const documentIds = this.uploadedFiles.map(file => file.documentId);
        getFileSizeByDocumentId({ documentIds })
        .then((fileVersions) => {
            let isFileSizeValid = true;
            fileVersions.forEach(file => {
                const fileSizeMB = file.ContentSize / (1024 * 1024);
                if (fileSizeMB > 50) {
                    isFileSizeValid = false;
                }
            });
            if (!isFileSizeValid) {
                this.showToast('Error', 'File size exceeds 50 MB', 'error');
                this.hasError = true;
                this.resetFormvalue();
            } else {
                this.hasError = false;
            }
        })
        .catch(error => {
            console.error('Error getting file size:', error);
            this.hasError = true;
        });
    }

    resetFormvalue() {
        this.uploadedFiles = [];
        this.uploadKey += 1;
    }

    resetForm() {
        this.firstPicklistValue = '';
        this.description = '';
        this.secondPicklistValue = '';
        this.documents = [];
        this.uploadedFiles = [];
        this.uploadKey += 1;
        this.selectedRole = '';
    }

    handleSubmit() {
        if (this.hasError) {
            this.showToast('Error', 'Please fix the errors before submitting', 'error');
            return;
        }
        if (this.firstPicklistValue && this.description && this.uploadedFiles.length > 0) {
            console.log('Uploaded files:', this.uploadedFiles);
            console.log('First picklist value:', this.firstPicklistValue);
            console.log('Second picklist value:', this.secondPicklistValue);
            console.log(' Description:', this.description);
            console.log(' selectedRole:', this.selectedRole);   
            console.log('application Id:', this.applicationId);
            const newDocuments = this.uploadedFiles.map(file => ({
                id: file.documentId,
                documentType: this.firstPicklistValue,
                description: this.description,
                role: this.secondPicklistValue,
                userrole: this.selectedRole,
                dateTime: new Date().toLocaleString()
            }));

            this.documentsData = [...this.documentsData, ...newDocuments];

            uploadFilesToApplication({ documents: newDocuments, applicationRecordId: this.applicationId })
                .then(() => {
                    this.showToast('Success', 'Files uploaded successfully', 'success');
                    this.resetForm();
                })
                .catch(error => {
                    this.showToast('Error', 'Failed to upload files', 'error');
                    console.error('Error in file upload:', error);
                });

        } else {
            this.showToast('Error', 'Please fill out all fields and upload at least one file', 'error');
        }
    }

    handleRowAction(event) {
        const actionName = event.detail.action.name;
        const row = event.detail.row;
        if (actionName === 'remove') {
            deleteFile({ contentDocumentId: row.id })
                .then(() => {
                    this.documentsData = this.documentsData.filter(doc => doc.id !== row.id);
                    this.showToast('Success', 'File removed successfully', 'success');
                    this.uniqueKeyId += 1;
                })
                .catch(error => {
                    this.showToast('Error', 'Failed to remove file', 'error');
                    console.error('Error removing file:', error);
                });
        }
    }

    handleClick() {
        this.isModalOpen = true;
        this.fetchRolesForApplication();
    }

    closeModal() {
        this.isModalOpen = false;
    }

    showToast(title, message, variant) {
        const event = new ShowToastEvent({
            title,
            message,
            variant,
        });
        this.dispatchEvent(event);
    }

}