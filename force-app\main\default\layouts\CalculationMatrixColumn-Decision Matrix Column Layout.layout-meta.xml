<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ColumnType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsWildcardColumn</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RangeValues</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ApiName</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CalculationMatrixId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DataType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>WildcardColumnValue</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DisplaySequence</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
