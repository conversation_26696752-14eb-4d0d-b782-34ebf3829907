/**
 * @description       :  This class is used to generate a strong password
 * <AUTHOR> Zennify
 * @last modified on  : 04-01-2025
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
@SuppressWarnings('PMD.ExcessiveParameterList, PMD.AvoidGlobalModifier')
public with sharing class DAORemoteUtil {

    public static String generateStrongPassword(Integer length, String exclude) {
        if (length == null || length < 16) {
            length = 16;
        }
        String upper = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        String lower = 'abcdefghijklmnopqrstuvwxyz';
        String digits = '0123456789';
        String symbols = '!@#$%^&*()-_=+[]{}|;:,.<>?';
    
        if (exclude != null) {
            for (Integer i = 0; i < exclude.length(); i++) {
                String ch = exclude.substring(i, i+1);
                upper = upper.replace(ch.toUpperCase(), '');
                lower = lower.replace(ch.toLowerCase(), '');
            }
        }
    
        String allChars = upper + lower + digits + symbols;
    
        if (String.isEmpty(allChars)) {
            throw new IllegalArgumentException('All characters have been excluded.');
        }

        List<String> charList = new List<String>();
        if (!String.isEmpty(upper)) charList.add(getRandomChar(upper));
        if (!String.isEmpty(lower)) charList.add(getRandomChar(lower));
        charList.add(getRandomChar(digits));
        charList.add(getRandomChar(symbols));
    
        while (charList.size() < length) {
            charList.add(getRandomChar(allChars));
        }

        charList = shuffleCharacters(charList);
    
        return String.join(charList, '');
    }
    
    public static String getRandomChar(String pool) {
        Integer index = Math.abs(Math.mod(Crypto.getRandomInteger(), pool.length()));
        return String.valueOf(pool.substring(index, index + 1));
    }

    public static List<String> shuffleCharacters(List<String> input) {
        List<String> shuffled = new List<String>();
        List<String> temp = new List<String>();
        temp.addAll(input);

        while (!temp.isEmpty()) {
            Integer index = Math.abs(Math.mod(Crypto.getRandomInteger(), temp.size()));
            shuffled.add(temp.remove(index));
        }

        return shuffled;
    }

    public static String generateAlias() {
        String alphabet = 'abcdefghijklmnopqrstuvwxyz';
        String alias = '';
        for (Integer i = 0; i < 8; i++) {
            Integer index = Math.abs(Math.mod(Crypto.getRandomInteger(), alphabet.length()));
            alias += alphabet.substring(index, index + 1);
        }
        return alias;
    }
}