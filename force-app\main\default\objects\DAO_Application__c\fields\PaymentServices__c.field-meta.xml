<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>PaymentServices__c</fullName>
    <label>Payment Services</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>MultiselectPicklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Credit Cards</fullName>
                <default>false</default>
                <label>Credit Cards</label>
            </value>
            <value>
                <fullName>Debit Cards</fullName>
                <default>false</default>
                <label>Debit Cards</label>
            </value>
            <value>
                <fullName>Delinquent Debt Collection</fullName>
                <default>false</default>
                <label>Delinquent Debt Collection</label>
            </value>
            <value>
                <fullName>Fee Processing</fullName>
                <default>false</default>
                <label>Fee Processing</label>
            </value>
            <value>
                <fullName>ISO</fullName>
                <default>false</default>
                <label>ISO</label>
            </value>
            <value>
                <fullName>Merchant Payments</fullName>
                <default>false</default>
                <label>Merchant Payments</label>
            </value>
            <value>
                <fullName>Payroll</fullName>
                <default>false</default>
                <label>Payroll</label>
            </value>
            <value>
                <fullName>Utility Bills</fullName>
                <default>false</default>
                <label>Utility Bills</label>
            </value>
            <value>
                <fullName>Other</fullName>
                <default>false</default>
                <label>Other</label>
            </value>
        </valueSetDefinition>
    </valueSet>
    <visibleLines>4</visibleLines>
</CustomField>
