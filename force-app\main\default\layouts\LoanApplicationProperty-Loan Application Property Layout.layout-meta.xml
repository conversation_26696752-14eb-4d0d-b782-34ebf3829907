<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>LoanApplicationId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PropertyAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>UnitsFinancedCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LoanPurposeType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PropertyUseType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RequestedLoanAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PropertyValue</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsMixedUseProperty</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsManufacturedHome</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpMonthlyRentalIncome</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpNetMonthlyRentalInc</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
