/**
 * @description       :  This class is used to assign permission sets to a user
 * <AUTHOR> Zennify
 * @last modified on  : 05-14-2025
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
public with sharing class DAORemoteQueueable implements Queueable {
    private Id userId;
    private List<String> permSetNames;

    public DAORemoteQueueable(Id userId, List<String> permSetNames) {
        this.userId = userId;
        this.permSetNames = permSetNames;
    }

    public void execute(QueueableContext context) {
        try {
            List<PermissionSet> permissionSets = [
                SELECT Id, Name
                FROM PermissionSet
                WHERE Name IN :permSetNames
            ];

            List<PermissionSetAssignment> assignments = new List<PermissionSetAssignment>();
            for (PermissionSet ps : permissionSets) {
                assignments.add(new PermissionSetAssignment(
                    AssigneeId = userId,
                    PermissionSetId = ps.Id
                ));
            }
            
            if (!assignments.isEmpty()) {
                insert assignments;
            }

        } catch (Exception e) {
            System.debug('⚠️ Error in PermissionSetAssignmentJob: ' + e.getMessage());
        }
    }
}