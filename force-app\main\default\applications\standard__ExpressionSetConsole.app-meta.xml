<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Opportunity_Record_Lighting_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
    </actionOverrides>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Business Rules Engine</label>
    <navType>Console</navType>
    <tabs>standard-DecisionTable</tabs>
    <tabs>standard-ExpressionSetTemplate</tabs>
    <tabs>standard-ExpressionSet</tabs>
    <tabs>Email_Preferences__c</tabs>
    <tabs>Email_Preference_Category__c</tabs>
    <uiType>Lightning</uiType>
    <workspaceConfig>
        <mappings>
            <tab>Email_Preference_Category__c</tab>
        </mappings>
        <mappings>
            <tab>Email_Preferences__c</tab>
        </mappings>
        <mappings>
            <tab>standard-DecisionTable</tab>
        </mappings>
        <mappings>
            <tab>standard-ExpressionSet</tab>
        </mappings>
        <mappings>
            <tab>standard-ExpressionSetTemplate</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
