<template>
    <lightning-card  variant="Narrow"  title="OmniScripts" icon-name="action:new_custom8">
        <template if:true={data} for:each={data} for:item="version">
            <lightning-accordion class="example-accordion" active-section-name="RCU_DAO" allow-multiple-sections-open key={version.Id}> 
                <lightning-accordion-section name={version.Name} label={version.Name}>
                <div class="slds-p-horizontal_small">
                    <table class="c_table">
                        <thead>
                            <tr class="c_th_tr">
                                <th class="c_th" scope="col"> 
                                    <div class="c_th_dv" >Name</div>
                                </th>
                                <th class="c_th" scope="col">
                                    <div class="c_th_dv">Description</div>
                                </th>
                                <th class="c_th" scope="col">
                                    <div class="c_th_dv" >Active</div>
                                </th>
                                <th class="c_th" scope="col">
                                    <div class="c_th_dv" >Version</div>
                                </th>
                                <th class="c_th" scope="col">
                                    <div class="c_th_dv" >Modified Date</div>
                                </th>
                                <th class="c_th" scope="col">
                                    <div class="c_th_dv" >Modified By</div>
                                </th>
                                <th class="c_th" scope="col">
                                    <div class="c_th_dv" >Created Date</div>
                                </th>
                                <th class="c_th" scope="col">
                                    <div class="c_th_dv" >Created By</div>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <template if:true={version.versions} for:each={version.versions} for:item="os">
                                <tr class="c_td_tr" key={os.Id}>
                                    <td class="c_td" >
                                        <div class="c_td_dv" ><a href={os.link} target="_blank">{os.Type}/{os.SubType}</a></div> 
                                    </td>
                                    <td class="c_td" >
                                        <div class="c_td_dv" >{os.Description}</div>
                                    </td>
                                    <td class="c_td" >
                                        <div class="c_td_dv">
                                            <div if:true={os.IsActive}>
                                                <svg aria-hidden="true" class="slds-icon slds-icon--x-small nds-icon nds-icon_x-small slds-icon-text-default" xmlns="http://www.w3.org/2000/svg" preserveAspectRatio="xMidYMid meet" sprite="'utility'" icon="'success'" size="'x-small'" extra-classes="'slds-icon-text-default'" viewBox="0 0 52 52" alt="success">
                                                    <path fill="#5e975e" xmlns="http://www.w3.org/2000/svg" d="m26 2c-13.3 0-24 10.7-24 24s10.7 24 24 24 24-10.7 24-24-10.7-24-24-24z m13.4 18l-15.3 15.5c-0.6 0.6-1.6 0.6-2.2 0l-8.4-8.5c-0.6-0.6-0.6-1.6 0-2.2l2.2-2.2c0.6-0.6 1.6-0.6 2.2 0l4.4 4.5c0.4 0.4 1.1 0.4 1.5 0l11.2-11.6c0.6-0.6 1.6-0.6 2.2 0l2.2 2.2c0.7 0.6 0.7 1.6 0 2.3z"></path>
                                                </svg>
                                            </div>
                                            <div if:false={os.IsActive}>
                                                -
                                            </div>
                                        </div>
                                    </td>
                                    <td class="c_td" >
                                        <div class="c_td_dv" >v. {os.VersionNumber}</div>
                                    </td>
                                    <td class="c_td" >
                                        <div class="c_td_dv" >{os.LastModifiedDate}</div>
                                    </td>
                                    <td class="c_td" >
                                        <div class="c_td_dv" >{os.LastModifiedBy.Name}</div>
                                    </td>
                                    <td class="c_td" >
                                        <div class="c_td_dv" >{os.CreatedDate}</div>
                                    </td>
                                    <td class="c_td" >
                                        <div class="c_td_dv" >{os.CreatedBy.Name}</div>
                                    </td>
                                    
                                </tr>
                                </template>
                        </tbody>
                    </table>
                </div>
                </lightning-accordion-section>
            </lightning-accordion>
        </template>
    </lightning-card>
</template>