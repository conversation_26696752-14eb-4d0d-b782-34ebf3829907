<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>VersionNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>MatrixType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CalculationMatrixId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LoadProcessStatus</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsEnabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>StartDateTime</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EndDateTime</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Rank</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ApiName</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Configuration</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>GroupKey</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>GroupKeyValue</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SubGroupKey</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SubGroupKeyValue</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
