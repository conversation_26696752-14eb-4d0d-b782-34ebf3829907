<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>58.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>File_Uploaded</name>
        <label>File Uploaded?</label>
        <locationX>182</locationX>
        <locationY>242</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Fileuploaded</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ContentDocumentIDs</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Image</targetReference>
            </connector>
            <label>File uploaded</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>[Image] URL on Employee Account {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[Image] URL on Employee Account</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Content_Delivery_Record</name>
        <label>Create Content Delivery Record</label>
        <locationX>50</locationX>
        <locationY>458</locationY>
        <assignRecordIdToReference>ContentDeliveryId</assignRecordIdToReference>
        <connector>
            <targetReference>Get_Content_Delivery</targetReference>
        </connector>
        <inputAssignments>
            <field>ContentVersionId</field>
            <value>
                <elementReference>Get_Image.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>Get_Image.Title</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PreferencesAllowViewInBrowser</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PreferencesNotifyOnVisit</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <object>ContentDistribution</object>
    </recordCreates>
    <recordLookups>
        <name>Get_Content_Delivery</name>
        <label>Get Content Delivery</label>
        <locationX>50</locationX>
        <locationY>566</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Update_Account</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ContentDeliveryId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ContentDistribution</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Image</name>
        <label>Get Image</label>
        <locationX>50</locationX>
        <locationY>350</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Create_Content_Delivery_Record</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>In</operator>
            <value>
                <elementReference>ContentVersionIDs</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ContentVersion</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_Account</name>
        <label>Update Account</label>
        <locationX>50</locationX>
        <locationY>674</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>HS_Image_URL__pc</field>
            <value>
                <elementReference>Get_Content_Delivery.ContentDownloadUrl</elementReference>
            </value>
        </inputAssignments>
        <object>Account</object>
    </recordUpdates>
    <screens>
        <name>Image_Upload</name>
        <label>Employee Image Upload</label>
        <locationX>182</locationX>
        <locationY>134</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>File_Uploaded</targetReference>
        </connector>
        <fields>
            <name>Upload</name>
            <extensionName>forceContent:fileUpload</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Upload Image File</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>multiple</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>recordId</name>
                <value>
                    <elementReference>recordId</elementReference>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <outputParameters>
                <assignToReference>ContentDocumentIDs</assignToReference>
                <name>contentDocIds</name>
            </outputParameters>
            <outputParameters>
                <assignToReference>ContentVersionIDs</assignToReference>
                <name>contentVersionIds</name>
            </outputParameters>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Image_Upload</targetReference>
        </connector>
    </start>
    <status>Obsolete</status>
    <variables>
        <name>ContentDeliveryId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ContentDocumentID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ContentDocumentIDs</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ContentVersionIDs</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
