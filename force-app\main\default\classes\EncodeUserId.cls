/**
 * @description       :  This class is used to encode the user id
 * <AUTHOR> Zennify
 * @last modified on  : 03-31-2025
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
global with sharing class EncodeUserId implements  System.Callable
{    
 
    global Object call(String action, Map<String, Object> args){
        Map<String, Object> input = (Map<String, Object>) args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');
        Boolean result = true;
        try{
            if(action.equals('encodeId')){
                String roleId = (String)input.get('ContextId');
                System.debug('#### roleId: ' + roleId);
                String decodedId = EncodingUtil.base64Decode(roleId).toString();
                System.debug( '#### decodedId: ' + decodedId);
                output.put('Id',decodedId);
            }
        }catch(Exception e){
            result = false;
        }
        return result;
    }
}