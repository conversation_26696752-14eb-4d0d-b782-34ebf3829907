<template>
            <div class="slds-grid slds-wrap slds-is-relative ">
            <div style="position: fixed;z-index: 999999;top: 0;right: 0;">
  <omnistudio-toast class="flexcard-toast-element" theme="slds" title="" message="" styletype=""> </omnistudio-toast>
</div>
            <div if:true={showLoader} class="slds-is-absolute vloc-loader_override" style="height: 100%; width: 100%; min-height:50px; background: transparent; z-index: 99;">
  <div>
   <omnistudio-spinner
      variant="brand"
      alternative-text="Loading content..."
      size="medium"
      theme="slds"
      ></omnistudio-spinner>
  </div>
</div>
            <template if:false={hasPermission}>
              You don't have required permissions to view this card.
            </template>
            <template if:true={hasPermission}>
              <template if:true={hasRecords}>
                        <template for:each={_records} for:item="record" for:index="rindex">
                            <omnistudio-flex-card-state if:true={record}  key={record._flex.uniqueKey} data-recordid={record.Id} record={record} data-statue="true"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12"  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-bottom_x-small" style="">
          <div data-style-id="state0element0" class="slds-col   slds-p-around_x-small slds-size_8-of-12  " data-rindex={rindex} style="" >
    <omnistudio-block data-style-id="state0element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          
        </div></omnistudio-block>
    </div><div data-style-id="state0element1" class="slds-col   slds-size_1-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state0element1_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="utility:attach"  size="medium"  extraclass="slds-icon_container slds-icon__svg--default "  variant="default"  imgsrc=""  theme="slds"  ></omnistudio-flex-icon>
      </div><div data-style-id="state0element2" class="slds-col   slds-size_1-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state0element2_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="utility:cart"  size="medium"  extraclass="slds-icon_container slds-icon__svg--default "  variant="default"  imgsrc=""  theme="slds"  ></omnistudio-flex-icon>
      </div><div data-style-id="state0element3" class="slds-col   slds-size_1-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state0element3_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="utility:chat"  size="medium"  extraclass="slds-icon_container slds-icon__svg--default  slds-text-link_reset"  variant="default"  imgsrc=""  action='\{"label":"Action","iconName":"standard-default","eventType":"onclick","actionList":[{"key":"1725956225826-3eicf34p1","label":"Action","draggable":false,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-1725956238740","type":"Custom","displayName":"Action","vlocityIcon":"standard-default","targetType":"Web Page","openUrlIn":"New Tab/Window","Web Page":{"targetName":"https://www.redwoodcu.org/about/contact/"}},"actionIndex":0}],"showSpinner":"true"}'  theme="slds" onclick={executeAction} data-element-label="icon3" data-action-key="state0element3" ></omnistudio-flex-icon>
      </div><div data-style-id="state0element4" class="slds-col   slds-size_1-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state0element4_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="utility:save"  size="medium"  extraclass="slds-icon_container slds-icon__svg--default  slds-text-link_reset"  variant="default"  imgsrc=""  action='\{"label":"Action","iconName":"standard-default","eventType":"onclick","actionList":[{"key":"1725963414471-4vaqqfw1z","label":"Action","draggable":false,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-1725968824611","type":"DataAction","displayName":"Action","vlocityIcon":"standard-default","openUrlIn":"New Tab/Window","message":"{\"type\":\"DataRaptor\",\"value\":{\"dsDelay\":\"\",\"bundle\":\"GetSavedSesionUrl\",\"bundleType\":\"Load\",\"inputMap\":{\"type\":\"\\\"rcu\\\"\",\"subtype\":\"\\\"application\\\"\",\"name\":\"\\\"Saved-OmniScript\\\"\",\"language\":\"\\\"English\\\"\"}},\"orderBy\":{\"name\":\"\",\"isReverse\":\"\"},\"contextVariables\":[]}"},"actionIndex":0}],"showSpinner":"true"}'  theme="slds" onclick={executeAction} data-element-label="icon4" data-action-key="state0element4" ></omnistudio-flex-icon>
      </div>
        </div>
      </omnistudio-flex-card-state>
                        </template>
                      </template>
                      <template if:false={hasRecords}>
                            <omnistudio-flex-card-state  record={record} data-statue="false"  data-index="0" data-rindex={rindex} class="cf-vlocity-state-0 cf-vlocity-state slds-size_12-of-12"  tracking-obj={_childCardTrackingObj} >
      <div if:true={cardLoaded} class="slds-grid slds-wrap slds-card slds-p-around_x-small slds-m-bottom_x-small" style="">
          <div data-style-id="state0element0" class="slds-col   slds-p-around_x-small slds-size_8-of-12  " data-rindex={rindex} style="" >
    <omnistudio-block data-style-id="state0element0_child"  card={card}  record={record}  label="Block"  collapsible="false"  collapsed-by-default="false"  theme="slds"  ><div class="slds-grid slds-wrap">
          
        </div></omnistudio-block>
    </div><div data-style-id="state0element1" class="slds-col   slds-size_1-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state0element1_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="utility:attach"  size="medium"  extraclass="slds-icon_container slds-icon__svg--default "  variant="default"  imgsrc=""  theme="slds"  ></omnistudio-flex-icon>
      </div><div data-style-id="state0element2" class="slds-col   slds-size_1-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state0element2_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="utility:cart"  size="medium"  extraclass="slds-icon_container slds-icon__svg--default "  variant="default"  imgsrc=""  theme="slds"  ></omnistudio-flex-icon>
      </div><div data-style-id="state0element3" class="slds-col   slds-size_1-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state0element3_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="utility:chat"  size="medium"  extraclass="slds-icon_container slds-icon__svg--default  slds-text-link_reset"  variant="default"  imgsrc=""  action='\{"label":"Action","iconName":"standard-default","eventType":"onclick","actionList":[{"key":"1725956225826-3eicf34p1","label":"Action","draggable":false,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-1725956238740","type":"Custom","displayName":"Action","vlocityIcon":"standard-default","targetType":"Web Page","openUrlIn":"New Tab/Window","Web Page":{"targetName":"https://www.redwoodcu.org/about/contact/"}},"actionIndex":0}],"showSpinner":"true"}'  theme="slds" onclick={executeAction} data-element-label="icon3" data-action-key="state0element3" ></omnistudio-flex-icon>
      </div><div data-style-id="state0element4" class="slds-col   slds-size_1-of-12  " data-rindex={rindex} style="" ><omnistudio-flex-icon data-style-id="state0element4_child"   card={card}  record={record}  icon-type="Salesforce SVG"  icon-name="utility:save"  size="medium"  extraclass="slds-icon_container slds-icon__svg--default  slds-text-link_reset"  variant="default"  imgsrc=""  action='\{"label":"Action","iconName":"standard-default","eventType":"onclick","actionList":[{"key":"1725963414471-4vaqqfw1z","label":"Action","draggable":false,"isOpen":true,"card":"{card}","stateAction":{"id":"flex-action-1725968824611","type":"DataAction","displayName":"Action","vlocityIcon":"standard-default","openUrlIn":"New Tab/Window","message":"{\"type\":\"DataRaptor\",\"value\":{\"dsDelay\":\"\",\"bundle\":\"GetSavedSesionUrl\",\"bundleType\":\"Load\",\"inputMap\":{\"type\":\"\\\"rcu\\\"\",\"subtype\":\"\\\"application\\\"\",\"name\":\"\\\"Saved-OmniScript\\\"\",\"language\":\"\\\"English\\\"\"}},\"orderBy\":{\"name\":\"\",\"isReverse\":\"\"},\"contextVariables\":[]}"},"actionIndex":0}],"showSpinner":"true"}'  theme="slds" onclick={executeAction} data-element-label="icon4" data-action-key="state0element4" ></omnistudio-flex-icon>
      </div>
        </div>
      </omnistudio-flex-card-state>
                      </template>
            </template>
            <template if:true={hasError}>
            {error}
            </template>
            
      <omnistudio-action action-wrapperclass="slds-hide" re-render-flyout class="action-trigger slds-col" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
      
      </omnistudio-action>
  
            <omnistudio-action class="execute-action" re-render-flyout action-wrapperclass="slds-hide" onupdateos={updateOmniscript} onselectcards={updateSelectedCards} onsetvalue={updateAction} onfireactionevent={actionEvtHandler}>
            
            </omnistudio-action>
            </div>
          </template>