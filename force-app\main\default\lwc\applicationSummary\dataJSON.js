const data = {
    "selectedOPAValue": "electronic",
    "selectedOPALabel": "Checks and Electronic Transactions Only",
    "DRId_DAO_Application__c": "a1IU800000VsS77MAF",
    "gambling": "",
    "marijuana": "",
    "professional": "",
    "charitable": "",
    "armored": "",
    "thirdParty": "",
    "replenish": "",
    "nonBank": "",
    "moneyService": "",
    "ContextId": "new",
    "omniscriptId": "0jNU8000000EhJdMAK",
    "language": "English",
    "type": "Essential Checking",
    "runMode": "preview",
    "sId": "0jNU8000000EhJdMAK",
    "theme": "lightning",
    "subType": "application",
    "userProfile": "System Administrator",
    "timeStamp": "2025-05-02T15:50:24.892Z",
    "userTimeZoneName": "America/Los_Angeles",
    "userTimeZone": "-420",
    "userCurrencyCode": "USD",
    "userName": "<EMAIL>",
    "userId": "005U800000DecKZIAZ",
    "omniProcessId": "0jNU8000000EhJdMAK",
    "localTimeZoneName": "America/Los_Angeles",
    "Are You a Member": {
        "Block25": {
            "Existing Member": null
        },
        "RCU Member": "New Member"
    },
    "selectApplication": null,
    "OPASelection": {},
    "Business Roles List": {},
    "Business_goal": null,
    "BusinessVisaRadio": false,
    "sInstanceId": "0kTU800000022TxMAI",
    "code": "BBUSCK",
    "savingsCode": "BUSSHARE",
    "eVisaDebit": "Yes",
    "eStatements": "No",
    "onlineBankingValue": "Yes",
    "businessCreditCard": "No",
    "eBoxCheck": "",
    "cVisaDebit": "",
    "cBoxCheck": "",
    "typeValueRadio": "Essential Checking",
    "ProductId": {
        "ProductId1": "a1KU800000QBAptMAH",
        "SavingId": "a1KU800000QBApuMAH"
    },
    "flag": false,
    "selectedValue": "electronic",
    "selectedLabel": "Checks and Electronic Transactions Only",
    "PrimaryContactInfo": {
        "Fictitious_Business_Name_DBA": false,
        "OtherHeadquartersCounty": null,
        "BusinessStructureBlock": {
            "Traded_on_the_stock_exchange": null,
            "Owned_by_an_entitiy_on_the_stock_exchange": null,
            "Publicly_traded_company": "No"
        },
        "Block13": {
            "Country_business_headquartered": null,
            "Business_headquartered_in_US": "Yes",
            "Registered_to_do_business_in_California": "Yes"
        },
        "BusinessStructureBlock1": {
            "Depend_on_charitable_donations_voluntary_service": "No",
            "Objectives_programs_activities_services": null,
            "Chartered_organization_country": null,
            "Registered_as_a_non-profit": null,
            "Sources_of_funding_for_the_organization": null,
            "Description": null,
            "Methods_to_obtain_funding": null,
            "fundingDescription": null,
            "Value_of_annual_funding_or_gross_receipts_for_the_most_recent_year": null,
            "Volunteers_from_non-US_countries": null,
            "Donor_or_volunteer_countries": null,
            "Will_services_benefit_individuals_in_foreign_countries": null,
            "In_what_countries_do_you_provide_charitable_services": null,
            "In_what_countries_are_your_foreign_beneficiaries_located": null
        },
        "PrimaryMobilecheckbox": true,
        "SecondaryMobilecheckbox": false,
        "formulaExtractLastFour": "4564",
        "BusinessName": "AJ Test Business 001",
        "BusinessHeadquartersCounty": "San Francisco",
        "BusinessStructure": "Limited Liability Company",
        "Preferred_branch": "San Francisco",
        "PContactFirstName": "TestAJ",
        "PContactLastName": "TetserAJ",
        "Email": "<EMAIL>",
        "DateofBirthofApplicant": "1997-05-20",
        "SSNofApplicant": "*********",
        "PreferredContactMethod": "Primary Phone",
        "Primary": "**********"
    },
    "ApplicationId": "a1IU800000VsS77MAF",
    "BusinessCounty": "San Francisco",
    "physicalAddressflag1": false,
    "mailingAddressflag1": false,
    "previousPhysicalAddressflag1": false,
    "Business_Info": {
        "Checkbox1": false,
        "Fictitious_Business_Name_DBA1": false,
        "Business_Tax_ID_Type0": null,
        "Business_Tax_ID": null,
        "Business_Tax_ID_SSNumber": "*********",
        "sameasphysical": false,
        "Other_Description": null,
        "ShowOccupancyDurationError": true,
        "formulaPhysicalStreet": "545 1/2 13th Ave S",
        "formulaPhysicalCity": "Saint Petersburg",
        "formulaPhysicalState": "FL",
        "formulaPhysicalPostal": "33701",
        "formulaMailingStreet": "545 1/2 13th Ave S",
        "formulaMailingCity": "Saint Petersburg",
        "formulaMailingState": "FL",
        "formulaMailingPostal": "33701",
        "formulaPreviousPhysicalStreet": null,
        "formulaPreviousPhysicalCity": null,
        "formulaPreviousPhysicalState": null,
        "formulaPreviousPhysicalPostal": null,
        "DBA_Name": "AJ Test Business 001",
        "State_Registered": "CA",
        "Business_Tax_ID_Type": "SSN",
        "Establish_Date": "2019-05-08",
        "Business_Annual_Revenue": 100000,
        "Number_of_Employees": 2,
        "Business_Description": "Tech",
        "NAICSCode-Block": {
            "NAICSCode": "541380 Testing Laboratories and Services Geotechnical testing laboratories or services",
            "Id": "a1NU8000000XruIMAS",
            "Search": "541380 Testing Laboratories and Services Geotechnical testing laboratories or services"
        },
        "physicalAddress": {
            "Address": [
                "545 1/2 13th Ave S",
                "Saint Petersburg",
                "FL",
                "33701"
            ]
        },
        "Occupancy_Status": "Own - Free And Clear",
        "Occupancy_Duration_Years": 10,
        "Occupancy Duration - Months": 0,
        "Business_Email": "<EMAIL>",
        "Business_Phone": "3216165465"
    },
    "rolestreetAddress": "545 1/2 13th Ave S",
    "rolecityAddress": "Saint Petersburg",
    "rolestateAddress": "FL",
    "rolezipcode": "33701",
    "rolephysicalFullAddres": "545 1/2 13th Ave S Saint Petersburg FL 33701 US",
    "mailingAddressflag": false,
    "physicalAddressflag": false,
    "RoleInfo": {
        "Date_of_Birth": "1997-05-20",
        "FrequentTraveler": null,
        "Outside_United_States": null,
        "Which_Countries": null,
        "Same_as_Business_Address": true,
        "Use_different_address_for_mailing": false,
        "PrimaryMobilecheckbox2": true,
        "SecondaryMobilecheckbox2": false,
        "Former_Profession_MOS": null,
        "Former_Profession_Job_Title": null,
        "Profession_Job_Title": "Software",
        "Profession/MOS": null,
        "Branch_of_Service": null,
        "Pay Grade": null,
        "PayGrade_1": null,
        "Time_in_Service_Years": null,
        "Time_in_Services-Months": null,
        "Time_in_Services-Months1": null,
        "ETS_Expiration": null,
        "Retired_Years": null,
        "Retired_Months": null,
        "Retired_Months1": null,
        "Employer": "Tester Test",
        "Business_Type": null,
        "Employment_Duration_Years": 5,
        "Employment_Duration_Months": null,
        "Employment_Duration_Months1": 0,
        "unemploymentYears": null,
        "unemploymentMonth": null,
        "unemploymentMonth1": null,
        "Gross_Monthly_Income": 150000,
        "Has_Control_1": "Yes",
        "ID_State": "CA",
        "ID_Country": null,
        "formulaPhysicalStreet1": "545 1/2 13th Ave S",
        "formulaPhysicalCity2": "Saint Petersburg",
        "formulaPhysicalState3": "FL",
        "formulaPhysicalPostal4": "33701",
        "formulaMailingStreet1": "545 1/2 13th Ave S",
        "formulaMailingCity2": "Saint Petersburg",
        "formulaMailingState3": "FL",
        "formulaMailingPostal4": "33701",
        "FormulaEmploymentDuration": 0,
        "FormulaUnemploymentDuration": null,
        "FormulaRetiredDuration": null,
        "FormulaTimeInServicesDuration": null,
        "RoleFirstName": "TestAJ",
        "RoleLastName": "TetserAJ",
        "Role_SSN_TIN": "*********",
        "Work_Phone": null,
        "Role_Email": "<EMAIL>",
        "Primary_Home_Phone": "**********",
        "Preferred_Contact_Method": "Primary Phone",
        "Secondary_Mobile_Phone": null,
        "IndividualRole": "Controlling Individual",
        "Has_Control": null,
        "RoleMiddleName": null,
        "Citizenship_Status": "US CITIZEN",
        "Employment_Status": "Employed",
        "Business_Owned": 80,
        "ID_Type": "DRIVERS LICENSE",
        "ID_Number": "*********",
        "ID Date Issued": "2023-05-16",
        "ID_Expiration_Date": "2026-05-20"
    },
    "ApiResponse": {
        "error": "We couldn't access the credential(s). You might not have the required permissions, or the external credential \"AzureOAuthToken\" might not exist.",
        "success": false
    },
    "SocureResponse": {
        "error": "We couldn't access the credential(s). You might not have the required permissions, or the external credential \"AzureOAuthToken\" might not exist.",
        "success": false
    },
    "DRId_DAO_Roles__c": "a1LU8000001mlKDMAY",
    "ShowError": false,
    "Due Diligence Activities": {
        "General_Operation_Funds": true,
        "Payroll": false,
        "Savings": true,
        "Credit_Card_Processing": false,
        "IOLTA/IOLA": false,
        "Lottery": false,
        "MSB_Activity": false,
        "Private_Banking": false,
        "Other": false,
        "Other_Notes": null,
        "Private_Label_Credit_Card": false,
        "Private_Label_Credit_Card_Point_of_Sale": null,
        "Postage_Remittance": false,
        "Postage_Purchase": null,
        "Postage_Third_Parties": null,
        "Postage_Cash_Refund": null,
        "Equipment_or_Lease": false,
        "Equipment_Purchase": null,
        "Equipment_Third_Parties": null,
        "Equipment_Cash_Refund": null,
        "Pooled_Investment_Vehicle": false,
        "Excluded_Financial_Institution": null,
        "Personal_Banking_Activity": null,
        "Formula1": true
    },
    "Due_Diligence_Transactions": {
        "Block10": {
            "Cash_Deposits": false,
            "Cash_Deposits_Avg_Amount": null
        },
        "Block11": {
            "ATM_Cash_Deposits": true,
            "ATM_Cash_Deposits_Avg_Amount": "5000"
        },
        "Block18": {
            "Cash_Withdrawals": false,
            "Cash_Withdrawals_Avg_Amount": null
        },
        "Block19": {
            "ATM_Cash_Withdrawals": false,
            "ATM_Cash_Withdrawals_Avg_Amount": null
        },
        "Block20": {
            "Check_Deposits": false,
            "Check_Deposits_Avg_Amount": null
        },
        "Block26": {
            "Remote_Deposit_Capture": false
        },
        "Block22": {
            "Check_Withdrawals": false,
            "Check_Withdrawals_Avg_Amount": null
        },
        "Block23": {
            "Cash_Deposits_Courier": false,
            "Cash_Withdrawals_Courier": null
        },
        "Block24": {
            "Incoming_Wire_Transfers": false,
            "Incoming_Wire_Transfers_Avg_Amount": null,
            "Incoming_Wire_Transfers_Non_US": null,
            "Incoming_Wire_Transfers_Countries": null
        },
        "Block16": {
            "Outgoing_Wire_Transfers": false,
            "Outgoing_Wire_Transfers_Avg_Amount": null,
            "Outgoing_Wire_Transfers_Non-US": null,
            "Outgoing_Wire_Transfers_Countries": null
        },
        "Block27": {
            "Incoming_Electronic_Transfers": false,
            "Incoming_Electronic_Transfers_Avg_Amount": null,
            "Incoming_Electronic_Transfers_Non-US": null,
            "Incoming_Electronic_Transfers_Countries": null
        },
        "Block28": {
            "Outgoing_Electronic_Transfers": false,
            "Outgoing_Electronic_Transfers_Avg_Amount": null,
            "Outgoing_Electronic_Transfers_Non-US": null,
            "Outgoing_Electronic_Transfers_Countries": null
        }
    },
    "DiligenceQuestions": {
        "Marijuana_Business__c": {
            "field": "Marijuana_Business__c",
            "value": "No",
            "isMain": true,
            "noChecked": true,
            "yesChecked": false,
            "showSubquestions": false,
            "subquestions": {
                "MarijuanaLicensed__c": {
                    "parent": "Marijuana_Business__c",
                    "field": "MarijuanaLicensed__c",
                    "value": "",
                    "type": "radio",
                    "isMain": false,
                    "noChecked": false,
                    "yesChecked": false
                },
                "MarijuanaPercentage__c": {
                    "parent": "Marijuana_Business__c",
                    "field": "MarijuanaPercentage__c",
                    "value": "",
                    "type": "picklist",
                    "isMain": false
                },
                "MarijuanaActivity__c": {
                    "parent": "Marijuana_Business__c",
                    "field": "MarijuanaActivity__c",
                    "value": "",
                    "type": "multipicklist",
                    "isMain": false
                }
            }
        },
        "Intermediary__c": {
            "field": "Intermediary__c",
            "value": "No",
            "isMain": true,
            "noChecked": true,
            "yesChecked": false,
            "showSubquestions": false,
            "subquestions": {
                "ProfessionalType__c": {
                    "parent": "Intermediary__c",
                    "field": "ProfessionalType__c",
                    "value": "",
                    "type": "picklist",
                    "isMain": false
                },
                "ServicesTypes__c": {
                    "parent": "Intermediary__c",
                    "field": "ServicesTypes__c",
                    "value": "",
                    "type": "multipicklist",
                    "isMain": false
                },
                "ProfessionalOthersUsing__c": {
                    "parent": "Intermediary__c",
                    "field": "ProfessionalOthersUsing__c",
                    "value": "",
                    "type": "radio",
                    "isMain": false,
                    "noChecked": false,
                    "yesChecked": false
                }
            }
        },
        "Third_Party_Payment_Processor__c": {
            "field": "Third_Party_Payment_Processor__c",
            "value": "No",
            "isMain": true,
            "noChecked": true,
            "yesChecked": false,
            "showSubquestions": false,
            "subquestions": {
                "TransactionsSendPayments__c": {
                    "parent": "Third_Party_Payment_Processor__c",
                    "field": "TransactionsSendPayments__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false
                },
                "TransactionsReceivePayments__c": {
                    "parent": "Third_Party_Payment_Processor__c",
                    "field": "TransactionsReceivePayments__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false
                },
                "PaymentServices__c": {
                    "parent": "Third_Party_Payment_Processor__c",
                    "field": "PaymentServices__c",
                    "value": "",
                    "type": "multipicklist",
                    "isMain": false
                },
                "PaymentsThroughAccounts__c": {
                    "parent": "Third_Party_Payment_Processor__c",
                    "field": "PaymentsThroughAccounts__c",
                    "value": "",
                    "type": "radio",
                    "isMain": false,
                    "noChecked": false,
                    "yesChecked": false,
                    "showSubSubquestions": false,
                    "subsubquestions": {
                        "PaymentsHowProcessed__c": {
                            "parent": "PaymentsThroughAccounts__c",
                            "grandParent": "Third_Party_Payment_Processor__c",
                            "field": "PaymentsHowProcessed__c",
                            "value": "",
                            "type": "multipicklist",
                            "isMain": false
                        }
                    }
                },
                "CreateChecksRemotely__c": {
                    "parent": "Third_Party_Payment_Processor__c",
                    "field": "CreateChecksRemotely__c",
                    "value": "",
                    "type": "radio",
                    "isMain": false,
                    "noChecked": false,
                    "yesChecked": false
                },
                "BusinessTypeRestrictions__c": {
                    "parent": "Third_Party_Payment_Processor__c",
                    "field": "BusinessTypeRestrictions__c",
                    "value": "",
                    "type": "radio",
                    "isMain": false,
                    "noChecked": false,
                    "yesChecked": false,
                    "showSubSubquestions": false,
                    "subsubquestions": {
                        "BusinessTypeRestrictionsText__c": {
                            "parent": "BusinessTypeRestrictions__c",
                            "grandParent": "Third_Party_Payment_Processor__c",
                            "field": "BusinessTypeRestrictionsText__c",
                            "value": "",
                            "type": "text",
                            "isMain": false
                        }
                    }
                }
            }
        },
        "ATM_Business__c": {
            "field": "ATM_Business__c",
            "value": "No",
            "isMain": true,
            "noChecked": true,
            "yesChecked": false,
            "showSubquestions": false,
            "subquestions": {
                "NumberOfAtm__c": {
                    "parent": "ATM_Business__c",
                    "field": "NumberOfAtm__c",
                    "value": "",
                    "type": "number",
                    "isMain": false
                },
                "ReplenishAtmCash__c": {
                    "parent": "ATM_Business__c",
                    "field": "ReplenishAtmCash__c",
                    "value": "",
                    "type": "radio",
                    "isMain": false,
                    "noChecked": false,
                    "yesChecked": false,
                    "showSubSubquestions": false,
                    "subsubquestions": {
                        "SourceOfAtmCash__c": {
                            "parent": "ReplenishAtmCash__c",
                            "grandParent": "ATM_Business__c",
                            "field": "SourceOfAtmCash__c",
                            "value": "",
                            "type": "multipicklist",
                            "isMain": false
                        }
                    }
                },
                "AtmMaxHolding__c": {
                    "parent": "ATM_Business__c",
                    "field": "AtmMaxHolding__c",
                    "value": "",
                    "type": "number",
                    "isMain": false
                },
                "AtmDenomination__c": {
                    "parent": "ATM_Business__c",
                    "field": "AtmDenomination__c",
                    "value": "",
                    "type": "multipicklist",
                    "isMain": false
                },
                "PrivateAtmType__c": {
                    "parent": "ATM_Business__c",
                    "field": "PrivateAtmType__c",
                    "value": "",
                    "type": "multipicklist",
                    "isMain": false
                }
            }
        },
        "Non_Bank_Financial_Institution__c": {
            "field": "Non_Bank_Financial_Institution__c",
            "value": "No",
            "isMain": true,
            "noChecked": true,
            "yesChecked": false,
            "showSubquestions": false,
            "subquestions": {
                "InvolveCasinos__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolveCasinos__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false
                },
                "InvolveSecurities__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolveSecurities__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false,
                    "showSubSubquestions": false,
                    "subsubquestions": {
                        "SecuritiesFinancialInstitution__c": {
                            "parent": "InvolveSecurities__c",
                            "grandParent": "Non_Bank_Financial_Institution__c",
                            "field": "SecuritiesFinancialInstitution__c",
                            "value": "",
                            "type": "multipicklist",
                            "isMain": false
                        },
                        "SecuritiesHowBusinessRegistered__c": {
                            "parent": "InvolveSecurities__c",
                            "grandParent": "Non_Bank_Financial_Institution__c",
                            "field": "SecuritiesHowBusinessRegistered__c",
                            "value": "",
                            "type": "multipicklist",
                            "isMain": false
                        },
                        "SecuritiesInvolveSecurities__c": {
                            "parent": "InvolveSecurities__c",
                            "grandParent": "Non_Bank_Financial_Institution__c",
                            "field": "SecuritiesInvolveSecurities__c",
                            "value": "",
                            "type": "radio",
                            "isMain": false,
                            "noChecked": false,
                            "yesChecked": false,
                            "showSubSubSubquestions": false,
                            "subsubsubquestions": {
                                "SecuritiesProductTypes__c": {
                                    "grandParent": "InvolveSecurities__c",
                                    "greatgrandParent": "Non_Bank_Financial_Institution__c",
                                    "parent": "SecuritiesInvolveSecurities__c",
                                    "field": "SecuritiesProductTypes__c",
                                    "value": "",
                                    "type": "multipicklist",
                                    "isMain": false
                                },
                                "SecuritiesInvestFundsInternationally__c": {
                                    "grandParent": "InvolveSecurities__c",
                                    "greatgrandParent": "Non_Bank_Financial_Institution__c",
                                    "parent": "SecuritiesInvolveSecurities__c",
                                    "field": "SecuritiesInvestFundsInternationally__c",
                                    "value": "",
                                    "type": "radio",
                                    "isMain": false,
                                    "noChecked": false,
                                    "yesChecked": false,
                                    "showSubSubSubSubquestions": false,
                                    "subsubsubsubquestions": {
                                        "SecuritiesCountriesText__c": {
                                            "grandParent": "SecuritiesInvolveSecurities__c",
                                            "greatgrandParent": "InvolveSecurities__c",
                                            "greatGreategrandParent": "Non_Bank_Financial_Institution__c",
                                            "parent": "SecuritiesInvestFundsInternationally__c",
                                            "field": "SecuritiesCountriesText__c",
                                            "value": "",
                                            "type": "text",
                                            "isMain": false
                                        }
                                    }
                                },
                                "SecuritiesServiceTypes__c": {
                                    "grandParent": "InvolveSecurities__c",
                                    "greatgrandParent": "Non_Bank_Financial_Institution__c",
                                    "parent": "SecuritiesInvolveSecurities__c",
                                    "field": "SecuritiesServiceTypes__c",
                                    "value": "",
                                    "type": "multipicklist",
                                    "isMain": false
                                }
                            }
                        }
                    }
                },
                "InvolveInsurance__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolveInsurance__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false,
                    "showSubSubquestions": false,
                    "subsubquestions": {
                        "InvolveInsuranceStateRegIns__c": {
                            "parent": "InvolveInsurance__c",
                            "grandParent": "Non_Bank_Financial_Institution__c",
                            "field": "InvolveInsuranceStateRegIns__c",
                            "value": "",
                            "type": "radio",
                            "isMain": false,
                            "noChecked": false,
                            "yesChecked": false
                        }
                    }
                },
                "InvolveLoanFinance__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolveLoanFinance__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false
                },
                "InvolveCreditCards__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolveCreditCards__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false
                },
                "InvolvePreciousMetals__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolvePreciousMetals__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false,
                    "showSubSubquestions": false,
                    "subsubquestions": {
                        "InvolvePreciousMetalsBuy50k__c": {
                            "parent": "InvolvePreciousMetals__c",
                            "grandParent": "Non_Bank_Financial_Institution__c",
                            "field": "InvolvePreciousMetalsBuy50k__c",
                            "value": "",
                            "type": "radio",
                            "isMain": false,
                            "noChecked": false,
                            "yesChecked": false
                        },
                        "InvolvePreciousMetalsSell50k__c": {
                            "parent": "InvolvePreciousMetals__c",
                            "grandParent": "Non_Bank_Financial_Institution__c",
                            "field": "InvolvePreciousMetalsSell50k__c",
                            "value": "",
                            "type": "radio",
                            "isMain": false,
                            "noChecked": false,
                            "yesChecked": false
                        }
                    }
                },
                "InvolvePawnBrokerage__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolvePawnBrokerage__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false
                },
                "InvolveTravelAgency__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolveTravelAgency__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false
                },
                "InvolveTelegraphCompany__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolveTelegraphCompany__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false
                },
                "InvolveVehicleSales__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolveVehicleSales__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false,
                    "showSubSubquestions": false,
                    "subsubquestions": {
                        "InvolveVehicleTypes__c": {
                            "parent": "InvolveVehicleSales__c",
                            "grandParent": "Non_Bank_Financial_Institution__c",
                            "field": "InvolveVehicleTypes__c",
                            "value": "",
                            "type": "multipicklist",
                            "isMain": false
                        }
                    }
                },
                "InvolveRealEstateClosing__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolveRealEstateClosing__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false
                },
                "InvolvePostalService__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolvePostalService__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false
                },
                "InvolveGovAgency__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolveGovAgency__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false
                },
                "InvolveNone__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "InvolveNone__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false
                },
                "DocBsaAmlProgram__c": {
                    "parent": "Non_Bank_Financial_Institution__c",
                    "field": "DocBsaAmlProgram__c",
                    "value": "",
                    "type": "radio",
                    "isMain": false,
                    "noChecked": false,
                    "yesChecked": false
                }
            }
        },
        "Money_Service_Business__c": {
            "field": "Money_Service_Business__c",
            "value": "No",
            "isMain": true,
            "noChecked": true,
            "yesChecked": false,
            "showSubquestions": false,
            "subquestions": {
                "InvolveCurrencyExchange__c": {
                    "parent": "Money_Service_Business__c",
                    "field": "InvolveCurrencyExchange__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false,
                    "showSubSubquestions": false,
                    "subsubquestions": {
                        "InvolveCurencyExchangeAgentPrincipal__c": {
                            "grandParent": "Money_Service_Business__c",
                            "parent": "InvolveCurrencyExchange__c",
                            "field": "InvolveCurencyExchangeAgentPrincipal__c",
                            "value": false,
                            "type": "checkbox",
                            "isMain": false
                        }
                    }
                },
                "InvolveCashChecks__c": {
                    "parent": "Money_Service_Business__c",
                    "field": "InvolveCashChecks__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false,
                    "showSubSubquestions": false,
                    "subsubquestions": {
                        "InvolveCheckTypes__c": {
                            "grandParent": "Money_Service_Business__c",
                            "parent": "InvolveCashChecks__c",
                            "field": "InvolveCheckTypes__c",
                            "value": "",
                            "type": "multipicklist",
                            "isMain": false
                        }
                    }
                },
                "InvolveMoneyOrders__c": {
                    "parent": "Money_Service_Business__c",
                    "field": "InvolveMoneyOrders__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false,
                    "showSubSubquestions": false,
                    "subsubquestions": {
                        "InvolveMoneyOrdersAgentPrincipal__c": {
                            "grandParent": "Money_Service_Business__c",
                            "parent": "InvolveMoneyOrders__c",
                            "field": "InvolveMoneyOrdersAgentPrincipal__c",
                            "value": false,
                            "type": "checkbox",
                            "isMain": false
                        }
                    }
                },
                "InvolveTransmitMoney__c": {
                    "parent": "Money_Service_Business__c",
                    "field": "InvolveTransmitMoney__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false,
                    "showSubSubquestions": false,
                    "subsubquestions": {
                        "TransmitMoneyAgentPrincipal__c": {
                            "grandParent": "Money_Service_Business__c",
                            "parent": "InvolveTransmitMoney__c",
                            "field": "TransmitMoneyAgentPrincipal__c",
                            "value": "",
                            "type": "radio",
                            "isMain": false,
                            "noChecked": false,
                            "yesChecked": false
                        },
                        "TransmitMoneyNonUsLocations__c": {
                            "grandParent": "Money_Service_Business__c",
                            "parent": "InvolveTransmitMoney__c",
                            "field": "TransmitMoneyNonUsLocations__c",
                            "value": "",
                            "type": "radio",
                            "isMain": false,
                            "noChecked": false,
                            "yesChecked": false,
                            "showSubSubSubquestions": false,
                            "subsubsubquestions": {
                                "TransmitForeignCountriesText__c": {
                                    "grandParent": "InvolveTransmitMoney__c",
                                    "greatgrandParent": "Money_Service_Business__c",
                                    "parent": "TransmitMoneyNonUsLocations__c",
                                    "field": "TransmitForeignCountriesText__c",
                                    "value": "",
                                    "type": "text",
                                    "isMain": false
                                }
                            }
                        },
                        "TransmitMoneyTypes__c": {
                            "grandParent": "Money_Service_Business__c",
                            "parent": "InvolveTransmitMoney__c",
                            "field": "TransmitMoneyTypes__c",
                            "value": "",
                            "type": "multipicklist",
                            "isMain": false
                        },
                        "TransmitMoneyCvc__c": {
                            "grandParent": "Money_Service_Business__c",
                            "parent": "InvolveTransmitMoney__c",
                            "field": "TransmitMoneyCvc__c",
                            "value": "",
                            "type": "radio",
                            "isMain": false,
                            "noChecked": false,
                            "yesChecked": false,
                            "showSubSubSubquestions": false,
                            "subsubsubquestions": {
                                "TransmitMoneyActivities__c": {
                                    "grandParent": "InvolveTransmitMoney__c",
                                    "greatgrandParent": "Money_Service_Business__c",
                                    "parent": "TransmitMoneyCvc__c",
                                    "field": "TransmitMoneyActivities__c",
                                    "value": "",
                                    "type": "multipicklist",
                                    "isMain": false
                                }
                            }
                        }
                    }
                },
                "InvolveVirtualCurrency__c": {
                    "parent": "Money_Service_Business__c",
                    "field": "InvolveVirtualCurrency__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false
                },
                "InvolveGiftCards__c": {
                    "parent": "Money_Service_Business__c",
                    "field": "InvolveGiftCards__c",
                    "value": false,
                    "type": "checkbox",
                    "isMain": false,
                    "showSubSubquestions": false,
                    "subsubquestions": {
                        "GiftCardAgentPrincipal__c": {
                            "grandParent": "Money_Service_Business__c",
                            "parent": "InvolveGiftCards__c",
                            "field": "GiftCardAgentPrincipal__c",
                            "value": "",
                            "type": "radio",
                            "isMain": false,
                            "noChecked": false,
                            "yesChecked": false
                        },
                        "GiftCardExceedDailyMax__c": {
                            "grandParent": "Money_Service_Business__c",
                            "parent": "InvolveGiftCards__c",
                            "field": "GiftCardExceedDailyMax__c",
                            "value": "",
                            "type": "radio",
                            "isMain": false,
                            "noChecked": false,
                            "yesChecked": false,
                            "showSubSubSubquestions": false,
                            "subsubsubquestions": {
                                "GiftCardActivationProcess__c": {
                                    "grandParent": "InvolveGiftCards__c",
                                    "greatgrandParent": "Money_Service_Business__c",
                                    "parent": "GiftCardExceedDailyMax__c",
                                    "field": "GiftCardActivationProcess__c",
                                    "value": "",
                                    "type": "radio",
                                    "isMain": false,
                                    "noChecked": false,
                                    "yesChecked": false
                                }
                            }
                        },
                        "GiftCardNetworkBranded__c": {
                            "grandParent": "Money_Service_Business__c",
                            "parent": "InvolveGiftCards__c",
                            "field": "GiftCardNetworkBranded__c",
                            "value": "",
                            "type": "radio",
                            "isMain": false,
                            "noChecked": false,
                            "yesChecked": false,
                            "showSubSubSubquestions": false,
                            "subsubsubquestions": {
                                "GiftCardAccessDailyMax__c": {
                                    "grandParent": "InvolveGiftCards__c",
                                    "greatgrandParent": "Money_Service_Business__c",
                                    "parent": "GiftCardNetworkBranded__c",
                                    "field": "GiftCardAccessDailyMax__c",
                                    "value": "",
                                    "type": "radio",
                                    "isMain": false,
                                    "noChecked": false,
                                    "yesChecked": false,
                                    "showSubSubSubSubquestions": false,
                                    "showSubSubSubSubquestionsYesNo": false,
                                    "subsubsubsubquestions": {
                                        "GiftCardReloaded__c": {
                                            "grandParent": "GiftCardNetworkBranded__c",
                                            "greatgrandParent": "InvolveGiftCards__c",
                                            "greatGreategrandParent": "Money_Service_Business__c",
                                            "parent": "GiftCardAccessDailyMax__c",
                                            "field": "GiftCardReloaded__c",
                                            "value": "",
                                            "type": "radio",
                                            "isMain": false,
                                            "noChecked": false,
                                            "yesChecked": false
                                        },
                                        "GiftCardTransferFunds__c": {
                                            "grandParent": "GiftCardNetworkBranded__c",
                                            "greatgrandParent": "InvolveGiftCards__c",
                                            "greatGreategrandParent": "Money_Service_Business__c",
                                            "parent": "GiftCardAccessDailyMax__c",
                                            "field": "GiftCardTransferFunds__c",
                                            "value": "",
                                            "type": "radio",
                                            "isMain": false,
                                            "noChecked": false,
                                            "yesChecked": false
                                        },
                                        "GiftCardTransferFundsInternationally__c": {
                                            "grandParent": "GiftCardNetworkBranded__c",
                                            "greatgrandParent": "InvolveGiftCards__c",
                                            "greatGreategrandParent": "Money_Service_Business__c",
                                            "parent": "GiftCardAccessDailyMax__c",
                                            "field": "GiftCardTransferFundsInternationally__c",
                                            "value": "",
                                            "type": "radio",
                                            "isMain": false,
                                            "noChecked": false,
                                            "yesChecked": false
                                        },
                                        "GiftCardRequireCustomerId__c": {
                                            "grandParent": "GiftCardNetworkBranded__c",
                                            "greatgrandParent": "InvolveGiftCards__c",
                                            "greatGreategrandParent": "Money_Service_Business__c",
                                            "parent": "GiftCardAccessDailyMax__c",
                                            "field": "GiftCardRequireCustomerId__c",
                                            "value": "",
                                            "type": "radio",
                                            "isMain": false,
                                            "noChecked": false,
                                            "yesChecked": false
                                        }
                                    }
                                }
                            }
                        },
                        "GiftCardPreventSales__c": {
                            "grandParent": "Money_Service_Business__c",
                            "parent": "InvolveGiftCards__c",
                            "field": "GiftCardPreventSales__c",
                            "value": "",
                            "type": "radio",
                            "isMain": false,
                            "noChecked": false,
                            "yesChecked": false
                        }
                    }
                },
                "GiftCardBsaAmlProg__c": {
                    "parent": "Money_Service_Business__c",
                    "field": "GiftCardBsaAmlProg__c",
                    "value": "",
                    "type": "radio",
                    "isMain": false,
                    "noChecked": false,
                    "yesChecked": false
                },
                "RegisteredFinCen__c": {
                    "parent": "Money_Service_Business__c",
                    "field": "RegisteredFinCen__c",
                    "value": "",
                    "type": "radio",
                    "isMain": false,
                    "noChecked": false,
                    "yesChecked": false
                }
            }
        }
    },
    "ApplicantNameValue": "TestAJ TetserAJ",
    "Initial_Deposit_Setup_and_Details": {
        "Business_Savings": null,
        "Business_Essential_Checking": null,
        "Business_Choice_Checking": null,
        "Cr_Business_Savings": 10,
        "Cr_Business_Essential_Checking": null,
        "Currency3": null,
        "Total_Deposit": 10,
        "Messaging1": true,
        "Credit_Card": {
            "isFutureDate": false,
            "Cardholder_Name": "TestAJ TetserAJ",
            "Billing_Address": "545 1/2 13th Ave S",
            "Billing_Zip": "33701",
            "Billing_City": "Saint Petersburg",
            "Billing_State": "FL",
            "Credit_Card_Number": "****************",
            "Expiration_Month": "03",
            "Select1": "2027"
        },
        "Block14": null,
        "How_do_you_want_to_fund_your_account": "Credit Card (max $510.00)"
    },
    "Occupancy": "10 yrs 0 mos",
    "employment": "5 yrs 0 mos",
    "businessTax": "",
    "fullNameValue": "TestAJTetserAJ",
    "estatementValue": false,
    "eVisaDebitCard": true,
    "cVisaDebitCard": false,
    "onlineBankingBoxValue": true,
    "fullnameValue": "TestAJ  TetserAJ",
    "unemployment": "0 yrs 0 mos",
    "CountRoleData": 2,
    "CountAutorizedSigner": 3,
    "RoleSize": 5,
    "ApplicationSummary": {
        "estatementsCheckbox": false,
        "onlineBanking": false,
        "MobileDepositCapture": false,
        "OnlineBankingCheckBox": true,
        "Checkbox4": false,
        "Checkbox3": true,
        "visaDebitEssential": true,
        "visaDebitChoice": false,
        "EstablishDate": "2019-05-08",
        "DateofBirth": "1997-05-20",
        "UnemploymentDuration": null,
        "IDExpirationDate": "2026-05-20",
        "IDDateIssued": "2023-05-16",
        "Role2": {
            "ProfessionJobTitle2": null,
            "employers2": null,
            "EmploymentDuration2": "0 yrs 0 mos",
            "UnEmploymentDuration2": null,
            "FirstName2": "Test2",
            "LastName2": "Testing",
            "RoleType2": "Controlling Individual",
            "SSNTIN2": null,
            "EmploymentStatus2": null,
            "FullName2": "Test2  Testing",
            "PreferredContactMethod2": null,
            "SecondaryMobilePhone2": null,
            "IDType2": null,
            "ExpirationDate2": null,
            "IDIssuedDate2": null,
            "PrimaryHomePhone2": null,
            "DateOfBirth2": null,
            "IdState2": null,
            "CitizenshipStatus2": null,
            "Email02": "<EMAIL>",
            "CurrentPhysicalAddress2": null,
            "workphones2": null,
            "IDNumber2": null
        },
        "Role3": {
            "ProfessionJobTitle3": null,
            "employers3": null,
            "EmploymentDuration3": "0 yrs 0 mos",
            "UnEmploymentDuration3": null,
            "FirstName3": "Tetster3",
            "LastName3": "Testing",
            "RoleType3": "Authorized Signer",
            "FullName3": "Tetster3  Testing",
            "EmploymentStatus3": null,
            "PreferredContactMethod3": null,
            "IDType3": null,
            "SecondaryMobilePhone3": null,
            "SSNTIN3": null,
            "ExpirationDate3": null,
            "IDIssuedDate3": null,
            "PrimaryHomePhone3": null,
            "DateOfBirth3": null,
            "CitizenshipStatus3": null,
            "IdState3": null,
            "Email03": "<EMAIL>",
            "CurrentPhysicalAddress3": null,
            "workphones3": null,
            "IDNumber3": null
        },
        "Role4": {
            "ProfessionJobTitle4": null,
            "employers4": null,
            "EmploymentDuration4": "0 yrs 0 mos",
            "UnEmploymentDuration4": null,
            "FirstName4": "Another",
            "LastName4": "One",
            "RoleType4": "Authorized Signer",
            "FullName4": "Another  One",
            "EmploymentStatus4": null,
            "IDNumber4": null,
            "PreferredContactMethod4": null,
            "CurrentPhysicalAddress4": null,
            "IDType4": null,
            "Email04": "<EMAIL>",
            "SecondaryMobilePhone4": null,
            "SSNTIN4": null,
            "ExpirationDate4": null,
            "IDIssuedDate4": null,
            "DateOfBirth4": null,
            "PrimaryHomePhone4": null,
            "IdState4": null,
            "CitizenshipStatus4": null,
            "workphones4": null
        },
        "Role5": {
            "ProfessionJobTitle5": null,
            "employers5": null,
            "EmploymentDuration5": "0 yrs 0 mos",
            "UnEmploymentDuration5": null,
            "RoleType5": "Authorized Signer",
            "IDNumber5": null,
            "ExpirationDate5": null,
            "FullName5": "More  Test5",
            "EmploymentStatus5": null,
            "PreferredContactMethod5": null,
            "CurrentPhysicalAddress5": null,
            "Email05": "<EMAIL>",
            "SSNTIN5": null,
            "SecondaryMobilePhone5": null,
            "DateOfBirth5": null,
            "IDIssuedDate5": null,
            "CitizenshipStatus5": null,
            "IdState5": null,
            "IDType5": null,
            "PrimaryHomePhone5": null,
            "workphones5": null
        },
        "Checkbox5": false,
        "ControllingRoleData": [
            {
                "IDNumber": "*********",
                "PhysicalCity": "Saint Petersburg",
                "MailingStreet": "545 1/2 13th Ave S",
                "LastName": "TetserAJ",
                "HomePhoneNumber": "**********",
                "Email5": "<EMAIL>",
                "ExpirationDate": "2026-05-20",
                "SSNTIN": "*********",
                "PhysicalStreetAddress": "545 1/2 13th Ave S",
                "PhysicalState": "FL",
                "Occupation": "Software",
                "IDType": "DRIVERS LICENSE",
                "PhsyicalZipCode": "33701",
                "RoleType": "Controlling Individual",
                "FirstName": "TestAJ",
                "employers": "Tester Test",
                "DateOfBirth": "1997-05-20"
            },
            {
                "LastName": "Testing",
                "RoleType": "Controlling Individual",
                "FirstName": "Test2",
                "Email5": "<EMAIL>"
            }
        ],
        "RoleData": [
            {
                "MailingAddresssameasPhysical": false,
                "MailingStreet": "545 1/2 13th Ave S",
                "LastName": "TetserAJ",
                "ExpirationDate": "2026-05-20",
                "PhysicalStreetAddress": "545 1/2 13th Ave S",
                "PhysicalState": "FL",
                "Occupation": "Software",
                "PhsyicalZipCode": "33701",
                "EmploymentStatus": "Employed",
                "GrossMonthlyIncome": 150000,
                "FirstName": "TestAJ",
                "employers": "Tester Test",
                "DateOfBirth": "1997-05-20",
                "IDNumber": "*********",
                "PhysicalCity": "Saint Petersburg",
                "CitizenshipStatus": "US CITIZEN",
                "HomePhoneNumber": "**********",
                "IDType": "DRIVERS LICENSE",
                "RoleType": "Controlling Individual",
                "IDIssuedDate": "2023-05-16",
                "idStates": "CA",
                "DAORolesName": "TestAJ  TetserAJ",
                "RoleId": "a1LU8000001mlKDMAY",
                "Email5": "<EMAIL>",
                "SSNTIN": "*********",
                "PrimaryHomePhone": "**********"
            },
            {
                "MailingAddresssameasPhysical": false,
                "MailingStreet": null,
                "LastName": "Testing",
                "ExpirationDate": null,
                "PhysicalStreetAddress": null,
                "PhysicalState": null,
                "Occupation": null,
                "PhsyicalZipCode": null,
                "EmploymentStatus": null,
                "GrossMonthlyIncome": null,
                "FirstName": "Test2",
                "employers": null,
                "DateOfBirth": null,
                "IDNumber": null,
                "PhysicalCity": null,
                "CitizenshipStatus": null,
                "HomePhoneNumber": null,
                "IDType": null,
                "RoleType": "Controlling Individual",
                "IDIssuedDate": null,
                "idStates": null,
                "DAORolesName": "Test2  Testing",
                "RoleId": "a1LU8000001mlQfMAI",
                "Email5": "<EMAIL>",
                "SSNTIN": null,
                "PrimaryHomePhone": null
            },
            {
                "MailingAddresssameasPhysical": false,
                "MailingStreet": null,
                "LastName": "Testing",
                "ExpirationDate": null,
                "PhysicalStreetAddress": null,
                "PhysicalState": null,
                "Occupation": null,
                "PhsyicalZipCode": null,
                "EmploymentStatus": null,
                "GrossMonthlyIncome": null,
                "FirstName": "Tetster3",
                "employers": null,
                "DateOfBirth": null,
                "IDNumber": null,
                "PhysicalCity": null,
                "CitizenshipStatus": null,
                "HomePhoneNumber": null,
                "IDType": null,
                "RoleType": "Authorized Signer",
                "IDIssuedDate": null,
                "idStates": null,
                "DAORolesName": "Tetster3  Testing",
                "RoleId": "a1LU8000001mlSHMAY",
                "Email5": "<EMAIL>",
                "SSNTIN": null,
                "PrimaryHomePhone": null
            },
            {
                "MailingAddresssameasPhysical": false,
                "MailingStreet": null,
                "LastName": "One",
                "ExpirationDate": null,
                "PhysicalStreetAddress": null,
                "PhysicalState": null,
                "Occupation": null,
                "PhsyicalZipCode": null,
                "EmploymentStatus": null,
                "GrossMonthlyIncome": null,
                "FirstName": "Another",
                "employers": null,
                "DateOfBirth": null,
                "IDNumber": null,
                "PhysicalCity": null,
                "CitizenshipStatus": null,
                "HomePhoneNumber": null,
                "IDType": null,
                "RoleType": "Authorized Signer",
                "IDIssuedDate": null,
                "idStates": null,
                "DAORolesName": "Another  One",
                "RoleId": "a1LU8000001mlTtMAI",
                "Email5": "<EMAIL>",
                "SSNTIN": null,
                "PrimaryHomePhone": null
            },
            {
                "MailingAddresssameasPhysical": false,
                "MailingStreet": null,
                "LastName": "Test5",
                "ExpirationDate": null,
                "PhysicalStreetAddress": null,
                "PhysicalState": null,
                "Occupation": null,
                "PhsyicalZipCode": null,
                "EmploymentStatus": null,
                "GrossMonthlyIncome": null,
                "FirstName": "More",
                "employers": null,
                "DateOfBirth": null,
                "IDNumber": null,
                "PhysicalCity": null,
                "CitizenshipStatus": null,
                "HomePhoneNumber": null,
                "IDType": null,
                "RoleType": "Authorized Signer",
                "IDIssuedDate": null,
                "idStates": null,
                "DAORolesName": "More  Test5",
                "RoleId": "a1LU8000001mlVVMAY",
                "Email5": "<EMAIL>",
                "SSNTIN": null,
                "PrimaryHomePhone": null
            }
        ],
        "CompanyName": "AJ Test Business 001",
        "businessTaxId": "-",
        "Industry": "541380 Testing Laboratories and Services Geotechnical testing laboratories or services",
        "StateRegistered": "CA",
        "BusinessDescription": "Tech",
        "Email3": "<EMAIL>",
        "NumberofEmployees": 2,
        "MobilePhone": "3216165465",
        "CurrentPhysicalAddress": "545 1/2 13th Ave S",
        "OccupancyDuration": "10 yrs 0 mos",
        "Occupancy_Status_1": "Own - Free And Clear",
        "FullName": "TestAJ  TetserAJ",
        "SSN_1": "*********",
        "Citizenship_Status_1": "US CITIZEN",
        "CurrentPhysicalAddress_1": "545 1/2 13th Ave S",
        "PreferredContact_Method": "Primary Phone",
        "Email4": "<EMAIL>",
        "PrimaryHome_Phone": "**********",
        "Employment_Status_1": "Employed",
        "ProfessionJobTitle": "Software",
        "Employer1": "Tester Test",
        "EmploymentDuration": "5 yrs 0 mos",
        "GrossMonthl_Income": 150000,
        "IdType": "DRIVERS LICENSE",
        "IdState_1": "CA",
        "Id_number": "*********",
        "total_deposit_1": 10,
        "FundingType": "Credit Card (max $510.00)",
        "AppId": "a1IU800000VsS77MAF",
        "BranchName": null,
        "AuthorizedRoleData": [
            {
                "LastName": "Testing",
                "FirstName": "Tetster3",
                "Email5": "<EMAIL>"
            },
            {
                "LastName": "One",
                "FirstName": "Another",
                "Email5": "<EMAIL>"
            },
            {
                "LastName": "Test5",
                "FirstName": "More",
                "Email5": "<EMAIL>"
            }
        ]
    }
}

export default data;