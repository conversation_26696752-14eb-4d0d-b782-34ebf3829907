/**
 * @description       : Handles the remote actions for the DAO app including redirecting the user to the login page
 * <AUTHOR> Zennify
 * @last modified on  : 06-13-2025
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
@SuppressWarnings('PMD.ExcessiveParameterList, PMD.AvoidGlobalModifier')
global virtual without sharing class DAORemote implements  Callable {

    
    global Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');
        
        if(action == 'redirectUser'){
             output.put('loginUrl', getLoginUrl(input, output));
            return true;
        } else if(action == 'isExistingUser'){
             output.put('UserFound', isExistingUser(input, output));
            return true;
        }   else if(action == 'decodeUrl'){
             output.put('ContextId', decodeUrl(input, output));
            return true;
        }   else if(action == 'decodeUserId'){
             output.put('Id', decodeUserId(input, output));
            return true;
        }   else if(action == 'submitApplicationDocument'){
              return submitApplicationDocument(input, output);
        }  

        return null;
    }

    public static String decodeUrl(Map<String,Object> input, Map<String,Object> output) {
        return ((String)EncodingUtil.urlDecode((String)input.get('ContextId'), 'UTF-8')).replace(' ', '+');
    }

    public static String decodeUserId(Map<String,Object> input, Map<String,Object> output) {
        return EncodingUtil.base64Decode((String)input.get('ContextId')).toString();
    }

    public static Boolean isExistingUser(Map<String,Object> input, Map<String,Object> output) {
        return ([SELECT count() FROM DAO_Application__c WHERE (Email_Address__c = :(String)input.get('email')) ] > 0);
    }

    public static Boolean submitApplicationDocument(Map<String,Object> input, Map<String,Object> output) {
        try {
            String loanNumber = (String)input.get('loanNumber');
            String applicationId = (String)input.get('applicationId');
            String firstName = (String)input.get('firstName');
            String lastName = (String)input.get('lastName');
            Set<Id> contentDocIds = new Set<Id>();
            List<Object> calloutResponses = new List<Object>();
            Integer totalDocuments = [SELECT count() FROM DAO_Application_Documents__c WHERE DAO_Application__c = :applicationId AND Submitted__c = false];



            if (input.containsKey('totalDocuments')) {
                totalDocuments = Integer.valueOf((String)input.get('totalDocuments'));
            }

            if (totalDocuments == 0) {
                return false;
            }

            List<ContentDocumentLink> docLinks = [
                SELECT ContentDocumentId
                FROM ContentDocumentLink
                WHERE LinkedEntityId = :applicationId
                LIMIT :totalDocuments
            ];

            for (ContentDocumentLink link : docLinks) {
                contentDocIds.add(link.ContentDocumentId);
            }

            if (contentDocIds.isEmpty()) {
                return false;
            } 

            List<ContentVersion> versions = [
                SELECT ContentDocumentId, PathOnClient, Title, VersionData
                FROM ContentVersion
                WHERE ContentDocumentId IN :contentDocIds
                LIMIT :totalDocuments
            ];

            for (ContentVersion document : versions) {
                // Map<String, Object> payload = new Map<String, Object>{
                //     'INPUT' => new Map<String, Object>{
                //         'REQUEST' => new Map<String, Object>{
                //             'APP_NUMBER' => new Map<String, Object>{
                //                 '_app_type' => 'XPRESS_APPS',
                //                 '__text' => loanNumber
                //             }
                //         },
                //         'DOCUMENT' => new Map<String, Object>{
                //             'CONTENT' => EncodingUtil.base64Encode(document.VersionData),
                //             'METADATA' => new Map<String, Object>{
                //                 'ITEM' => new List<Object>{
                //                     new Map<String, Object>{'_key' => 'fname', '_value' => firstName},
                //                     new Map<String, Object>{'_key' => 'lname', '_value' => lastName}
                //                 }
                //             },
                //             '_title' => document.Title,
                //             '_filename' => document.PathOnClient,
                //             '_docgroup' => 'K',
                //             '_doccode' => 'N',
                //             '__text' => loanNumber
                //         },
                //         '_xmlns' => 'http://www.meridianlink.com/CLF',
                //         '_xmlns:xsi' => 'http://www.w3.org/2001/XMLSchema-instance'
                //     }
                // };
                Map<String, Object> payload = new Map<String, Object>{ 'INPUT' => new Map<String, Object>{ 'REQUEST' => new Map<String, Object>{ 'APP_NUMBER' => new Map<String, Object>{ '_app_type' => 'XPRESS_APPS', '__text' => loanNumber } }, 'DOCUMENT' => new Map<String, Object>{ 'CONTENT' => EncodingUtil.base64Encode(document.VersionData), 'METADATA' => new Map<String, Object>{ 'ITEM' => new List<Object>{ new Map<String, Object>{'_key' => 'fname', '_value' => firstName}, new Map<String, Object>{'_key' => 'lname', '_value' => lastName} } }, '_title' => document.Title, '_filename' => document.PathOnClient, '_docgroup' => 'K', '_doccode' => 'N', '__text' => loanNumber }, '_xmlns' => 'http://www.meridianlink.com/CLF', '_xmlns:xsi' => 'http://www.w3.org/2001/XMLSchema-instance' } };
                calloutToMeridianLink(payload, output, calloutResponses);
            }

            sendResponse(output, calloutResponses);
                
            return true;

        } catch (Exception e) {
            System.debug( LoggingLevel.ERROR, 'Error in submitDocument: ' + e.getMessage());
            output.put('Error', e.getMessage());
            return false;
        }
    }

    public static void calloutToMeridianLink(Map<String, Object> payload, Map<String, Object> output, List<Object> calloutResponses  ) {
        HttpRequest req = new HttpRequest();
         req.setEndpoint('callout:MeridianLinkAPI');
         req.setMethod('POST');
         req.setHeader('Content-Type', 'application/json');
         req.setHeader('Accept', '*/*');
         req.setBody(JSON.serialize(payload));
         System.debug('⚠️ payload: ' + JSON.serialize(payload));
         Http http = new Http();
         HttpResponse res = http.send(req);
        processCalloutResponse(res, output, calloutResponses);
    }

    public static void processCalloutResponse(HttpResponse res, Map<String, Object> output, List<Object> calloutResponses) {
        calloutResponses.add(new Map<String, Object>{
            'statusCode' => res.getStatusCode(),
            'body' => res.getBody()
        });
        System.debug('⚠️ calloutResponses: ' + JSON.serialize(calloutResponses));
    }

    public static void sendResponse(Map<String, Object> output, List<Object> calloutResponses) {
        Boolean error = false;
        for (Object response : calloutResponses) {
            Map<String, Object> responseMap = (Map<String, Object>)response;
            if (responseMap.get('statusCode') != 200) {
                error = true;
            }
        }
        if (error) {
            output.put('MeridianLinkSubmitDocumentStatus', 'Error');
        } else {
            output.put('MeridianLinkSubmitDocumentStatus', 'Success');
        }
    }

    @AuraEnabled(cacheable=false)
    public static String getLoginUrl(Map<String,Object> input, Map<String,Object> output) {
        try {
            String contextId = (String)input.get('contextId');
            String email = (String)input.get('email');
            Boolean userFound = (Boolean)input.get('userFound');
            Long CURRENT_TIME = Datetime.now().getTime();
            String redirectUrl = '';

            if (contextId == null || contextId == '') {
                redirectUrl = '/s/application?ContextId=' + 'new' ;
                
            } else if (contextId.contains('@') && contextId.contains('.')) {
                redirectUrl = '/s/application?ContextId=' + contextId ;
                
            } else {
                redirectUrl = '/s/applicant?ContextId=' + contextId;
            }

            User communityUser = new User(
                FirstName = 'ExtUsrFstNme_' + CURRENT_TIME,
                LastName = 'ExtUsrLstNme_' + CURRENT_TIME,
                Email = CURRENT_TIME + '@mail.rcu.dao',
                Alias = DAORemoteUtil.generateAlias(),
                Username = CURRENT_TIME + '@mail.rcu.dao',
                CommunityNickname = 'ExtUsrNick_' + CURRENT_TIME,
                TimeZoneSidKey = 'America/New_York',
                LocaleSidKey = 'en_US',
                EmailEncodingKey = 'UTF-8',
                LanguageLocaleKey = 'en_US',
                ProfileId = [SELECT Id FROM Profile WHERE Name = 'DAO Customer Community' LIMIT 1].Id,
                IsActive = true
            );

            String password = DAORemoteUtil.generateStrongPassword(16, communityUser.Username);
            Id communityUserId = Site.createPortalUser(communityUser,
                                                [SELECT Id FROM Account WHERE Name = 'Community Users' LIMIT 1].Id,
                                                password,
                                                false
                                            );
                                            
            System.enqueueJob(new DAORemoteQueueable(
                communityUserId,
                new List<String>{
                    'OmniStudio_Guest_User',
                    'Omnistudio_Guest_User_Permission'
                }
            ));

            ApexPages.PageReference pageRef = Site.login( communityUser.Username, password, redirectUrl);
                
            return pageRef.getUrl();

        } catch (Exception e) {
            System.debug('⚠️ Error in getLoginUrl: ' + e.getMessage());
            return null;
        }
    }   
}