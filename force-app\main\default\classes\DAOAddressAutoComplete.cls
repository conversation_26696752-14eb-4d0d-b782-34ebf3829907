/**
 * @description       : Class will be used to call the SmartyStreets API to get the address suggestions based on the search string.
 * <AUTHOR> Zennify
 * @last modified on  : 01-16-2025
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
public with sharing class DAOAddressAutoComplete {
    @AuraEnabled
    public static string getAddress(String search){
        try {
            search = search.replace(' ','%20');
            String authId = System.Label.SmartyAuthId;
            String token = System.Label.SmartyToken; 
            String endpoint = System.Label.SmartyEndpoint; 
            String url = endpoint+authId+'&auth-token='+token+'&search='+search;
            
            HttpRequest req = new HttpRequest();
            req.setEndpoint(url);
            req.setMethod('GET');
            Http http = new Http();
            HTTPResponse res = http.send(req);
            return res.getBody();

        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
}