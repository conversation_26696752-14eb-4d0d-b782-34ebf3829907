import { LightningElement } from 'lwc';
import { OmniscriptBaseMixin } from 'omnistudio/omniscriptBaseMixin';

export default class DaoAutoCloseBrowser extends OmniscriptBaseMixin(LightningElement) {

    connectedCallback() {
        // Close the current browser tab when the component is rendered
                // Attempt to close the tab directly
                const isClosed = window.close();

                // If direct close fails, redirect to a URL designed to close the tab
                if (!isClosed) {
                    window.location.replace('about:blank');
                }
    }
}