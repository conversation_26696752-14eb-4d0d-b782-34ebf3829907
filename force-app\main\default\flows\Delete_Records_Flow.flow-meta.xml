<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>59.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>Delete Records Flow</description>
    <environments>Default</environments>
    <interviewLabel>Delete Records Flow {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Delete Records Flow</label>
    <loops>
        <name>Loop_through_Ids</name>
        <label>Loop through Ids</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <collectionReference>ids</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Delete_record</targetReference>
        </nextValueConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordDeletes>
        <description>Delete record</description>
        <name>Delete_record</name>
        <label>Delete record</label>
        <locationX>264</locationX>
        <locationY>242</locationY>
        <connector>
            <targetReference>Loop_through_Ids</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Address_Value__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Loop_through_Ids</elementReference>
            </value>
        </filters>
        <object>Email_Preferences__c</object>
    </recordDeletes>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Loop_through_Ids</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <description>ids</description>
        <name>ids</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
