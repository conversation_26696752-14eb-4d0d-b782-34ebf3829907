<template>
    <table style="border-collapse: collapse; width: 100%; height: 213px; margin-left: auto; margin-right: auto;" border="1">
        <tbody>
            <tr style="height: 15px;" class="hor_Line">
                <td class="cell_width Table_header" colspan="3" style="border-right:20px solid white"><strong>Money Market Accounts</strong></td>
                <td class="cell_width Table_header"><strong>CD's</strong></td>
            </tr>
            <tr style="background-color: #d5e3cf;" class="hor_Line">
                <td class="cell_width Odd_row" colspan="3" style="border-right:20px solid white">Business money market accounts offer higher dividends, give you check-writing privileges, and allows you to withdraw at any time</td>
                <td class="cell_width Odd_row">Grow your company's savings with higher dividends compared to other savings accounts.</td>
            </tr>
            <tr style="background-color: #ebf1e9;" class="hor_Line">
                <td class="cell_width line Sub_header" style="font-size: 19px; height: 15px; "><strong>Growth</strong></td>
                <td class="cell_width line Sub_header" style="font-size: 19px; height: 15px; "><strong>Performance</strong></td>
                
                <td class="cell_width Sub_header" style="font-size: 19px; height: 15px; border-right:20px solid white"><strong>premier</strong></td>
                <td class="cell_width Sub_header">Bump up option to be able to request a new, higher interest rate if rates increase during your term</td>

            </tr>
            <tr style="background-color: #d5e3cf; " class="hor_Line">
                <td class="cell_width line Odd_row" style="color:red; font-size: 16px;"> Up to 1.00% APY</td>
                <td class="cell_width line Odd_row" style="color:red; font-size: 16px;"> Up to 3.50% APY</td>
                 <td class="cell_width line Odd_row" style="color:red; font-size: 16px; border-right:20px solid white"> Up to 5.00% APY</td>
                <td class="cell_width Odd_row"> Add on option to deposit additional funds anytime during your term ($500 minimum).</td>
            </tr>
            <tr class="hor_Line" style="background-color: #ebf1e9;">
                <td class="cell_width line Even_row">--</td>
                <td class="cell_width line Even_row" style="text-align:left; vertical-align: top;">
                    <p><strong>Required:</strong></p>
                    -Active Business Checking Account*
                </td>
                <td class="cell_width line Even_row" style="text-align:left; border-right:20px solid white">
                    <p><strong>Required:</strong></p>
                    <p>-Active Business Checking Account*</p>
                    <p>-Monthly Digital Deposit of $500 or more</p>
                    <p>-Active RCU Credit Card</p>
                </td>
                <td class="cell_width Even_row">Flexible term lengths</td>

            </tr>
              <tr class="hor_Line" style="background-color: #d5e3cf;">
                <td class="cell_width line Odd_row" rowspan="2">--</td>
                <td class="cell_width line Odd_row" rowspan="2" style="text-align:left; vertical-align: top;">
                    <p><strong>Atleast one of the following:</strong></p>
                    <p>-Monthly Digital Deposit of $500 or more</p>
                    <p>-Active RCU Credit Card</p>
                    <p>-Open loan at RCU</p>
                    <p>-Investment account through RCU</p>
                    <p>-Active property and casualty insurance policy through RCU Insurance Services</p>
                </td>
                <td class="cell_width line Odd_row" rowspan="2" style="text-align:left; vertical-align: top; border-right:20px solid white">
                    <p><strong>At least one of the following:</strong></p>
                    <p>-Open loan at RCU</p>
                    <p>Investment account through RCU</p>
                    <p>Active property and casualty insurance policy through RCU insurance Services</p>
                </td>
                <td class="cell_width Odd_row">Earn More with an Active Business Checking Account*</td>
            </tr>
            <tr class="hor_Line" style="background-color: #ebf1e9;">
                <td class="cell_width Even_row">High balances earn higher rates (Min $1000)</td> 
            </tr>
            <tr style="background-color: #ebf1e9;" class="hor_Line">
                <td class="cell_width line Even_row">Open with low balance<p>(Min $100)</p></td>
                <td class="cell_width line Even_row">Open with low balance<p>(Min $100)</p></td>
                <td class="cell_width Even_row" style="border-right:20px solid white">Min $2,500</td>
                <td class="cell_width Odd_row" >Fixed rates to be able to easily predict your earnings by knowing the dividend rates will remain the same during the term of your business CD.</td>
            </tr>
            <tr style="background-color: #d5e3cf;" class="hor_Line">
                <td class="cell_width Odd_row" colspan="3" style="border-right:20px solid white"><a href="#">More info </a></td>
                <td class="cell_width Even_row" ><a href="#">More info </a></td>  
            </tr>
            <tr style="background-color: #ebf1e9;" class="hor_Line">
                <td class="cell_width Even_row" colspan="3" style="border-right:20px solid white">(Select a Money Market Account Below)</td>
                <td class="cell_width Odd_row"  ></td>  
            </tr>
            <tr style="background-color: #d5e3cf;" class="hor_Line">
             <td class="cell_width line Odd_row"> <lightning-input type="checkbox" onchange={handleCheckboxChange1}></lightning-input></td>
             <td class="cell_width line Odd_row"> <lightning-input type="checkbox" onchange={handleCheckboxChange2}></lightning-input></td>
             <td class="cell_width Odd_row" style="border-right:20px solid white"> <lightning-input type="checkbox" onchange={handleCheckboxChange3}></lightning-input></td>
             <td class="cell_width Even_row"> <lightning-input type="checkbox" onchange={handleCheckboxChange4}></lightning-input></td>
                
            </tr>

        </tbody>
    </table>
    <br>&nbsp;
    <div class="button-container">
        <button class="previous-btn" onclick={handlePreviousPage}>Back</button>
        <button class="save-continue-btn" onclick={handleNextPage}>Next</button>
    </div>
    <!-- <lightning-button label="Next" onclick={handleNextPage} variant="brand" class="slds-float_right"></lightning-button>
    <lightning-button label="Back" onclick={handlePreviousPage} class="slds-float_right" stretch style="margin-right: 1%;" size="large"></lightning-button> -->

</template>