<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>InsurancePolicyId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InsurancePolicyVersionId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ParentTransactionId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TransactionNumber</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TransactionAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TransactionTaxAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TransactionFeeAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalTransactionAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TransactionEffectiveDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TransactionPostedDate</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
