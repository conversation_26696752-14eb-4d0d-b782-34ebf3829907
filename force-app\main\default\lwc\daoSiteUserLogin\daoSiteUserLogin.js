import { LightningElement, track , api} from 'lwc';
import {OmniscriptBaseMixin} from 'omnistudio/omniscriptBaseMixin';
import getLoginUrl from '@salesforce/apex/DAORemote.getLoginUrl';

export default class DaoSiteUserLogin extends OmniscriptBaseMixin(LightningElement) {
    @api omniScript;
    @api contextId;
    loginUrl;
    isPolling = false;
    firstName;
    lastName;
    @api email;
    @api userFound;

    connectedCallback() {
        console.log('inside lwc daositeuserlogin');
        this.contextId = this.omniJsonData.ContextId;
        this.email = this.omniJsonData.UserEmail;
        this.userFound = this.omniJsonData.UserFound;
        this.isPolling = true;
    }

    renderedCallback() {
        console.log('inside lwc daositeuserlogin2');
        this.fetchLoginUrl()
    }

    fetchLoginUrl() {
        console.log('inside lwc daositeuserlogin3');
        const input = {
                    userFound: this.firstName,
                    email: this.email,
                    contextId: this.contextId,
                };

        getLoginUrl({ input: input, output: null })
                .then((result) => {
                     if (result === 'Error') {
                        console.log('New Stringified error:', JSON.stringify(result));
                     }
                     let dataToPass = {};
                     this.loginUrl = result;
                     console.log('this.loginurl>> '+this.loginUrl);
                     dataToPass = {
                         loginUrl: this.loginUrl
                     };
             
                     setTimeout(() => {
                         this.omniApplyCallResp(dataToPass); // Send to OmniScript
                         this.omniNextStep(); // Move to next step
                     }
                     , 5000);

                })
                .catch(error => {
                    if (error.body) {
                        if (Array.isArray(error.body)) {
                            error.body.forEach(err => console.error(err.message));
                        } else if (typeof error.body.message === 'string') {
                            console.error('Apex Error Message:', error.body.message);
                        }
                    }
                });
    }
}