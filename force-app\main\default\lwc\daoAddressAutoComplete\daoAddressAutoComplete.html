<!--
  @description       : Prototype for the Smarty component
  <AUTHOR> Zennify
  @last modified on  : 05-13-2025
  @last modified by  : <PERSON><PERSON><PERSON>
-->
<template>
    <div class="slds-grid slds-wrap slds-m-top_none ">
        <div class="slds-form-element slds-col slds-size_2-of-3 slds-m-top_none slds-m-left_small slds-m-right_small slds-m-bottom_small" >
            <div class="slds-form-element__control">
              <div class="slds-input-has-icon slds-input-has-icon_right">
                <lightning-input 
                    type="text" 
                    label="Address" 
                    id="inputField" 
                    onchange={handleInputChange} 
                    value={fullAddress}
                    required
                ></lightning-input>
                <template if:true={isAddressFilled}>
                    <lightning-button-icon icon-name="utility:edit"   onclick={handlePencilClick} alternative-text="Edit" title="Edit"></lightning-button-icon>
                </template>
              </div>
              <div if:true={suggestion}>
                <div class="slds-m-around_medium" role="listbox">
                    <ul class="slds-listbox slds-listbox_vertical slds-dropdown slds-dropdown_fluid"
                        role="presentation">
                        <template for:each={suggestion} for:item="addressRecommendation">
                            <li data-key={addressRecommendation.id} key={addressRecommendation.id} role="presentation"
                                onclick={setAddress}
                                data-value={addressRecommendation.value} class="slds-listbox__item">
                                <span
                                    class="slds-media slds-listbox__option slds-listbox__option_entity slds-listbox__option_has-meta"
                                    role="option">
                                    <span class="slds-media__body slds-m-left_xx-small slds-m-bottom_xx-small">
                                        <div class="slds-grid slds-m-bottom_small">
                                            <div class="slds-col slds-size_1-of-10">
                                                <lightning-button-icon size="medium" icon-name="utility:checkin"
                                                    class="slds-input__icon" variant="bare">
                                                </lightning-button-icon>
                                            </div>
                                            <div class="slds-m-left_medium slds-col slds-size_8-of-10">
                                                <span
                                                    class="slds-listbox__option-text slds-listbox__option-text_entity"><b>{addressRecommendation.value}</b></span>
                                            </div>
                                            <div class="slds-col slds-size_1-of-10"></div>
                                        </div>
                                    </span>
                                </span>
                            </li>
                        </template>
                    </ul>
                </div>
            </div>
            </div>
          </div>
        <template if:true={showAddressForm}>
            <div class="slds-col slds-size_2-of-3 slds-m-around_small slds-m-top_none">
                    <lightning-input-address
                        address-label=""
                        street-label="Street"
                        city-label="City"
                        country-label="Country"
                        province-label="State"
                        state-label="State"
                        postal-code-label="PostalCode"
                        street={street}
                        city={city}
                        country="US"
                        postal-code={postalCode}
                        province={state}
                        required
                        field-level-help=""
                        onchange={handleAddressChange}>
                </lightning-input-address>
            </div>
        </template>

    </div>
</template>