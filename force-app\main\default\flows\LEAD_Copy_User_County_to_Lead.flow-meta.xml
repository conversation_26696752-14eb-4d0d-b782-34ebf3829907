<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <description>When an ELO lead is manually created copy the user (ELO) County to the Lead</description>
    <environments>Default</environments>
    <interviewLabel>[LEAD] Copy User County to Lead {!$Flow.CurrentDateTime}</interviewLabel>
    <label>[LEAD] Copy User County to Lead</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>Copy User County to Lead</description>
        <name>Copy_User_County_to_Lead</name>
        <label>Copy User County to Lead</label>
        <locationX>176</locationX>
        <locationY>287</locationY>
        <inputAssignments>
            <field>Mortgage_County__c</field>
            <value>
                <elementReference>$User.ELO_User_County__c</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Copy_User_County_to_Lead</targetReference>
        </connector>
        <filterFormula>{!$Record.RecordType.DeveloperName} = &quot;Mortgage_Lead&quot;</filterFormula>
        <object>Lead</object>
        <recordTriggerType>Create</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
