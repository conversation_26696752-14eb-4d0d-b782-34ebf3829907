<template>
    <div style="background-color:white; font-size:15px;"> 
        <center>Please select any other products and services you would like to hear about below:</center> 
    </div>
    <table style="border-collapse: collapse; width: 100%; height: 213px; margin-left: auto; margin-right: auto;" border="1">
        <tbody>
            <tr style="height: 15px; border-bottom: 5px solid #ffffff;" >
                <td class="cell_width Table_header" colspan="4" style="color: #edf5e4;"><strong>.</strong></td>
            </tr>
            <tr style="height: 15px; font-size: 19px;" class="hor_Line Sub_header">
                <td class="cell_width line"><strong>Term Loan</strong></td>
                <td class="cell_width line"><strong>SBA Loan</strong></td>
                <td class="cell_width line"><strong>Lines of Credit</strong></td>
                <td class="cell_width "><strong>Commercial Real Estate</strong></td>
            </tr>
            <tr class="hor_Line Odd_row"> 
                <td class="cell_width line">Get the funding you need to pay for services or assets to help your business grow and thrive.</td>
                <td class="cell_width line">With the help of an SBA 7(a) or 504 business loan, you can receive funds for manufacturing equipment, commercial real estate, technology or inventory, debt refinancing and more.</td>
                <td class="cell_width line">Our small business lines of credit lets you borrow what you need it, to keep operating your business.</td>
                <td class="cell_width ">Purchase or renovate commercial property with a commercial real estate laon from RCU.</td>
            </tr>
            <tr class="hor_Line Even_row">
                <td class="cell_width line">Finance autos for commercial use, equipment, working capital, etc.</td>
                <td class="cell_width line">Flexible loan terms from 7 to 25 years</td>
                <td class="cell_width line">We offer lines of credit from $5,000 to $4MM.</td>
                <td class="cell_width "> Available for most property types, including multi-family, mixed use and other owner-used and investment properties,</td>
                
            </tr>
            <tr class="hor_Line Odd_row">
                <td class="cell_width line"> Terms between 1-7 years.</td>
                <td class="cell_width line"> SBA 7a Loans: An SBA 7(a) loan provides you flexibility. Use the funds for debts refinancing, equipment and real estate purchases, acquistions, and more.</td>
                <td class="cell_width line">Unsecured lines of credit up to $50,000 with no fees.</td>
                <td class="cell_width ">Up to 30 year amortization.</td>
                

            </tr>
            <tr class="hor_Line Even_row">
                <td class="cell_width line">Fixed Interest Rate</td>
                <td class="cell_width line">SBA 7a Loans: With loan amounts between $50,000 and $5,00,000, you can find the right financing for your business.</td>
                <td class="cell_width line">Terms form 1-5 years.</td>
                <td class="cell_width ">&nbsp;</td>
              

            </tr>
            <tr class="hor_Line Odd_row">
                <td class="cell_width line">-</td>
                <td class="cell_width line">SBA 504 Loans: Finance up to 90% of your projects value.</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width ">&nbsp;</td>
             

            </tr>
            <tr class="hor_Line Even_row">
                <td class="cell_width line">-</td>
                <td class="cell_width line">SBA 504 Loans: Purchase major business assets including property and long-term machinery to help your business operate.</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width line">&nbsp;</td>
              
            </tr>
             <tr class="hor_Line Odd_row">
                <td class="cell_width line"><a href="#">More info</a></td>
                <td class="cell_width line"><a href="#">More info</a> </td>
                <td class="cell_width line"><a href="#">More info</a></td>
                <td class="cell_width line"><a href="#">More info</a></td>
               
            </tr>
            <tr class="hor_Line Even_row">
                <td colspan="4" class="cell_width ">
                    .
                </td>   
            </tr>
            <tr class="hor_Line Odd_row">
                <td class="cell_width line" >  
                    <!-- <lightning-input type="checkbox" if:true={termLoan} checked="true" onchange={handleCheckboxChange1}></lightning-input> -->
                    <lightning-input type="checkbox"  onchange={handleCheckboxChange1}></lightning-input>
                </td>
                <td class="cell_width line"> 
                    <!-- <lightning-input type="checkbox" if:true={sbaLoan} checked="true" onchange={handleCheckboxChange2}></lightning-input> -->
                    <lightning-input type="checkbox"  onchange={handleCheckboxChange2}></lightning-input>
                </td>
                <td class="cell_width line"> 
                    <!-- <lightning-input type="checkbox" if:true={linesOfCredit} checked="true" onchange={handleCheckboxChange3}></lightning-input> -->
                    <lightning-input type="checkbox"  onchange={handleCheckboxChange3}></lightning-input>
                </td>
                <td class="cell_width line"> 
                    <!-- <lightning-input type="checkbox" if:true={commercialRealState} checked="true" onchange={handleCheckboxChange4}></lightning-input> -->
                    <lightning-input type="checkbox"  onchange={handleCheckboxChange4}></lightning-input>
                </td>

                
            </tr>
        </tbody>
    </table>
    
    <br>&nbsp;
    <div class="button-container">
        <button class="previous-btn" onclick={handlePreviousPage}>Back</button>
        <button class="save-continue-btn" onclick={handleNextPage}>Next</button>
    </div>
    <!-- <lightning-button label="Next" onclick={handleNextPage} variant="brand" class="slds-float_right"></lightning-button>
    <lightning-button label="Back" onclick={handlePreviousPage} class="slds-float_right" stretch style="margin-right: 1%;" size="large"></lightning-button> -->

</template>