<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OpportunityId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MortgageProgramType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OtherMortgageProgramType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OtherAmortizationType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LoanPurpose</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OtherLoanPurpose</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsBorrowerCommPropState</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsPropertyCommPropState</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsContractConversion</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsRenovation</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsConstructionConversion</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsSingleClosing</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ImprovementCost</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LotAcquiredDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LotOriginalCost</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RefinanceType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RefinanceProgramType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OtherRefinanceProgType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsEnergyRelImprovement</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsSubjectToPriorityLien</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ProjectType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MortgageLienType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MonthsBeforeFirstAdj</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MonthsBetweenAdjustments</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsBalloon</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BalloonTermMonths</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsInterestOnly</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IntOnlyTermMonthCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsNegativeAmortization</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HasPrepaymentPenalty</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PrepayPenaltyTermMonth</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsTempIntRateBuydown</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InitialBuydownRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HasOtherLoanFeature</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OtherLoanFeature</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EstateHoldType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LeaseHoldExpirationDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TitleType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TrustTitleType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>NativeLandTenure</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InterestRate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AmortizationTerm</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AmortizationType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TotalFinancialAssetAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TotalOtherAssetAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TotalFinLiabilityMonthlyAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TotalOtherFinLiabilityMonthlyAmt</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
