<?xml version="1.0" encoding="UTF-8"?>
<CustomObjectTranslation xmlns="http://soap.sforce.com/2006/04/metadata">
    <fieldSets>
        <label><!-- Relationship Group Members --></label>
        <name>WM_Client_Relationship_Group_Members</name>
    </fieldSets>
    <layouts>
        <layout>Contact (Business) Layout</layout>
        <sections>
            <label><!-- Custom Links --></label>
            <section>Custom Links</section>
        </sections>
        <sections>
            <label><!-- Referral Information --></label>
            <section>Referral Information</section>
        </sections>
    </layouts>
    <layouts>
        <layout>Contact (Individual) Layout</layout>
        <sections>
            <label><!-- Address 2 --></label>
            <section>Address 2</section>
        </sections>
        <sections>
            <label><!-- Contact Details --></label>
            <section>Contact Details</section>
        </sections>
        <sections>
            <label><!-- Custom Links --></label>
            <section>Custom Links</section>
        </sections>
        <sections>
            <label><!-- Employment Information --></label>
            <section>Employment Information</section>
        </sections>
        <sections>
            <label><!-- Phone and Email --></label>
            <section>Phone and Email</section>
        </sections>
    </layouts>
    <layouts>
        <layout>Contact (Policyholder) Layout</layout>
        <sections>
            <label><!-- Communication Details --></label>
            <section>Communication Details</section>
        </sections>
        <sections>
            <label><!-- Contact Details --></label>
            <section>Contact Details</section>
        </sections>
        <sections>
            <label><!-- Custom Links --></label>
            <section>Custom Links</section>
        </sections>
        <sections>
            <label><!-- Demographics --></label>
            <section>Demographics</section>
        </sections>
        <sections>
            <label><!-- Employment Information --></label>
            <section>Employment Information</section>
        </sections>
        <sections>
            <label><!-- System Information --></label>
            <section>System Information</section>
        </sections>
    </layouts>
    <layouts>
        <layout>Contact (Retail Client - Individual) Layout</layout>
        <sections>
            <label><!-- Communication Details --></label>
            <section>Communication Details</section>
        </sections>
        <sections>
            <label><!-- Communication Preferences --></label>
            <section>Communication Preferences</section>
        </sections>
        <sections>
            <label><!-- Custom Links --></label>
            <section>Custom Links</section>
        </sections>
        <sections>
            <label><!-- Know Your Client --></label>
            <section>Know Your Client</section>
        </sections>
        <sections>
            <label><!-- System Information --></label>
            <section>System Information</section>
        </sections>
    </layouts>
    <recordTypes>
        <description><!-- Contact data for an institution or a business account --></description>
        <label><!-- Business --></label>
        <name>FinServ__IndustriesBusiness</name>
    </recordTypes>
    <recordTypes>
        <description><!-- Contact data for a retail client --></description>
        <label><!-- Individual --></label>
        <name>FinServ__IndustriesIndividual</name>
    </recordTypes>
    <validationRules>
        <errorMessage><!-- Please user the &quot;MM/DD&quot; format. --></errorMessage>
        <name>Birthdate_MM_DD_Proper_Format</name>
    </validationRules>
    <validationRules>
        <errorMessage><!-- Date of birth can&apos;t be in the future. --></errorMessage>
        <name>FinServ__BirthdateCannotBeInFuture</name>
    </validationRules>
    <validationRules>
        <errorMessage><!-- Only one address can be designated as the primary address. --></errorMessage>
        <name>FinServ__CannotHaveMoreThanOnePrimaryAddress</name>
    </validationRules>
    <validationRules>
        <errorMessage><!-- Can&apos;t associate a lead or contact to a household account. --></errorMessage>
        <name>FinServ__ContactCannotBeAssociatedToHousehold</name>
    </validationRules>
    <validationRules>
        <errorMessage><!-- The selected account has an Individual record type and can&apos;t have additional contacts associated with it --></errorMessage>
        <name>FinServ__IndividualCannotHaveMoreThanOneContact</name>
    </validationRules>
    <validationRules>
        <errorMessage><!-- Internal referrers must be internal Salesforce users. --></errorMessage>
        <name>FinServ__NonPortalUserReferrer</name>
    </validationRules>
    <validationRules>
        <errorMessage><!-- The Individual record type can&apos;t be changed --></errorMessage>
        <name>FinServ__NotAllowingConversionFromIndividual</name>
    </validationRules>
    <validationRules>
        <errorMessage><!-- The record type can&apos;t be changed to Individual. --></errorMessage>
        <name>FinServ__NotAllowingConversionToIndividual</name>
    </validationRules>
    <validationRules>
        <errorMessage><!-- Enter an internal referrer or an external referrer. You can list only one referrer for each referral. --></errorMessage>
        <name>FinServ__SingleReferredByEntryByRecord</name>
    </validationRules>
    <webLinks>
        <label><!-- AddRelation --></label>
        <name>FinServ__AddRelation</name>
    </webLinks>
    <webLinks>
        <label><!-- GoogleMaps --></label>
        <name>GoogleMaps</name>
    </webLinks>
    <webLinks>
        <label><!-- GoogleSearch --></label>
        <name>GoogleSearch</name>
    </webLinks>
    <webLinks>
        <label><!-- Mass_Delete_Records --></label>
        <name>Mass_Delete_Records</name>
    </webLinks>
    <webLinks>
        <label>Réabonnement Marketing Cloud</label>
        <name>et4ae5__ExactTarget_Resubscribe</name>
    </webLinks>
    <webLinks>
        <label>Désabonnement Marketing Cloud</label>
        <name>et4ae5__ExactTarget_Unsubscribe</name>
    </webLinks>
    <webLinks>
        <label>Envoyer un e-mail Marketing Cloud</label>
        <name>et4ae5__Send_ExactTarget_Email</name>
    </webLinks>
    <webLinks>
        <label>Envoyer un SMS Marketing Cloud</label>
        <name>et4ae5__Send_ExactTarget_SMS</name>
    </webLinks>
    <webLinks>
        <label>Afficher les analyses Marketing Cloud</label>
        <name>et4ae5__View_ExactTarget_Analytics</name>
    </webLinks>
</CustomObjectTranslation>
