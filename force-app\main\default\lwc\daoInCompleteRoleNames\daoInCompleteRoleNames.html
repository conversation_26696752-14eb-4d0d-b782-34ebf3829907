<template>
    <lightning-card>
        <template if:true={daoRoles.length}>
            <p style="color:red">*Please fill these Incomplete Role Names</p>
            <table class="slds-table slds-table_cell-buffer slds-table_bordered">
                <thead>
                    <tr>
                        <th scope="col">Role Name</th>
                    </tr>
                </thead>
                <tbody>
                    <template for:each={daoRoles} for:item="role">
                        <tr key={role.Id}>
                            <td>
                                <a href="javascript:void(0);" onclick={handleRoleClick} data-id={role.Id}>
                                    {role.Name}
                                </a>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </template>
        <template if:false={daoRoles.length}>
        </template>
    </lightning-card>
</template>