/**
 * @description       : Provide test coverage for DAOAddressAutoComplete class
 * <AUTHOR> Zennify
 * @last modified on  : 06-13-2025
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
@isTest
public class DAOAddressAutoCompleteTest {
    class MockHttpResponseGenerator implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setBody('{"suggestions": ["123 Main St", "456 Oak Ave"]}');
            res.setStatusCode(200);
            return res;
        }
    }

    @isTest
    static void testGetAddress() {
        // Set up mock callout
        Test.setMock(HttpCalloutMock.class, new MockHttpResponseGenerator());
        
        
        Test.startTest();
        String result = DAOAddressAutoComplete.getAddress('Main St');
        Test.stopTest();
        System.assertNotEquals(null, result, 'The response should not be null');
    }

    @isTest
    static void testGetAddressException() {
        // No mock set, should throw exception
        try {
            DAOAddressAutoComplete.getAddress('Test');
            System.assert(false, 'Exception should have been thrown');
        } catch (Exception e) {
            System.assert(e.getMessage() != null);
        }
    }
}