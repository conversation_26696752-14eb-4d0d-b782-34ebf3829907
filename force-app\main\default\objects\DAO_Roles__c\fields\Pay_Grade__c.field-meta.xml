<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Pay_Grade__c</fullName>
    <label>Pay Grade</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>E1</fullName>
                <default>false</default>
                <label>E1</label>
            </value>
            <value>
                <fullName>E2</fullName>
                <default>false</default>
                <label>E2</label>
            </value>
            <value>
                <fullName>E3</fullName>
                <default>false</default>
                <label>E3</label>
            </value>
            <value>
                <fullName>E4</fullName>
                <default>false</default>
                <label>E4</label>
            </value>
            <value>
                <fullName>E5</fullName>
                <default>false</default>
                <label>E5</label>
            </value>
            <value>
                <fullName>E6</fullName>
                <default>false</default>
                <label>E6</label>
            </value>
            <value>
                <fullName>E7</fullName>
                <default>false</default>
                <label>E7</label>
            </value>
            <value>
                <fullName>E8</fullName>
                <default>false</default>
                <label>E8</label>
            </value>
            <value>
                <fullName>E9</fullName>
                <default>false</default>
                <label>E9</label>
            </value>
            <value>
                <fullName>O1</fullName>
                <default>false</default>
                <label>O1</label>
            </value>
            <value>
                <fullName>O1E</fullName>
                <default>false</default>
                <label>O1E</label>
            </value>
            <value>
                <fullName>O2</fullName>
                <default>false</default>
                <label>O2</label>
            </value>
            <value>
                <fullName>O2E</fullName>
                <default>false</default>
                <label>O2E</label>
            </value>
            <value>
                <fullName>O3</fullName>
                <default>false</default>
                <label>O3</label>
            </value>
            <value>
                <fullName>O3E</fullName>
                <default>false</default>
                <label>O3E</label>
            </value>
            <value>
                <fullName>O4</fullName>
                <default>false</default>
                <label>O4</label>
            </value>
            <value>
                <fullName>O5</fullName>
                <default>false</default>
                <label>O5</label>
            </value>
            <value>
                <fullName>O6</fullName>
                <default>false</default>
                <label>O6</label>
            </value>
            <value>
                <fullName>O7</fullName>
                <default>false</default>
                <label>O7</label>
            </value>
            <value>
                <fullName>O8</fullName>
                <default>false</default>
                <label>O8</label>
            </value>
            <value>
                <fullName>O9</fullName>
                <default>false</default>
                <label>O9</label>
            </value>
            <value>
                <fullName>O10</fullName>
                <default>false</default>
                <label>O10</label>
            </value>
            <value>
                <fullName>W1</fullName>
                <default>false</default>
                <label>W1</label>
            </value>
            <value>
                <fullName>W2</fullName>
                <default>false</default>
                <label>W2</label>
            </value>
            <value>
                <fullName>W3</fullName>
                <default>false</default>
                <label>W3</label>
            </value>
            <value>
                <fullName>W4</fullName>
                <default>false</default>
                <label>W4</label>
            </value>
            <value>
                <fullName>W5</fullName>
                <default>false</default>
                <label>W5</label>
            </value>
            <value>
                <fullName>GS1</fullName>
                <default>false</default>
                <label>GS1</label>
            </value>
            <value>
                <fullName>GS2</fullName>
                <default>false</default>
                <label>GS2</label>
            </value>
            <value>
                <fullName>GS3</fullName>
                <default>false</default>
                <label>GS3</label>
            </value>
            <value>
                <fullName>GS4</fullName>
                <default>false</default>
                <label>GS4</label>
            </value>
            <value>
                <fullName>GS5</fullName>
                <default>false</default>
                <label>GS5</label>
            </value>
            <value>
                <fullName>GS6</fullName>
                <default>false</default>
                <label>GS6</label>
            </value>
            <value>
                <fullName>GS7</fullName>
                <default>false</default>
                <label>GS7</label>
            </value>
            <value>
                <fullName>GS8</fullName>
                <default>false</default>
                <label>GS8</label>
            </value>
            <value>
                <fullName>GS9</fullName>
                <default>false</default>
                <label>GS9</label>
            </value>
            <value>
                <fullName>GS10</fullName>
                <default>false</default>
                <label>GS10</label>
            </value>
            <value>
                <fullName>GS11</fullName>
                <default>false</default>
                <label>GS11</label>
            </value>
            <value>
                <fullName>GS12</fullName>
                <default>false</default>
                <label>GS12</label>
            </value>
            <value>
                <fullName>GS13</fullName>
                <default>false</default>
                <label>GS13</label>
            </value>
            <value>
                <fullName>GS14</fullName>
                <default>false</default>
                <label>GS14</label>
            </value>
            <value>
                <fullName>GS15</fullName>
                <default>false</default>
                <label>GS15</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
