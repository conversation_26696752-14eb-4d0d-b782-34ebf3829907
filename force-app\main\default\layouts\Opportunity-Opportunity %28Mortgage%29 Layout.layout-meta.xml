<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Opportunity Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Buyers_Agent__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Agency__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Loan_Officer__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Loan_Number__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Record_Type_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>First_Payment_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sent_to_Processing_Date__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BK_Application_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CloseDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>StageName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Probability</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BK_External_ID__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Property Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Property_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Property_Address__c</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Mortgage Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Lien_Position__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Loan_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Loan_Reason__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Construction_Loan_Reason__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Purchase_Price__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Interest_Rate_Locked__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>First_Mortgage_Requested_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>First_Mortgage_Amount_Funded__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HELOAN_Amount_Requested__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HELOAN_Amount_Funded__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RCU_Fees__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Appraisal_Fee__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Downpayment_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Second_Mortgage_Requested_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Second_Mortgage_Amount_Funded__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HELOC_Requested_Amount__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>HELOC_Amount_Funded__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Loan_to_Value_Ratio__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Other Information</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Additional Information</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Description Information</label>
        <layoutColumns/>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Referral Information</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Clone</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Share</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewEventAdvisor</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__NewTaskAdvisor</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FinServ__LogACallAdvisor</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <relatedContent>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>AccountId</field>
            </layoutItem>
        </relatedContentItems>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>OwnerId</field>
            </layoutItem>
        </relatedContentItems>
    </relatedContent>
    <relatedLists>
        <fields>ContactName</fields>
        <fields>Role</fields>
        <fields>ContactTitle</fields>
        <fields>IsPrimary</fields>
        <fields>ContactPhone</fields>
        <fields>ContactEmail</fields>
        <fields>ContactAccountName</fields>
        <relatedList>RelatedContactRoleList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>NAME</fields>
        <fields>Contact__c</fields>
        <fields>Role__c</fields>
        <relatedList>Application_Role__c.Application__c</relatedList>
    </relatedLists>
    <relatedObjects>AccountId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hDn00000CS4qC</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
