<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <environments>Default</environments>
    <interviewLabel>DeactivateCustomerCommunityUsers {!$Flow.CurrentDateTime}</interviewLabel>
    <label>DeactivateCustomerCommunityUsers</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GetProfileId</name>
        <label>GetProfileId</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>UpdateCommunityUsers</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>DAO Customer Community</stringValue>
            </value>
        </filters>
        <object>Profile</object>
        <outputAssignments>
            <assignToReference>CommunityProfileId</assignToReference>
            <field>Id</field>
        </outputAssignments>
    </recordLookups>
    <recordUpdates>
        <name>UpdateCommunityUsers</name>
        <label>UpdateCommunityUsers</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <filters>
            <field>ProfileId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CommunityProfileId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Username</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </filters>
        <inputAssignments>
            <field>IsActive</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <object>User</object>
    </recordUpdates>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetProfileId</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>CommunityProfileId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>CommunityUsersToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>User</objectType>
    </variables>
</Flow>
