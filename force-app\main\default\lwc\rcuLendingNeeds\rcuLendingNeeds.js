import { LightningElement } from 'lwc';
import {OmniscriptBaseMixin} from 'omnistudio/omniscriptBaseMixin';
export default class RcuLendingNeeds extends OmniscriptBaseMixin(LightningElement) {
    lendingNeedCheck=null;
    handleNextPage(event) {
            let dataToPass = {};
            this.lendingNeedCheck=event.target.value;
            console.log("Lending Needs",this.lendingNeedCheck);
            dataToPass={
                lendingNeeds:this.lendingNeedCheck
            };
            this.omniApplyCallResp(dataToPass); // Send to OmniScript
            this.omniNextStep(); // Move to next step
        }

        // Check if Choice is selected and fields are filled
    handlePreviousPage(){
        this.omniPrevStep();
    }
}