import { LightningElement ,api} from 'lwc';
import { OmniscriptBaseMixin } from 'omnistudio/omniscriptBaseMixin';
import getAddress from '@salesforce/apex/DAOAddressAutoComplete.getAddress'


export default class DaoAddressAutoComplete extends OmniscriptBaseMixin(LightningElement) {

    name =""
    phone=""
    website = ""
    accountNumber=""
    fullAddress=""

    doneTypingInterval = 300;
    typingTimer;

    suggestion;
    // formulaPhysicalStreet=null;
    // formulaPhysicalCity;
    // formulaPhysicalState;
    // formulaPhysicalPostal;
    city =''
    state=''
    postalCode=''
    street=''
    @api omniScript;
     @api screentype
    // sameasphysical;
    connectedCallback() {

     //console.log("Address",this.omniJsonData.formulaPhysicalStreet);
     //this.street=this.omniJsonData.formulaPhysicalStreet;
   
     console.log("Same as physical",this.omniJsonData.sameasphysical);
    if(this.screentype=="businessinfo")
    {
        this.street=this.omniJsonData.businessmailingStreet;
        this.city=this.omniJsonData.businessmailingCity;
        this.state=this.omniJsonData.businessmailingstate;
        this.postalCode=this.omniJsonData.businessmailingZipcode;
        this.fullAddress= this.omniJsonData.businessmailingfullAddress;  
    }
    else if(this.screentype=="roleinfo")
    {
        this.street=this.omniJsonData.rolemailingStreet;
        this.city=this.omniJsonData.rolemailingCity;
        this.state=this.omniJsonData.rolemailingstate;
        this.postalCode=this.omniJsonData.rolemailingZipcode;
        this.fullAddress= this.omniJsonData.rolemailingfullAddress; 
    }
     const state = this.omniGetSaveState();
     if(state) this.value = state;
    
    }

    handleInputChange(event) {
        clearTimeout(this.typingTimer);
        console.log('event value : ' + event.detail.value);
        let value = event.target.value;
        this.typingTimer = setTimeout(() => {
            if(value){
                getAddress({ search: value }).then((result) => {
                    let temp  = JSON.parse(result);
                    let suggestionList = []
                    temp.suggestions.forEach((elem)=>{
                        let address = ''+elem.street_line +','+elem.city +','+elem.state +','+elem.zipcode;
                        
                        suggestionList.push({id : Date.now(),value:address}); 
                    })
                    console.log('this.suggestion '+suggestionList)
                    this.suggestion = suggestionList
             //       const suggestionPane = this.template.querySelector('.slds-popover');
               //     suggestionPane.classList.remove('slds-hide');
                })
            }
        }, this.doneTypingInterval);

      }

      setAddress(event){
        let placeId = event.currentTarget.dataset.value.split(',');
        let key  = event.currentTarget.dataset.key
        this.suggestion = undefined
        console.log("use diffrent addres",this.omniJsonData.sameasphysical)
        this.street = placeId.length > 0 ? placeId[0] :''
        this.city = placeId.length > 1 ? placeId[1] : ''
        this.state = placeId.length > 2 ? placeId[2] : ''
        this.postalCode = placeId.length > 3 ? placeId[3] : ''
        this.country = placeId.length > 4 ? placeId[4] : ''
        this.fullAddress = this.street + ' ' + this.city + ' ' + this.state + ' ' + this.postalCode + ' ' + 'US';
        this.omniSaveState({
            Address: placeId
        });
        
        this.omniUpdateDataJson({
            Address: placeId
        });
        let dataToPass; 
        if(this.screentype=="businessinfo")
        {
            dataToPass={
                businessmailingStreet:this.street,
                businessmailingCity:this.city,
                businessmailingstate:this.state,
                businessmailingZipcode:this.postalCode,
                businessmailingfullAddress:this.fullAddress
            }
        }
        else if(this.screentype=="roleinfo")
        {
             dataToPass={
                rolemailingStreet:this.street,
                rolemailingCity:this.city,
                rolemailingstate:this.state,
                rolemailingZipcode:this.postalCode,
                rolemailingfullAddress:this.fullAddress
            }
        }
        // if(this.omniJsonData.sameasphysical)
        this.omniApplyCallResp(dataToPass);
      }


      handleClick(event){

      }
}