<!--
  @description       : 
  <AUTHOR> Zennify
  @last modified on  : 05-16-2025
  @last modified by  : <PERSON><PERSON><PERSON>
-->
<template>
    <div class="radio-group">

        <!-- Radio button 1 -->
        <label for="option2">
            <lightning-input
                type="radio"
                name="customRadioGroup"
                value="electronic-Checks and Electronic Transactions Only"
                checked={opaSelected1}
                onchange={handleChange}>
            </lightning-input>
            <!-- <span><span style="color:red">*</span> -->
            <span><strong>Checks and Electronic Transactions Only- </strong> RCU will pay overdrafts only on checks, electronic (ACH) and recurring debit card transactions. RCU will not pay everyday debit card transactions.</span>
        </label>

        <!-- Radio button 2 -->
        <label for="option1">
            <lightning-input
                type="radio"
                name="customRadioGroup"
                value="Overdraft-All Overdraft Pay Advantage Services"
                checked={opaSelected}
                onchange={handleChange}> 
                
            </lightning-input>
            <span><strong>All Overdraft Pay Advantage Services-</strong> RCU will pay overdrafts on checks, electronic (ACH) and recurring debit card transactions, and everyday debit card transactions.</span>
        </label>

        

        <!-- Radio button 3 -->
        <label for="option3">
            <lightning-input
                type="radio"
                name="customRadioGroup"
                value="No Overdraft-No Overdraft Pay Advantage Services"
                checked={opaSelected2}
                onchange={handleChange}>
            </lightning-input>
            <span><strong>No Overdraft Pay Advantage Services- </strong> RCU will not pay overdrafts on checks, electronic (ACH) and everyday debit card transactions. Non-sufficient funds (NSF) fees will apply.</span>
        </label>
    </div>
</template>