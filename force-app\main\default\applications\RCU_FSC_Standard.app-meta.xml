<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <brand>
        <headerColor>#0F6B38</headerColor>
        <shouldOverrideOrgTheme>true</shouldOverrideOrgTheme>
    </brand>
    <description>Consumer MLO</description>
    <formFactors>Small</formFactors>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>RCU FSC_Standard</label>
    <navType>Standard</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>RCU_FSC_Accounts</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Real_Estate_Agent</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>RCU_FSC_Accounts</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Real_Estate_Agent</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>RCU_FSC_Accounts</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Real_Estate_Agent</recordType>
        <type>Flexipage</type>
        <profile>Loan Officer</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>RCU_FSC_Accounts</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Real_Estate_Agent</recordType>
        <type>Flexipage</type>
        <profile>Loan Officer</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>RCU_FSC_Accounts</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Real_Estate_Offices</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>RCU_FSC_Accounts</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Real_Estate_Offices</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>RCU_FSC_Accounts</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Real_Estate_Offices</recordType>
        <type>Flexipage</type>
        <profile>Loan Officer</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>RCU_FSC_Accounts</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.Real_Estate_Offices</recordType>
        <type>Flexipage</type>
        <profile>Loan Officer</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lead_Account_Relationship_Record_Page2</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Lead_Account_Relationship__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Loan Officer</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lead_Account_Relationship_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Lead_Account_Relationship__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Loan Officer</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lead_Account_Relationship_Record_Page2</content>
        <formFactor>Small</formFactor>
        <pageOrSobjectType>Lead_Account_Relationship__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Lead_Account_Relationship_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Lead_Account_Relationship__c</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <tabs>standard-home</tabs>
    <tabs>standard-Lead</tabs>
    <tabs>standard-Account</tabs>
    <tabs>standard-report</tabs>
    <tabs>standard-Dashboard</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>RCU_FSC_clone_UtilityBar</utilityBar>
</CustomApplication>
