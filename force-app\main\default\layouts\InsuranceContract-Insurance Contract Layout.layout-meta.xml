<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BusinessLicenseId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EnrollmentStartDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EnrollmentWaitingPeriod</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ProducerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>ContractId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BrokerageAgencyId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EnrollmentEndDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EnrollmentCensusId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>UnderwriterId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
