import { LightningElement, api } from "lwc";
import { OmniscriptBaseMixin } from "omnistudio/omniscriptBaseMixin";
import getAddress from "@salesforce/apex/DAOAddressAutoComplete.getAddress";

export default class Smarty extends OmniscriptBaseMixin(LightningElement) {
  name = "";
  phone = "";
  website = "";
  accountNumber = "";
  fullAddress = "";
  doneTypingInterval = 300;
  typingTimer;
  suggestion;
  city = "";
  state = "";
  postalCode = "";
  street = "";
  showAddressForm = false;

  @api omniScript;
  @api screentype;

  connectedCallback() {
    console.log("OmniScript : ", JSON.stringify(this.omniJsonData, null, 2));
    console.log('screentype'+this.screentype);
    this.fullAddress = '';

    if (this.screentype == "physicalAddress") {
      this.street = this.omniJsonData.rolestreetAddress;
      this.city = this.omniJsonData.rolecityAddress;
      this.state = this.omniJsonData.rolestateAddress;
      this.postalCode = this.omniJsonData.rolezipcode;
      this.fullAddress = this.omniJsonData.rolephysicalFullAddres;
      console.log("Full Address Prefill",this.fullAddress);
    } else if (this.screentype == "mailingAddress" ) {
       console.log('mailingAddress'+this.fullAddress);
      this.street = this.omniJsonData.rolemailingStreet;
      this.city = this.omniJsonData.rolemailingCity;
      this.state = this.omniJsonData.rolemailingState;
      this.postalCode = this.omniJsonData.rolemailingZipcode;
      this.fullAddress = this.omniJsonData.rolemailingfullAddress;
    } else if(this.screentype == "mailingAddressForApplicant" ) {
      if(this.omniJsonData?.RoleInfo?.Use_different_address_for_mailing == true){
      this.street = this.omniJsonData.rolemailingStreetApplication;
      this.city = this.omniJsonData.rolemailingCityApplication;
      this.state = this.omniJsonData.rolemailingStateApplication;
      this.postalCode = this.omniJsonData.rolemailingZipcodeApplication;
      this.fullAddress = this.omniJsonData.rolemailingfullAddressApplication;
      console.log('inside the loop'+this.fullAddress);
      }else{
          this.fullAddress = '';
      this.street = '';
      this.city = '';
      this.state = '';
      this.postalCode = '';
      this.fullAddress ='';
      }
      
    } else if(this.screentype == "previousPhysicalAddress"){
      this.street = this.omniJsonData.rolePreviousPhysicalstreetAddress;
      this.city = this.omniJsonData.rolePreviousPhysicalcityAddress;
      this.state = this.omniJsonData.rolePreviousPhysicalstateAddress;
      this.postalCode = this.omniJsonData.rolePreviousPhysicalzipcode;
      this.fullAddress = this.omniJsonData.rolePreviousPhysicalfullAddress;
    }
    

    console.log("Same as physical", this.omniJsonData.sameasphysical);
    const state = this.omniGetSaveState();
    if (state) this.value = state;
  }

  handleInputChange(event) {
    clearTimeout(this.typingTimer);
    console.log("event value : " + event.detail.value);
    let value = event.target.value;
    this.typingTimer = setTimeout(() => {
      if (value) {
        getAddress({ search: value })
          .then((result) => {
            let temp = JSON.parse(result);
            console.log(JSON.stringify(temp, null, 2));
            let suggestionList = [];
            temp.suggestions.forEach((elem) => {
              let address =
                "" +
                elem.street_line +
                "," +
                elem.city +
                "," +
                elem.state +
                "," +
                elem.zipcode;

              suggestionList.push({ id: Date.now(), value: address });
            });
            console.log("this.suggestion " + suggestionList);
            this.suggestion = suggestionList;
            //       const suggestionPane = this.template.querySelector('.slds-popover');
            //     suggestionPane.classList.remove('slds-hide');
          })
          .catch((error) => {
            console.log("## error in creating records: " + error);
          });
      }
    }, this.doneTypingInterval);
  }

  setAddress(event) {
    let placeId = event.currentTarget.dataset.value.split(",");
    let key = event.currentTarget.dataset.key;
    this.suggestion = undefined;
    console.log("use diffrent addres", this.omniJsonData.sameasphysical);
    this.street = placeId.length > 0 ? placeId[0] : "";
    this.city = placeId.length > 1 ? placeId[1] : "";
    this.state = placeId.length > 2 ? placeId[2] : "";
    this.postalCode = placeId.length > 3 ? placeId[3] : "";
    this.country = placeId.length > 4 ? placeId[4] : "";
    this.fullAddress =
      this.street +
      " " +
      this.city +
      " " +
      this.state +
      " " +
      this.postalCode +
      " " +
      "US";
    this.omniSaveState({
      Address: placeId,
    });

    this.omniUpdateDataJson({
      Address: placeId,
    });
    let dataToPass;
    if (this.screentype == "physicalAddress") {
      dataToPass = {
        rolestreetAddress: this.street,
        rolecityAddress: this.city,
        rolestateAddress: this.state,
        rolezipcode: this.postalCode,
        rolephysicalFullAddres: this.fullAddress,
      };
    } else if (this.screentype == "mailingAddress") {
      dataToPass = {
          rolemailingStreet: this.street,
          rolemailingCity: this.city,
          rolemailingState: this.state,
          rolemailingZipcode: this.postalCode,
          rolemailingfullAddress: this.fullAddress,
      };
    }else if(this.screentype == "mailingAddressForApplicant"){
      dataToPass = {
        rolemailingStreetApplication : this.street,
       rolemailingCityApplication: this.city,
       rolemailingStateApplication :this.state,
      rolemailingZipcodeApplication: this.postalCode,
       rolemailingfullAddressApplication: this.fullAddress,
       
      };
      }
      else if (this.screentype == "previousPhysicalAddress") {
      dataToPass = {
          rolePreviousPhysicalstreetAddress: this.street,
          rolePreviousPhysicalcityAddress: this.city,
          rolePreviousPhysicalstateAddress: this.state,
          rolePreviousPhysicalzipcode: this.postalCode,
          rolePreviousPhysicalfullAddress: this.fullAddress,
      };
    }

    this.omniApplyCallResp(dataToPass);
  }
  handlePencilClick() {
    this.showAddressForm = !this.showAddressForm; // Toggle the visibility
  }

  handleAddressChange(event) {
    // Extract address details from event.detail
    const { street, city, province, postalCode } = event.detail;

    this.street = street || "";
    this.city = city || "";
    this.state = province || "";
    this.postalCode = postalCode || "";

    // Update the OmniScript data
    let dataToPass;
    if (this.screentype == "physicalAddress") {
      dataToPass = {
        rolestreetAddress: this.street,
        rolecityAddress: this.city,
        rolestateAddress: this.state,
        rolezipcode: this.postalCode,
        rolephysicalFullAddres: this.fullAddress,
      };
    } else if (this.screentype == "mailingAddress") {
      dataToPass = {
        rolemailingStreet: this.street,
        rolemailingCity: this.city,
        rolemailingState: this.state,
        rolemailingZipcode: this.postalCode,
        rolemailingfullAddress: this.fullAddress,
      };
    }else if (this.screentype == "previousPhysicalAddress") {
      dataToPass = {
          rolePreviousPhysicalstreetAddress: this.street,
          rolePreviousPhysicalcityAddress: this.city,
          rolePreviousPhysicalstateAddress: this.state,
          rolePreviousPhysicalzipcode: this.postalCode,
          rolePreviousPhysicalfullAddress: this.fullAddress,
      };
    }

    this.omniApplyCallResp(dataToPass);
  }

  // Show pencil icon only if address is filled
  get isAddressFilled() {
    return this.fullAddress && this.fullAddress.trim() !== "";
}
}