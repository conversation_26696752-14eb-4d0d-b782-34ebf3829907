<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LoanApplicationId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AssetType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RealEstateAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DispositionType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PresentMarketValue</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>GrossMonthlyRentalIncome</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RealEstateMonthlyExpense</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>NetMonthlyRentalIncome</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FinancialInstitutionName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CashOrMarketValue</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsJointAsset</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AssetClass</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
