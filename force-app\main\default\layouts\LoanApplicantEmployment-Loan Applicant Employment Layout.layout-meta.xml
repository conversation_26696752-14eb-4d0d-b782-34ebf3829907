<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LoanApplicationId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>LoanApplicantId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EmployerName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EmployerAddress</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsSelfEmployed</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CurrentJobYearCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CurrentJobAdditionalMonthCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>JobTitle</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EmployerPhone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>StartDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EndDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MonthlyIncome</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsRelToTransactionParty</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsLessThanQuarterShare</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MonthlyBaseIncomeAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MonthlyOvertimeAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MonthlyBonusAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MonthlyCommissionAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MonthlyMilitaryEntlAmt</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MonthlyOtherIncomeAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>MonthlyTotalIncomeAmount</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
