import { LightningElement, api, track } from 'lwc';
import isEmailFound from '@salesforce/apex/ApplicationController.isEmailFound';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { OmniscriptBaseMixin } from "omnistudio/omniscriptBaseMixin";

export default class applicationSelector extends OmniscriptBaseMixin(LightningElement) {
    @track selectedOption;
    @track showEmailField = false;
    @track email = '';

    get options() {
        return [
            { label: 'Start a new application', value: 'new' },
            { label: 'Continue an existing application', value: 'existing' }
        ];
    }

    handleOptionChange(event) {
        console.log(event.detail.value);
        this.selectedOption = event.detail.value;
        if (this.selectedOption === 'new') {
            this.email = '';
            this.showEmailField = false;
            this.omniApplyCallResp({ContextId: 'new'});
        } else if (this.selectedOption === 'existing') {
            this.showEmailField = true;
        }
    }

    handleEmailChange(event) {
        console.log('Email--:' + event.detail.value);
        this.email = event.detail.value;
    }

    moveToNextStep(){
        if(!this.selectedOption){
            this.showToast();
        }else if(this.selectedOption == 'new'){
            this.omniNextStep();
        }else{
            if(this.showEmailField && !this.email){
                this.showToast(null,'Put a valid email', null);
            }else if(this.showEmailField && this.email){
                isEmailFound({ email: this.email }).then(result => {
                    console.log('$result: ',result);
                    if (result) {
                        this.omniApplyCallResp({ContextId: this.email});
                        this.omniNextStep();
                    } else {
                        this.showToast('Error','Email address not found in existing records.','error');
                    }
                })
                .catch(error => {
                    console.log('$error: ',error);
                    this.showToast('Error', error.body.message, 'error');
                });
            }
        }
    }

    showToast(title, message, variant) {
        console.log('message:', message);
        const evt = new ShowToastEvent({
            title: title ?? 'Error',
            message: message ?? 'Select an option',
            variant: variant ?? 'error',
        });
        this.dispatchEvent(evt);
    }

}