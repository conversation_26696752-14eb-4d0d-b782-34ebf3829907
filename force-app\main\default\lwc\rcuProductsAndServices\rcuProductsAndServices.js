import { LightningElement } from 'lwc';
import {OmniscriptBaseMixin} from 'omnistudio/omniscriptBaseMixin';
export default class RcuProductsAndServices extends OmniscriptBaseMixin(LightningElement) {

      handleCheckboxChange1(event) { 
}

handleCheckboxChange2(event) {
}

handleCheckboxChange3(event) {
}

handleCheckboxChange4(event) {
}
handleCheckboxChange5(event){

}
handleCheckboxChange6(evevnt){
    
}
handleCheckboxChange7(event){

}

    handleNextPage(event) {
            this.omniNextStep(); // Move to next step
        }

        // Check if Choice is selected and fields are filled
    handlePreviousPage(){
        this.omniPrevStep();
    }
}