<template>
    <table style="border-collapse: collapse; width: 100%; height: 213px; margin-left: auto; margin-right: auto;" border="1">
        <tbody>
            <tr style="height: 15px;" class="hor_Line">
                <td class="cell_width Table_header" colspan="7"><strong>Other products and Services</strong></td>
            </tr>
            <tr style="height: 15px; font-size: 19px;" class="hor_Line Sub_header">
                <td class="cell_width line"><strong>Payroll Services</strong></td>
                <td class="cell_width line"><strong>Merchant Services</strong></td>
                <td class="cell_width line"><strong>ACH Services</strong></td>
                <td class="cell_width line"><strong>Remote Deposit Services</strong></td>
                <td class="cell_width line"><strong>Wealth Management</strong></td>
                <td class="cell_width line"><strong>CD's</strong></td>
                <td class="cell_width"><strong>Insurance Services</strong></td>
            </tr>
            <tr class="hor_Line Odd_row"> 
                <td class="cell_width line">Get trusted payroll services through RCU's partnership with Paychex</td>
                <td class="cell_width line">Easily and efficently process card payment to give your customers an exceptional checkout experience.</td>
                <td class="cell_width line">Fast and secure payments directly to and from your RCU account</td>
                <td class="cell_width line">Makes it easy to quickly and securely deposit multiple checks from the convenience of your own home or office.</td>
                <td class="cell_width line">high-level features and benefits of RCU Wealth Management and the importance of consulting with financial advisor</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width ">&nbsp;</td>
            </tr>
            <tr class="hor_Line Even_row">
                <td class="cell_width line">Automatic payroll and taxes</td>
                <td class="cell_width line">Efficiently integrate information from your online sales and back-end system (such as an ERP or CRM)</td>
                <td class="cell_width line">Invoice services. Quickly and easily schedule payments to or from vendors online.</td>
                <td class="cell_width line"> Review deposit history & check images within digital banking </td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width ">&nbsp;</td>
            </tr>
            <tr class="hor_Line Odd_row">
                <td class="cell_width line"> Tax and regulation monitoring</td>
                <td class="cell_width line"> Automatic credit card payment deposit</td>
                <td class="cell_width line">Create templates to genrate ACH files , or upload ACH files from your payroll or accounting software to pay your team.</td>
                <td class="cell_width line">Filter by batch or timeframe and download Excel reports for your records.</td>
                <td class="cell_width line">prominent disclosure must clearly diffrentiate between RCU and Non-NCUA products</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width ">&nbsp;</td>

            </tr>
            <tr class="hor_Line Even_row">
                <td class="cell_width line">Award-winning mobile app</td>
                <td class="cell_width line">Analytics to learn from purchases at the businesses</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width line">Ability to lease to own or purchase check scanners through RCU or purchase your own compatible devices</td>
                <td class="cell_width line">link to referral from or a Coconut meeting request would also be beneficial </td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width ">&nbsp;</td>

            </tr>
            <tr class="hor_Line Odd_row">
                <td class="cell_width line">Employee self-service</td>
                <td class="cell_width line">Loyalty and gift card programs</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width line">FINRA Rules Compliance,LPL</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width ">&nbsp;</td>

            </tr>
            <tr class="hor_Line Even_row">
                <td class="cell_width line">Same-day ACH</td>
                <td class="cell_width line">Quickbooks integration and processing </td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width ">&nbsp;</td>
            </tr>
            <tr class="hor_Line Odd_row">
                <td class="cell_width line">Robust Reporting</td>
                <td class="cell_width line">&nbsp; </td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width line">Jackie Lou for more info</td>
                <td class="cell_width line">&nbsp;</td>
                <td class="cell_width ">&nbsp;</td>
            </tr>
             <tr class="hor_Line Even_row">
                <td class="cell_width line"><a href="#">More info</a></td>
                <td class="cell_width line"><a href="#">More info</a> </td>
                <td class="cell_width line"><a href="#">More info</a></td>
                <td class="cell_width line"><a href="#">More info</a></td>
                <td class="cell_width line"><a href="#">More info</a></td>
                <td class="cell_width line"><a href="#">More info</a></td>
                <td class="cell_width "><a href="#">More info</a></td>
            </tr>
            <tr class="hor_Line Odd_row">
                <td colspan="7" class="cell_width ">
                    (Select Interested Account(s)/Services Below)
                </td>   
            </tr>
            <tr class="hor_Line Even_row">
                <td class="cell_width line"> <lightning-input type="checkbox" onchange={handleCheckboxChange1}></lightning-input></td>
                <td class="cell_width line"> <lightning-input type="checkbox" onchange={handleCheckboxChange2}></lightning-input></td>
                <td class="cell_width line"> <lightning-input type="checkbox" onchange={handleCheckboxChange3}></lightning-input></td>
                <td class="cell_width line"> <lightning-input type="checkbox" onchange={handleCheckboxChange4}></lightning-input></td>
                <td class="cell_width line"> <lightning-input type="checkbox" onchange={handleCheckboxChange5}></lightning-input></td>
                <td class="cell_width line"> <lightning-input type="checkbox" onchange={handleCheckboxChange6}></lightning-input></td>
                <td class="cell_width"> <lightning-input type="checkbox" onchange={handleCheckboxChange7}></lightning-input></td>
                
            </tr>
        </tbody>
    </table>
    
    <br>&nbsp;
    <div class="button-container">
        <button class="previous-btn" onclick={handlePreviousPage}>Back</button>
        <button class="save-continue-btn" onclick={handleNextPage}>Next</button>
    </div>
    <!-- <lightning-button label="Next" onclick={handleNextPage} variant="brand" class="slds-float_right"></lightning-button>
    <lightning-button label="Back" onclick={handlePreviousPage} class="slds-float_right" stretch style="margin-right: 1%;" size="large"></lightning-button> -->

</template>