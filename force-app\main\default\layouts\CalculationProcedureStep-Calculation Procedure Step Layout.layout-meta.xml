<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CalculationProcedureVersionId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FormulaConvertedText</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FormulaExpressionText</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FormulaUiFormattedText</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CalculationMatrixId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CalculationProcedure</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsConditionalStep</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ConditionsConvertedText</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ConditionsExpressionText</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ConditionsUiFormattedText</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>StepType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InputVariablesFormatText</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsResultIncluded</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CalculationMatrixType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OutputVariablesFormatText</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OutputVariablesMappingText</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>StageStepSequence</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Stage</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ReferenceProcedureId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ReturnMessageValueSet</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DecisionTableId</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
