table {
  width: 100%;
  Color: #333333;
  border-collapse: collapse;
  Font-size: 16px (12pt);
}
.cell_width{
  width: 20%;
  text-align: center; 
  color: #333333;
  border: none;
  padding: 10px;
}
.Table_header {
  /* background-color: #edf5e4; */
  background-color: rgb(235, 241, 233);
  font-weight: bold;
  border: none;
  font-size: 18px;
  text-align: left;
  vertical-align: top;
  padding: 10px;
  padding-top: 20px;
}
.Sub_header {
  background: #f3f3f3;
}
.Odd_row {
  background-color: #ffffff;
}
.Even_row{
  background-color: #fafafa;
}
label {
    display: flex;
    margin-bottom: 4px;
}
.horizontal_opions{
  display: flex;
  justify-content: space-around;
}
.line{
  border-right: 12px solid #ffffff;
}
.colwidth{
  border-bottom: 12px solid #ffffff;
}
.checkbox{
  color: #046a38;
}
.button-container {
  display: flex;
  justify-content: flex-end;  /* Align buttons to the right */
  gap: 20px;  /* Increase space between buttons */
  margin-top: 20px;  /* Optional: Add some top margin */
}
.previous-btn {
  padding: 20px 30px; /* Padding */
  background: #333333; /* Dark background */
  color: #ffffff; /* Text color */
  border-radius: 5px; /* Rounded corners */
  text-decoration: none; /* No underline */
  font-weight: bold; /* Bold text */
  border: none; /* Remove default border */
  cursor: pointer; /* Pointer on hover */
  transition: background 0.3s ease; /* Smooth transition for background */
}

/* Hover style for Previous button */
.previous-btn:hover {
  background: #65656A; /* Lighten background on hover */
}
.save-continue-btn {
  padding: 20px 30px; /* Padding */
  background: linear-gradient(45deg, #046a38, #84bd00); /* Gradient background */
  color: #ffffff; /* Text color */
  border-radius: 5px; /* Rounded corners */
  text-decoration: none; /* No underline */
  font-weight: bold; /* Bold text */
  border: none; /* Remove default border */
  cursor: pointer; /* Pointer on hover */
  transition: background 0.3s ease; /* Smooth transition for background */
}

/* Hover style for Save and Continue button */
.save-continue-btn:hover {
  background: #046a38; /* Solid color on hover */
}