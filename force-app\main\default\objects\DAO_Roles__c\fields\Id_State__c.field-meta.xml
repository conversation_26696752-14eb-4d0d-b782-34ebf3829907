<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Id_State__c</fullName>
    <label>Id State</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>CA</fullName>
                <default>false</default>
                <label>CA</label>
            </value>
            <value>
                <fullName>AL</fullName>
                <default>false</default>
                <label>AL</label>
            </value>
            <value>
                <fullName>AK</fullName>
                <default>false</default>
                <label>AK</label>
            </value>
            <value>
                <fullName>AZ</fullName>
                <default>false</default>
                <label>AZ</label>
            </value>
            <value>
                <fullName>AR</fullName>
                <default>false</default>
                <label>AR</label>
            </value>
            <value>
                <fullName>CO</fullName>
                <default>false</default>
                <label>CO</label>
            </value>
            <value>
                <fullName>CT</fullName>
                <default>false</default>
                <label>CT</label>
            </value>
            <value>
                <fullName>DE</fullName>
                <default>false</default>
                <label>DE</label>
            </value>
            <value>
                <fullName>FL</fullName>
                <default>false</default>
                <label>FL</label>
            </value>
            <value>
                <fullName>GA</fullName>
                <default>false</default>
                <label>GA</label>
            </value>
            <value>
                <fullName>HI</fullName>
                <default>false</default>
                <label>HI</label>
            </value>
            <value>
                <fullName>ID</fullName>
                <default>false</default>
                <label>ID</label>
            </value>
            <value>
                <fullName>IL</fullName>
                <default>false</default>
                <label>IL</label>
            </value>
            <value>
                <fullName>IN</fullName>
                <default>false</default>
                <label>IN</label>
            </value>
            <value>
                <fullName>IA</fullName>
                <default>false</default>
                <label>IA</label>
            </value>
            <value>
                <fullName>KS</fullName>
                <default>false</default>
                <label>KS</label>
            </value>
            <value>
                <fullName>KY</fullName>
                <default>false</default>
                <label>KY</label>
            </value>
            <value>
                <fullName>LA</fullName>
                <default>false</default>
                <label>LA</label>
            </value>
            <value>
                <fullName>ME</fullName>
                <default>false</default>
                <label>ME</label>
            </value>
            <value>
                <fullName>MD</fullName>
                <default>false</default>
                <label>MD</label>
            </value>
            <value>
                <fullName>MA</fullName>
                <default>false</default>
                <label>MA</label>
            </value>
            <value>
                <fullName>MI</fullName>
                <default>false</default>
                <label>MI</label>
            </value>
            <value>
                <fullName>MN</fullName>
                <default>false</default>
                <label>MN</label>
            </value>
            <value>
                <fullName>MS</fullName>
                <default>false</default>
                <label>MS</label>
            </value>
            <value>
                <fullName>MO</fullName>
                <default>false</default>
                <label>MO</label>
            </value>
            <value>
                <fullName>MT</fullName>
                <default>false</default>
                <label>MT</label>
            </value>
            <value>
                <fullName>NE</fullName>
                <default>false</default>
                <label>NE</label>
            </value>
            <value>
                <fullName>NV</fullName>
                <default>false</default>
                <label>NV</label>
            </value>
            <value>
                <fullName>NH</fullName>
                <default>false</default>
                <label>NH</label>
            </value>
            <value>
                <fullName>NJ</fullName>
                <default>false</default>
                <label>NJ</label>
            </value>
            <value>
                <fullName>NM</fullName>
                <default>false</default>
                <label>NM</label>
            </value>
            <value>
                <fullName>NY</fullName>
                <default>false</default>
                <label>NY</label>
            </value>
            <value>
                <fullName>NC</fullName>
                <default>false</default>
                <label>NC</label>
            </value>
            <value>
                <fullName>ND</fullName>
                <default>false</default>
                <label>ND</label>
            </value>
            <value>
                <fullName>OH</fullName>
                <default>false</default>
                <label>OH</label>
            </value>
            <value>
                <fullName>OK</fullName>
                <default>false</default>
                <label>OK</label>
            </value>
            <value>
                <fullName>OR</fullName>
                <default>false</default>
                <label>OR</label>
            </value>
            <value>
                <fullName>PA</fullName>
                <default>false</default>
                <label>PA</label>
            </value>
            <value>
                <fullName>RI</fullName>
                <default>false</default>
                <label>RI</label>
            </value>
            <value>
                <fullName>SC</fullName>
                <default>false</default>
                <label>SC</label>
            </value>
            <value>
                <fullName>SD</fullName>
                <default>false</default>
                <label>SD</label>
            </value>
            <value>
                <fullName>TN</fullName>
                <default>false</default>
                <label>TN</label>
            </value>
            <value>
                <fullName>TX</fullName>
                <default>false</default>
                <label>TX</label>
            </value>
            <value>
                <fullName>UT</fullName>
                <default>false</default>
                <label>UT</label>
            </value>
            <value>
                <fullName>VT</fullName>
                <default>false</default>
                <label>VT</label>
            </value>
            <value>
                <fullName>VA</fullName>
                <default>false</default>
                <label>VA</label>
            </value>
            <value>
                <fullName>WA</fullName>
                <default>false</default>
                <label>WA</label>
            </value>
            <value>
                <fullName>WV</fullName>
                <default>false</default>
                <label>WV</label>
            </value>
            <value>
                <fullName>WI</fullName>
                <default>false</default>
                <label>WI</label>
            </value>
            <value>
                <fullName>WY</fullName>
                <default>false</default>
                <label>WY</label>
            </value>
        </valueSetDefinition>
    </valueSet>
</CustomField>
