<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>InvolveCheckTypes__c</fullName>
    <label>Involve Check Types</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>MultiselectPicklist</type>
    <valueSet>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Bank</fullName>
                <default>false</default>
                <label>Bank</label>
            </value>
            <value>
                <fullName>Cashier</fullName>
                <default>false</default>
                <label>Cashier</label>
            </value>
            <value>
                <fullName>Draft</fullName>
                <default>false</default>
                <label>Draft</label>
            </value>
            <value>
                <fullName>Government-Issued</fullName>
                <default>false</default>
                <label>Government-Issued</label>
            </value>
            <value>
                <fullName>Out-of-State</fullName>
                <default>false</default>
                <label>Out-of-State</label>
            </value>
            <value>
                <fullName>Payroll</fullName>
                <default>false</default>
                <label>Payroll</label>
            </value>
            <value>
                <fullName>Personal</fullName>
                <default>false</default>
                <label>Personal</label>
            </value>
            <value>
                <fullName>Third-Party</fullName>
                <default>false</default>
                <label>Third-Party</label>
            </value>
            <value>
                <fullName>Unknown</fullName>
                <default>false</default>
                <label>Unknown</label>
            </value>
        </valueSetDefinition>
    </valueSet>
    <visibleLines>4</visibleLines>
</CustomField>
