export const OMNIDEF = {"userTimeZone":-420,"userProfile":"System Administrator","userName":"<EMAIL>","userId":"005U8000008LQSjIAO","userCurrencyCode":"USD","timeStamp":"2024-10-01T23:25:01.900Z","sOmniScriptId":"0jNU80000002JRZMA2","sobjPL":{},"RPBundle":"","rMap":{},"response":null,"propSetMap":{"currentLanguage":"en_US","scrollBehavior":"auto","disableUnloadWarn":true,"stepChartPlacement":"right","stylesheet":{"lightningRtl":"","newportRtl":"","lightning":"","newport":""},"errorMessage":{"custom":[]},"consoleTabIcon":"custom:custom18","consoleTabTitle":null,"rtpSeed":false,"showInputWidth":false,"currencyCode":"","autoFocus":false,"pubsub":false,"message":{},"ssm":false,"wpm":false,"consoleTabLabel":"New","cancelRedirectTemplateUrl":"vlcCancelled.html","cancelRedirectPageName":"OmniScriptCancelled","cancelSource":"%ContextId%","allowCancel":true,"cancelType":"SObject","visualforcePagesAvailableInPreview":{},"mergeSavedData":false,"hideStepChart":false,"timeTracking":false,"knowledgeArticleTypeQueryFieldsMap":{},"lkObjName":null,"bLK":false,"enableKnowledge":false,"trackingCustomData":{},"seedDataJSON":{},"elementTypeToHTMLTemplateMapping":{},"autoSaveOnStepNext":false,"saveURLPatterns":{},"saveObjectId":"%ContextId%","saveContentEncoded":false,"saveForLaterRedirectTemplateUrl":"vlcSaveForLaterAcknowledge.html","saveForLaterRedirectPageName":"sflRedirect","saveExpireInDays":null,"saveNameTemplate":null,"allowSaveForLater":true,"persistentComponent":[{"modalConfigurationSetting":{"modalSize":"lg","modalController":"ModalProductCtrl","modalHTMLTemplateId":"vlcProductConfig.html"},"itemsKey":"cartItems","id":"vlcCart","responseJSONNode":"","responseJSONPath":"","sendJSONNode":"","sendJSONPath":"","postTransformBundle":"","preTransformBundle":"","remoteOptions":{"postTransformBundle":"","preTransformBundle":""},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","label":"","render":false},{"modalConfigurationSetting":{"modalSize":"lg","modalController":"","modalHTMLTemplateId":""},"itemsKey":"knowledgeItems","id":"vlcKnowledge","postTransformBundle":"","preTransformBundle":"","remoteOptions":{"postTransformBundle":"","preTransformBundle":""},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","label":"","dispOutsideOmni":false,"render":false}]},"prefillJSON":"{}","lwcId":"49618d3b-365d-4740-326a-5f740239a647","labelMap":{"SSN":"PersonalInfo:personalInfoBlock:SSN","DateofBirth":"PersonalInfo:personalInfoBlock:DateofBirth","Email":"PersonalInfo:personalInfoBlock:Email","option":"NeworExistingApplication:option","formulaValidateTokenInput":"verifyPasscode:formulaValidateTokenInput","verifyTokenInput":"verifyPasscode:verifyTokenInput","TextBlock1":"verifyPasscode:TextBlock1","token":"PersonalInfo:token","personalInfoBlock":"PersonalInfo:personalInfoBlock","navigateToExistingApplication":"navigateToExistingApplication","setTokenError":"setTokenError","verifyPasscode":"verifyPasscode","sendVerificationToken":"sendVerificationToken","PersonalInfo":"PersonalInfo","NeworExistingApplication":"NeworExistingApplication"},"labelKeyMap":{},"errorMsg":"","error":"OK","dMap":{},"depSOPL":{},"depCusPL":{},"cusPL":{},"children":[{"type":"Step","propSetMap":{"businessEvent":"","businessCategory":"","pubsub":false,"message":{},"ssm":false,"wpm":false,"errorMessage":{"default":null,"custom":[]},"allowSaveForLater":false,"chartLabel":null,"instructionKey":"","HTMLTemplateId":"","conditionType":"Hide if False","show":null,"knowledgeOptions":{"typeFilter":"","remoteTimeout":30000,"dataCategoryCriteria":"","keyword":"","publishStatus":"Online","language":"English"},"remoteOptions":{},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","showPersistentComponent":[true,false],"instruction":"","completeMessage":"Are you sure you want to complete the script?","completeLabel":"Complete","saveMessage":"Are you sure you want to save it for later?","saveLabel":"Save for later","cancelMessage":"Are you sure?","cancelLabel":"Cancel","nextWidth":3,"nextLabel":"Next","previousWidth":3,"previousLabel":"Previous","validationRequired":true,"label":"Welcome","uiElements":{"NeworExistingApplication":"","option":""},"aggElements":{}},"offSet":0,"name":"NeworExistingApplication","level":0,"indexInParent":0,"bHasAttachment":false,"bEmbed":false,"response":null,"inheritShowProp":null,"children":[{"response":null,"level":1,"indexInParent":0,"eleArray":[{"type":"Radio","rootIndex":0,"response":null,"propSetMap":{"disOnTplt":false,"enableCaption":true,"imageCountInRow":3,"optionHeight":100,"optionWidth":100,"hide":false,"HTMLTemplateId":"","accessibleInFutureSteps":false,"conditionType":"Hide if False","show":null,"controllingField":{"source":"","type":"","element":""},"optionSource":{"source":"","type":""},"options":[{"autoAdv":null,"value":"Starting a new application","developerName":null,"name":"new"},{"autoAdv":null,"value":"Continuing an existing application","developerName":null,"name":"existing"}],"helpTextPos":"","helpText":"","help":false,"defaultValue":null,"horizontalMode":false,"readOnly":false,"repeatLimit":null,"repeatClone":false,"repeat":false,"required":false,"label":"Are you starting  new application or continuing an existing application?","controlWidth":12},"name":"option","level":1,"JSONPath":"NeworExistingApplication:option","indexInParent":0,"index":0,"children":[],"bHasAttachment":false,"bRadio":true,"lwcId":"lwc00-0"}],"bHasAttachment":false}],"bAccordionOpen":true,"bAccordionActive":true,"bStep":true,"isStep":true,"JSONPath":"NeworExistingApplication","lwcId":"lwc0"},{"type":"Step","propSetMap":{"businessEvent":"","businessCategory":"","pubsub":false,"message":{},"ssm":false,"wpm":false,"errorMessage":{"default":null,"custom":[]},"allowSaveForLater":true,"chartLabel":null,"instructionKey":"","HTMLTemplateId":"","conditionType":"Hide if False","show":null,"knowledgeOptions":{"typeFilter":"","remoteTimeout":30000,"dataCategoryCriteria":"","keyword":"","publishStatus":"Online","language":"English"},"remoteOptions":{},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","showPersistentComponent":[true,false],"instruction":"","completeMessage":"Are you sure you want to complete the script?","completeLabel":"Complete","saveMessage":"Are you sure you want to save it for later?","saveLabel":"Save for later","cancelMessage":"Are you sure?","cancelLabel":"Cancel","nextWidth":3,"nextLabel":"Next","previousWidth":3,"previousLabel":"Previous","validationRequired":true,"label":"Personal Information","uiElements":{"PersonalInfo":"","Email":"","DateofBirth":"","SSN":"","personalInfoBlock":""},"aggElements":{"token":""}},"offSet":0,"name":"PersonalInfo","level":0,"indexInParent":1,"bHasAttachment":false,"bEmbed":false,"response":null,"inheritShowProp":null,"children":[{"response":null,"level":1,"indexInParent":0,"eleArray":[{"type":"Block","rootIndex":1,"response":null,"propSetMap":{"bus":true,"hide":false,"HTMLTemplateId":"","accessibleInFutureSteps":false,"conditionType":"Hide if False","show":null,"repeatLimit":null,"repeatClone":false,"repeat":false,"collapse":false,"label":"Please enter the following information","controlWidth":12},"name":"personalInfoBlock","level":1,"JSONPath":"PersonalInfo:personalInfoBlock","indexInParent":0,"index":0,"children":[{"response":null,"level":2,"indexInParent":0,"eleArray":[{"type":"Text","rootIndex":1,"response":null,"propSetMap":{"autocomplete":null,"disOnTplt":false,"hide":false,"HTMLTemplateId":"","debounceValue":0,"accessibleInFutureSteps":false,"conditionType":"Hide if False","show":null,"placeholder":"","maxLength":255,"minLength":0,"ptrnErrText":"","pattern":"","mask":"","helpTextPos":"","helpText":"","help":false,"defaultValue":null,"readOnly":false,"repeatLimit":null,"repeatClone":false,"repeat":false,"required":false,"inputWidth":12,"showInputWidth":false,"label":"Email","controlWidth":12},"name":"Email","level":2,"JSONPath":"PersonalInfo:personalInfoBlock|1:Email","indexInParent":0,"index":0,"children":[],"bHasAttachment":false,"bText":true,"lwcId":"lwc1000-0"}],"bHasAttachment":false},{"response":null,"level":2,"indexInParent":1,"eleArray":[{"type":"Date","rootIndex":1,"response":null,"propSetMap":{"maxDate":"","minDate":"","disOnTplt":false,"hide":false,"HTMLTemplateId":"","accessibleInFutureSteps":false,"conditionType":"Hide if False","show":null,"dateFormat":"yyyy-MM-dd","modelDateFormat":"yyyy-MM-dd","dateType":"string","helpTextPos":"","helpText":"","help":false,"defaultValue":null,"readOnly":false,"repeatLimit":null,"repeatClone":false,"repeat":false,"required":false,"inputWidth":12,"showInputWidth":false,"label":"Date of Birth","controlWidth":12},"name":"DateofBirth","level":2,"JSONPath":"PersonalInfo:personalInfoBlock|1:DateofBirth","indexInParent":1,"index":0,"children":[],"bHasAttachment":false,"bDate":true,"lwcId":"lwc1001-0"}],"bHasAttachment":false},{"response":null,"level":2,"indexInParent":2,"eleArray":[{"type":"Text","rootIndex":1,"response":null,"propSetMap":{"autocomplete":null,"disOnTplt":false,"hide":false,"HTMLTemplateId":"","debounceValue":0,"accessibleInFutureSteps":false,"conditionType":"Hide if False","show":null,"placeholder":"","maxLength":255,"minLength":0,"ptrnErrText":"","pattern":"","mask":"","helpTextPos":"","helpText":"","help":false,"defaultValue":null,"readOnly":false,"repeatLimit":null,"repeatClone":false,"repeat":false,"required":false,"inputWidth":12,"showInputWidth":false,"label":"Last 4 of your SSN","controlWidth":12},"name":"SSN","level":2,"JSONPath":"PersonalInfo:personalInfoBlock|1:SSN","indexInParent":2,"index":0,"children":[],"bHasAttachment":false,"bText":true,"lwcId":"lwc1002-0"}],"bHasAttachment":false}],"bHasAttachment":false,"bBlock":true,"lwcId":"lwc10-0"}],"bHasAttachment":false},{"response":null,"level":1,"indexInParent":1,"eleArray":[{"type":"Formula","rootIndex":1,"response":null,"propSetMap":{"disOnTplt":false,"HTMLTemplateId":"","dateFormat":"MM-dd-yyyy","hideGroupSep":false,"dataType":null,"mask":null,"show":null,"hide":true,"expression":"ROUND(RANDOM(4)*100000)","inputWidth":12,"showInputWidth":false,"label":"token","controlWidth":12},"name":"token","level":1,"JSONPath":"PersonalInfo:token","indexInParent":1,"index":0,"children":[],"bHasAttachment":false,"bFormula":true,"lwcId":"lwc11-0"}],"bHasAttachment":false}],"bAccordionOpen":false,"bAccordionActive":false,"bStep":true,"isStep":true,"JSONPath":"PersonalInfo","lwcId":"lwc1"},{"type":"Email Action","propSetMap":{"businessEvent":"","businessCategory":"","enableActionMessage":false,"enableDefaultAbort":false,"errorMessage":{"default":null,"custom":[]},"pubsub":false,"message":{},"ssm":false,"wpm":false,"HTMLTemplateId":"","show":null,"showPersistentComponent":[true,false],"redirectPreviousWidth":3,"redirectPreviousLabel":"Previous","redirectNextWidth":3,"redirectNextLabel":"Next","redirectTemplateUrl":"vlcAcknowledge.html","redirectPageName":"","validationRequired":"Step","failureAbortMessage":"Are you sure?","failureGoBackLabel":"Go Back","failureAbortLabel":"Abort","failureNextLabel":"Continue","postMessage":"Done","inProgressMessage":"In Progress","remoteTimeout":30000,"docList":"","staticDocList":[],"contentVersionList":"","attachmentList":"","fileAttachments":"","OrgWideEmailAddress":"<EMAIL>","emailInformation":{"setHtmlBody":true,"emailBody":"<body style=\"font-family: Arial, sans-serif; background-color: #f4f4f4; margin: 0; padding: 0;\">\n\n    <div style=\"background-color: #ffffff; padding: 20px; margin: 20px auto; max-width: 600px; border-radius: 8px; box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\">\n        <h1 style=\"color: #333333;\">Two-Factor Authentication (2FA) Required</h1>\n        <p>Hello, %PersonalInfo:personalInfoBlock:Email%</p>\n        <p>To continue accessing your account, please enter the security token below as part of the Two-Factor Authentication process. This helps us keep your account safe and secure.</p>\n    \n        <div style=\"background-color: #e8f4ff; padding: 15px; margin: 20px 0; border-left: 4px solid #007bff; font-size: 24px; text-align: center; font-weight: bold; letter-spacing: 2px; color: #333333;\">\n            <!-- Insert the dynamically generated token here -->\n            %PersonalInfo:token%\n        </div>\n    \n        <p> If you did not request this token, please disregard this email.</p>\n    \n    \n        <p style=\"text-align: center; color: #999999; font-size: 12px; margin-top: 30px;\">If you need any assistance, please contact our support team at <a href=\"mailto:<EMAIL>\"><EMAIL></a>.</p>\n    </div>\n\n</body>","emailSubject":"Verification Token","bccAddressList":[],"ccAddressList":[],"toAddressList":["%PersonalInfo:personalInfoBlock:Email%"]},"emailTemplateInformation":{"whatId":"","saveAsActivity":false,"emailTargetObjectId":"","emailTemplateName":""},"useTemplate":false,"label":"sendVerificationToken","controlWidth":12,"aggElements":{}},"offSet":0,"name":"sendVerificationToken","level":0,"indexInParent":2,"bHasAttachment":false,"bEmbed":false,"bEmailAction":true,"JSONPath":"sendVerificationToken","lwcId":"lwc2"},{"type":"Step","propSetMap":{"businessEvent":"","businessCategory":"","pubsub":false,"message":{},"ssm":false,"wpm":false,"errorMessage":{"default":null,"custom":[]},"allowSaveForLater":false,"chartLabel":null,"instructionKey":"","HTMLTemplateId":"","conditionType":"Hide if False","show":null,"knowledgeOptions":{"typeFilter":"","remoteTimeout":30000,"dataCategoryCriteria":"","keyword":"","publishStatus":"Online","language":"English"},"remoteOptions":{},"remoteTimeout":30000,"remoteMethod":"","remoteClass":"","showPersistentComponent":[true,false],"instruction":"","completeMessage":"Are you sure you want to complete the script?","completeLabel":"Complete","saveMessage":"Are you sure you want to save it for later?","saveLabel":"Save for later","cancelMessage":"Are you sure?","cancelLabel":"Cancel","nextWidth":3,"nextLabel":"Next","previousWidth":3,"previousLabel":"Previous","validationRequired":true,"label":"Verify Passcode","uiElements":{"verifyPasscode":"","verifyTokenInput":""},"aggElements":{"formulaValidateTokenInput":""}},"offSet":0,"name":"verifyPasscode","level":0,"indexInParent":3,"bHasAttachment":false,"bEmbed":false,"response":null,"inheritShowProp":null,"children":[{"response":null,"level":1,"indexInParent":0,"eleArray":[{"type":"Text Block","rootIndex":3,"response":null,"propSetMap":{"sanitize":false,"textKey":"","HTMLTemplateId":"","dataJSON":false,"show":null,"text":"<h4><span style=\"color: #236fa1;\">Please enter the code sent to your email, if you have not received it please go back and re enter your personal information</span></h4>","label":"TextBlock1","controlWidth":12},"name":"TextBlock1","level":1,"JSONPath":"verifyPasscode:TextBlock1","indexInParent":0,"index":0,"children":[],"bHasAttachment":false,"bTextBlock":true,"lwcId":"lwc30-0"}],"bHasAttachment":false},{"response":null,"level":1,"indexInParent":1,"eleArray":[{"type":"Text","rootIndex":3,"response":null,"propSetMap":{"autocomplete":null,"disOnTplt":false,"hide":false,"HTMLTemplateId":"","debounceValue":0,"accessibleInFutureSteps":false,"conditionType":"Hide if False","show":null,"placeholder":"","maxLength":255,"minLength":0,"ptrnErrText":"","pattern":"","mask":"","helpTextPos":"","helpText":"","help":false,"defaultValue":null,"readOnly":false,"repeatLimit":null,"repeatClone":false,"repeat":false,"required":false,"inputWidth":12,"showInputWidth":false,"label":"","controlWidth":12},"name":"verifyTokenInput","level":1,"JSONPath":"verifyPasscode:verifyTokenInput","indexInParent":1,"index":0,"children":[],"bHasAttachment":false,"bText":true,"lwcId":"lwc31-0"}],"bHasAttachment":false},{"response":null,"level":1,"indexInParent":2,"eleArray":[{"type":"Formula","rootIndex":3,"response":null,"propSetMap":{"disOnTplt":false,"HTMLTemplateId":"","dateFormat":"MM-dd-yyyy","hideGroupSep":false,"dataType":null,"mask":null,"show":null,"hide":true,"expression":"IF( %PersonalInfo:token% == %verifyPasscode:verifyTokenInput%, true, false)","inputWidth":12,"showInputWidth":false,"label":"formulaValidateTokenInput","controlWidth":12},"name":"formulaValidateTokenInput","level":1,"JSONPath":"verifyPasscode:formulaValidateTokenInput","indexInParent":2,"index":0,"children":[],"bHasAttachment":false,"bFormula":true,"lwcId":"lwc32-0"}],"bHasAttachment":false}],"bAccordionOpen":false,"bAccordionActive":false,"bStep":true,"isStep":true,"JSONPath":"verifyPasscode","lwcId":"lwc3"},{"type":"Set Errors","propSetMap":{"pubsub":false,"message":{},"ssm":false,"wpm":false,"HTMLTemplateId":"","show":{"group":{"rules":[{"field":"formulaValidateTokenInput","condition":"=","data":"false"}],"operator":"AND"}},"showPersistentComponent":[true,false],"elementErrorMap":{"verifyTokenInput":"Oops, the token entered is not valid."},"validationRequired":"Step","label":"setTokenError","controlWidth":12,"aggElements":{}},"offSet":0,"name":"setTokenError","level":0,"indexInParent":4,"bHasAttachment":false,"bEmbed":false,"bSetErrors":true,"JSONPath":"setTokenError","lwcId":"lwc4"},{"type":"Navigate Action","propSetMap":{"targetParams":"https://rcu2022--devzennify--omnistudio.sandbox.vf.force.com/lightning/cmp/omnistudio__vlocityLWCOmniWrapper?c__target=c:rcuApplicationEnglish&c__layout=lightning&c__instanceId=0kTU80000000pVZMAY","targetName":"omnistudio__vlocityLWCOmniWrapper","targetLWCParams":"","targetLWC":"c:rcuApplicationEnglish","targetUrl":"https://rcu2022--devzennify--omnistudio.sandbox.vf.force.com/lightning/cmp/omnistudio__vlocityLWCOmniWrapper?c__target=c:rcuApplicationEnglish&c__layout=lightning&c__instanceId=0kTU80000000pVZMAY","businessEvent":"","businessCategory":"","targetLWCLayout":"lightning","replace":true,"iconPosition":"left","iconVariant":"","iconName":"","variant":"brand","targetId":"%ContextId%","targetFilter":"Recent","loginAction":"login","recordAction":"view","objectAction":"home","targetType":"Web Page","message":{},"pubsub":false,"ssm":false,"wpm":false,"HTMLTemplateId":"","show":null,"validationRequired":"Submit","label":"navigateToExistingApplication","controlWidth":12,"aggElements":{}},"offSet":0,"name":"navigateToExistingApplication","level":0,"indexInParent":5,"bHasAttachment":false,"bEmbed":false,"bNavigate":true,"JSONPath":"navigateToExistingApplication","lwcId":"lwc5"}],"bReusable":false,"bpVersion":5,"bpType":"rcu","bpSubType":"prototype","bpLang":"English","bHasAttachment":false,"lwcVarMap":{}};