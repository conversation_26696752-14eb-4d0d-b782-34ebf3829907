import { LightningElement, wire } from 'lwc';
import { NavigationMixin } from 'lightning/navigation';
import { CurrentPageReference } from 'lightning/navigation';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';

export default class CloseTab extends NavigationMixin(LightningElement) {
    @wire(CurrentPageReference) pageRef;

    connectedCallback() {
        this.closeCurrentTab();
    }

    closeCurrentTab() {
        if (this.pageRef) {
            this[NavigationMixin.Navigate]({
                type: 'standard__navItemPage',
                attributes: {
                    apiName: 'Home'
                }
            });
        } else if (this.pageRef) {
            // Logic to close the tab for record pages
            const closeTabEvent = new CustomEvent('close');
            this.dispatchEvent(closeTabEvent);
        } else {
            const event = new ShowToastEvent({
                title: 'Error',
                message: 'Cannot close this type of tab.',
                variant: 'error'
            });
            this.dispatchEvent(event);
        }
    }
}