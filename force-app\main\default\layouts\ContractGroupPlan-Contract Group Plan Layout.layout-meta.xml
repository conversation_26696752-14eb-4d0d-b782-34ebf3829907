<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>ContractId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EffectiveStartDate</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EffectiveEndDate</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Enrollment Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EnrollmentStartDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EnrollmentRatingType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EligibilityCriteria</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EnrollmentEndDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EnrollmentWaitingPeriod</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Additional Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ProductNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>QuoteLineItemId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>UnitPrice</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OriginalContractPlanId</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>GroupNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ContractGroupParentPlanId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PricingStructure</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>Name</fields>
        <fields>NameInsured</fields>
        <fields>PolicyName</fields>
        <fields>PremiumAmount</fields>
        <fields>UniversalPolicyNumber</fields>
        <relatedList>InsurancePolicies</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
