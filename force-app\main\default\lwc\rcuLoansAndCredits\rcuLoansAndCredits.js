import { LightningElement,api} from 'lwc';
import {OmniscriptBaseMixin} from 'omnistudio/omniscriptBaseMixin';
export default class RcuLoansAndCredits extends OmniscriptBaseMixin(LightningElement) {
    // linesOfCredit=false;
    // termLoan =false;
    // sbaLoan=false;
    // commercialRealState =false;
    // connectedCallback() {
    //     console.log("omniscript data",this.omniJsonData);
    //     console.log("expnad your business = ",this.omniJsonData.expandyourbusiness)
    //     if(this.omniJsonData.expandyourbusiness || this.omniJsonData.marketingexpense || this.omniJsonData.managecashflow)
    //     {
    //         this.linesOfCredit=true;
    //         console.log("Lines of credit = ",this.linesOfCredit)
    //     }
    //     else
    //     {
    //         this.linesOfCredit=false;
    //         console.log("Lines of credit = ",this.linesOfCredit)
    //     }
    //     if(this.omniJsonData.purchasevehicle || this.omniJsonData.purchaseequipment)
    //     {
    //          this.termLoan=true;
    //          console.log("Term Loan = ",this.termLoan)
    //     }else
    //     {
    //         this.termLoan=false;
    //          console.log("Term Loan = ",this.termLoan)
    //     }
    //     this.sbaLoan=this.omniJsonData.startupcost;
    //      console.log("SBA Loan = ",this.sbaLoan)
    //     this.commercialRealState=this.omniJsonData.buybuilding;
    //      console.log("Commercial Real State = ",this.commercialRealState)
    // }


handleCheckboxChange1(event) {
    console.log('Term Loan checkbox changed:', event.target.checked);
   
}

handleCheckboxChange2(event) {
}

handleCheckboxChange3(event) {
}

handleCheckboxChange4(event) {
}


    
    handleNextPage() {
            this.omniNextStep(); // Move to next step
        }
    
        // Check if Choice is selected and fields are filled
    handlePreviousPage(){
        this.omniPrevStep();
    }
}