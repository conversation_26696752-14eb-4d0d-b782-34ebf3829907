<!--
  @description       : 
  <AUTHOR> Zennify
  @last modified on  : 05-08-2025
  @last modified by  : <PERSON><PERSON><PERSON>
-->
<template>

    <div class="slds-m-around_medium">
        <lightning-button-icon
            icon-name="utility:attach"
            alternative-text="Attach File"
            class="slds-icon-text-default"
            onclick={handleClick}
        ></lightning-button-icon>
    </div>

    <button class="slds-button" alternative-text="Attach-File" onclick={handleClick}></button>

    <template if:true={isModalOpen}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">Attach File</h2>
                </header>
                <div class="slds-modal__content slds-p-around_medium">
                    <p style="font-size: 20px;"><b>Please Select the document type and enter a description</b></p>
                    </br>
                    <!-- Document Type -->
                    <lightning-combobox
                        name="firstPicklist"
                        label="Document Type"
                        required = "true"
                        value={firstPicklistValue}
                        placeholder="Choose an option"
                        options={documentTypeOptions}
                        onchange={handlePicklistChange}
                    ></lightning-combobox>

                    <!-- Description -->
                    <lightning-input
                        type="text"
                        required = "true"
                        label="Document Description"
                        value={description}
                        onchange={handleDescriptionChange}
                        class="slds-m-top_medium"
                    ></lightning-input>
                    
                    <!-- Role -->
                    
                    <lightning-combobox
                        style="display: none;"
                        name="secondPicklist"
                        label="Role"
                        required = "true"
                        if:true={isId}
                        value={secondPicklistValue}
                        placeholder="Choose an option"
                        options={rolePicklistValues}
                        onchange={handlePicklistChange}
                        class="slds-m-top_medium"
                    ></lightning-combobox> 
                    

                    <template if:true={showSelectRoleDropdowns}>
                    <lightning-combobox
                        name="rolesPicklistOptions"
                        label="Select Role"
                        required="true"
                        if:true={isId}
                        value={selectedRole}
                        placeholder="Choose a role"
                        options={rolesPicklistOptions}
                        onchange={handleRolePicklistChange}
                        class="slds-m-top_medium"
                    ></lightning-combobox>
                    </template>
                    
                    <!-- File Upload -->
                    <lightning-file-upload 
                        name="fileUploader"
                        label="Upload Files"
                        record-id={applicationId}
                        accept={acceptedFormats}
                        onuploadfinished={handleUploadFinished}
                        class="slds-m-top_medium"
                    ></lightning-file-upload>

                    <!-- Upload Button -->
                    <div class="slds-m-top_medium slds-text-align_center">
                        <lightning-button
                            label="Submit"
                            variant="brand"
                            class="slds-button_success"
                            onclick={handleSubmit}
                            disabled={hasError}
                        ></lightning-button>
                    </div>
                
                    </br>
                    </br>
                
                    <!-- Display the uploaded files -->
                    <template if:true={documentsDataArray.length}>
                        <lightning-datatable
                            key-field="Id"
                            key={uniqueKeyId}
                            data={documentsDataArray}
                            columns={documentColumns}
                            onrowaction={handleRowAction}
                        ></lightning-datatable>
                    </template>
                </div>

                <footer class="slds-modal__footer">
                    <lightning-button label="Close" onclick={closeModal}></lightning-button>
                </footer>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
</template>