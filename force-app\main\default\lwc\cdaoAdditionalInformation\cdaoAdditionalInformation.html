<template>
    <div class="additionalInfo-form-container">
            <h1 class="headline">{LABELS.AddITIONAL_INFO_HEADLINE}</h1>
            <p class="subtext">{LABELS.AddITIONAL_INFO_SUBTEXT}</p>
            <!-- Verbal Account Password Section -->
            <h2><strong>{LABELS.VERBAL_PASSWORD_HEADING}</strong> </h2><br>
            <section class="box-section">
                    <p>
                            {LABELS.VERBAL_PASSWORD_INFO}<br />
                            <strong>{LABELS.VERBAL_PASSWORD_WARNING}</strong>
                    </p>
                    <div class="input-group">
                            <div class="input-box">
                                    <lightning-input label={LABELS.VERBAL_PASSWORD_LABEL}
                                            value={Verbal_Password_Value} type="text" placeholder=""
                                            onchange={handleInput}></lightning-input>
                            </div>
                            <div class="input-box">
                                    <lightning-input label={LABELS.VERBAL_PASSWORD_HINT_LABEL}
                                            value={Verbal_Password_Hint_Value} type="text" placeholder=""
                                            onchange={handleInput}></lightning-input>
                            </div>
                    </div>
            </section>
            <!--Personal Information-->
                    <h2><strong> {LABELS.PERSONAL_INFO_HEADING}</strong></h2><br>
                    <section class="box-section">
                            <div class="input-group">
                                <template if:true={isCCSelected}>
                                    <div class="input-box">
                                            <lightning-combobox label={LABELS.HOUSING_STATUS_LABEL}
                                                    options={OPTIONS.HOUSING_STATUS} placeholder="Select"
                                                    value={Housing_Status_Value} onchange={handleInput}>
                                            </lightning-combobox>
                                    </div>
                                </template>
                                    <div class="input-box">
                                        <lightning-combobox label={LABELS.COLLEGE_STUDENT_LABEL}
                                                options={OPTIONS.COLLEGE_STUDENT} placeholder="Select"
                                                value={College_Student_Value} onchange={handleInput}>
                                        </lightning-combobox>
                                </div>
                            </div>
                    </section>
            <div class="button-containerr">
                    <lightning-button variant="success" label="Save and Continue" onclick={handleNext}
                            disabled={isSaveDisabled}>
                    </lightning-button>
                    <lightning-button variant="inverse" label="Go Back" onclick={handleBack}>
                    </lightning-button>
            </div>
    </div>
</template>