/**
 * @description       : Provide test coverage for DAORemoteQueueable class
 * <AUTHOR> Zennify
 * @last modified on  : 06-13-2025
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
@isTest
public class DAORemoteQueueableTest {
    @isTest
    static void testQueueableAssignsPermissionSets() {
        // Create a test user
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        User u = new User(
            FirstName = 'Test',
            LastName = 'User',
            Email = '<EMAIL>',
            Username = 'testuser' + System.currentTimeMillis() + '@zennify.com',
            Alias = 'tuser',
            TimeZoneSidKey = 'America/New_York',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            ProfileId = p.Id
        );
        insert u;

        // Create a test permission set
        PermissionSet ps = new PermissionSet(Name = 'Test_Permission_Set', Label = 'Test Permission Set');
        insert ps;

        List<String> permSetNames = new List<String>{ps.Name};
        Test.startTest();
        System.enqueueJob(new DAORemoteQueueable(u.Id, permSetNames));
        Test.stopTest();

        // Verify assignment
        List<PermissionSetAssignment> psa = [SELECT Id FROM PermissionSetAssignment WHERE AssigneeId = :u.Id AND PermissionSetId = :ps.Id];
        System.assertEquals(1, psa.size(), 'User should have the permission set assigned');
    }
}