<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Dev_Stage_Change</name>
        <label>Dev Stage Change</label>
        <locationX>182</locationX>
        <locationY>431</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>Is_loan_closing</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <stringValue><EMAIL>,<EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>Dev Stage Change</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>LeadStageInformation</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <description>Send congratulations email</description>
        <name>Send_congratulations_email</name>
        <label>Send congratulations email</label>
        <locationX>50</locationX>
        <locationY>647</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>$Record.Empower_Lead__r.Owner:User.Email</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <elementReference>congratzEmailSubject</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>congratzEmailBody</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Send_fault_email</name>
        <label>Send fault email</label>
        <locationX>578</locationX>
        <locationY>431</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <stringValue><EMAIL>,<EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>A fault has occurred</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <stringValue>A fault has occurred</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>sendRichBody</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <description>Is loan closing</description>
        <name>Is_loan_closing</name>
        <label>Is loan closing</label>
        <locationX>182</locationX>
        <locationY>539</locationY>
        <defaultConnectorLabel>Loan in other stage</defaultConnectorLabel>
        <rules>
            <name>Loan_is_closing</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.StageName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Closing</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Send_congratulations_email</targetReference>
            </connector>
            <label>Loan is closing</label>
        </rules>
    </decisions>
    <description>Mortgage update lead with empower stage from opportunity</description>
    <environments>Default</environments>
    <interviewLabel>Mortgage update lead with empower stage from opportunity {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Mortgage update lead with empower stage from opportunity</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordUpdates>
        <description>Update lead record with stage from opportunity</description>
        <name>Update_lead_record_with_stage_from_opportunity</name>
        <label>Update lead record with stage from opportunity</label>
        <locationX>182</locationX>
        <locationY>323</locationY>
        <connector>
            <targetReference>Dev_Stage_Change</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Send_fault_email</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Empower_Opportunity__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Application_Date__c</field>
            <value>
                <elementReference>$Record.Application_Submitted_Date__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Empower_Stage__c</field>
            <value>
                <elementReference>$Record.StageName</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Loan_Funded_Date__c</field>
            <value>
                <elementReference>$Record.Funded_Date__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Loan_Number__c</field>
            <value>
                <elementReference>$Record.BK_External_ID__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Mortgage_Application_Decision__c</field>
            <value>
                <stringValue>Yes</stringValue>
            </value>
        </inputAssignments>
        <object>Lead</object>
    </recordUpdates>
    <start>
        <locationX>56</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Update_lead_record_with_stage_from_opportunity</targetReference>
        </connector>
        <filterFormula>AND(
    NOT(ISBLANK({!$Record.Empower_Lead__c})), 
    ISCHANGED({!$Record.StageName}),
    {!$Record.RecordType.Name} = &quot;Mortgage Opportunity&quot;
)</filterFormula>
        <object>Opportunity</object>
        <recordTriggerType>Update</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <textTemplates>
        <description>The body of the congratulations email to loan officer</description>
        <name>congratzEmailBody</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Hello {!$Record.Empower_Lead__r.FirstName}, 
We received notification from Empower that the Loan for {!$Record.Empower_Lead__r.Name} has been funded.

Congratulations!</text>
    </textTemplates>
    <textTemplates>
        <description>The subject of the congratulations email sent to the loan officer</description>
        <name>congratzEmailSubject</name>
        <isViewedAsPlainText>true</isViewedAsPlainText>
        <text>Hooray 🎈 Loan Funded for {!$Record.Empower_Lead__r.Name}!</text>
    </textTemplates>
    <textTemplates>
        <name>LeadStageInformation</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>The stage changed for &apos;{!$Record.Name} {!$Record}&apos; to &apos;{!$Record.StageName}&apos;.</text>
    </textTemplates>
</Flow>
