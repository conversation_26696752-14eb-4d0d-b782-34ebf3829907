<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>PolicyParticipantMasterDetails</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>InsurancePolicyId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Role</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RelationshipToInsured</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BeneficiarySharePercentage</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EffectiveDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpirationDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsActiveParticipant</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TermFeeAmount</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PrimaryParticipantAccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PrimaryParticipantContactId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LegalGuardianId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsMinorBeneficiary</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TotalTermAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TermPremiumAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TermTaxAmount</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
