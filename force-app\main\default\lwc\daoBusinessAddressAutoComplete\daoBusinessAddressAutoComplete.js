import { LightningElement, api } from "lwc";
import { OmniscriptBaseMixin } from "omnistudio/omniscriptBaseMixin";
import getAddress from "@salesforce/apex/DAOAddressAutoComplete.getAddress";


export default class DaoBusinessAddressAutoComplete extends OmniscriptBaseMixin(
    LightningElement
) {
    name = "";
    phone = "";
    website = "";
    accountNumber = "";
    fullAddress = "";
    doneTypingInterval = 300;
    typingTimer;
    suggestion;
    city = "";
    state = "";
    postalCode = "";
    street = "";
    showAddressForm = false;
    isBusinessPhysicalAddressSet;
    isBusinessMailingAddressSet;

    @api omniScript;
    @api screentype;
  

    connectedCallback() {
        if (this.screentype == "businessPhysicalAddress") {
            console.log(" This is businessPhysicalAddress screen");
            this.street = this.omniJsonData.rolestreetAddress;
            this.city = this.omniJsonData.rolecityAddress;
            this.state = this.omniJsonData.rolestateAddress;
            this.postalCode = this.omniJsonData.rolezipcode;
            this.fullAddress = this.omniJsonData.rolephysicalFullAddres;
            this.isBusinessPhysicalAddressSet = this.omniJsonData.isBusinessPhysicalAddressSet;
        } else if (this.screentype == "businessMailingAddress") {
            console.log("This is businessMailingAddress screen");
            this.street = this.omniJsonData.rolemailingStreet;
            this.city = this.omniJsonData.rolemailingCity;
            this.state = this.omniJsonData.rolemailingState;
            this.postalCode = this.omniJsonData.rolemailingZipcode;
            this.fullAddress = this.omniJsonData.rolemailingfullAddress;
            this.isBusinessMailingAddressSet = this.omniJsonData.isBusinessMailingAddressSet;
        } 
        const state = this.omniGetSaveState();
        if (state) this.value = state;
    }

    handleInputChange(event) {
        clearTimeout(this.typingTimer);
        let value = event.target.value;
        this.fullAddress = value;

        // Validation: set isSet to false if address is empty
        if (!value || value.trim() === "") {
            if(this.screentype == "businessPhysicalAddress"){
                this.isBusinessPhysicalAddressSet = false;
            } else if(this.screentype == "businessMailingAddress"){
                this.isBusinessMailingAddressSet = false;
            }
        } else {
            if(this.screentype == "businessPhysicalAddress"){
                this.isBusinessPhysicalAddressSet = true;
            } else if(this.screentype == "businessMailingAddress"){
                this.isBusinessMailingAddressSet = true;
            }
        }
       
        this.typingTimer = setTimeout(() => {
            if (value) {
                getAddress({ search: value })
                    .then((result) => {
                        let temp = JSON.parse(result);
                        let suggestionList = [];
                        temp.suggestions.forEach((elem) => {
                            let address =
                                "" +
                                elem.street_line +
                                "," +
                                elem.city +
                                "," +
                                elem.state +
                                "," +
                                elem.zipcode;

                            suggestionList.push({ id: Date.now(), value: address });
                        });
                        this.suggestion = suggestionList;
                    })
                    .catch((error) => {
                        console.log("## error in creating records: " + error);
                    });
            }
        }, this.doneTypingInterval);

        let dataToPass;
        if (this.screentype == "businessPhysicalAddress") {
            dataToPass = {
                rolestreetAddress: this.street,
                rolecityAddress: this.city,
                rolestateAddress: this.state,
                rolezipcode: this.postalCode,
                rolephysicalFullAddres: this.fullAddress,
                isBusinessPhysicalAddressSet: this.isBusinessPhysicalAddressSet,
                isBusinessMailingAddressSet: this.isBusinessMailingAddressSet,
            };
        } else if (this.screentype == "businessMailingAddress") {
            dataToPass = {
                rolemailingStreet: this.street,
                rolemailingCity: this.city,
                rolemailingState: this.state,
                rolemailingZipcode: this.postalCode,
                rolemailingfullAddress: this.fullAddress,
                isBusinessMailingAddressSet: this.isBusinessMailingAddressSet,
            };
        } 
        this.omniApplyCallResp(dataToPass);
    }

    setAddress(event) {
        let placeId = event.currentTarget.dataset.value.split(",");
        let key = event.currentTarget.dataset.key;
        this.suggestion = undefined;
        this.street = placeId.length > 0 ? placeId[0] : "";
        this.city = placeId.length > 1 ? placeId[1] : "";
        this.state = placeId.length > 2 ? placeId[2] : "";
        this.postalCode = placeId.length > 3 ? placeId[3] : "";
        this.country = placeId.length > 4 ? placeId[4] : "";
        this.fullAddress =
            this.street +
            " " +
            this.city +
            " " +
            this.state +
            " " +
            this.postalCode +
            " " +
            "US";
        // Validation: set isSet to true if address is filled, false otherwise
        if (!this.fullAddress || this.fullAddress.trim() === "") {
            this.isBusinessPhysicalAddressSet = false;
            this.isBusinessMailingAddressSet = false;
        } else {
            this.isBusinessPhysicalAddressSet = true;
            this.isBusinessMailingAddressSet = true;
        }
        this.omniSaveState({
            Address: placeId,
        });

        this.omniUpdateDataJson({
            Address: placeId,
        });
        let dataToPass;
        if (this.screentype == "businessPhysicalAddress") {
            dataToPass = {
                rolestreetAddress: this.street,
                rolecityAddress: this.city,
                rolestateAddress: this.state,
                rolezipcode: this.postalCode,
                rolephysicalFullAddres: this.fullAddress,
                isBusinessPhysicalAddressSet: this.isBusinessPhysicalAddressSet,
            };
        } else if (this.screentype == "businessMailingAddress") {
            dataToPass = {
                rolemailingStreet: this.street,
                rolemailingCity: this.city,
                rolemailingState: this.state,
                rolemailingZipcode: this.postalCode,
                rolemailingfullAddress: this.fullAddress,
                isBusinessMailingAddressSet: this.isBusinessMailingAddressSet,
            };
        } 

        this.omniApplyCallResp(dataToPass);
    }
    handlePencilClick() {
        this.showAddressForm = !this.showAddressForm; // Toggle the visibility
    }

    handleAddressChange(event) {
        // Extract address details from event.detail
        const { street, city, province, postalCode } = event.detail;
        this.street = street || "";
        this.city = city || "";
        this.state = province || "";
        this.postalCode = postalCode || "";

        // Compose fullAddress for validation
        this.fullAddress = `${this.street} ${this.city} ${this.state} ${this.postalCode} US`;

        // Validation: set isSet to false if all address fields are empty
        if (
            (!this.street || this.street.trim() === "") ||
            (!this.city || this.city.trim() === "") ||
            (!this.state || this.state.trim() === "") ||
            (!this.postalCode || this.postalCode.trim() === "")
        ) {
            if(this.screentype == "businessPhysicalAddress"){
                this.isBusinessPhysicalAddressSet = false;
            } else if(this.screentype == "businessMailingAddress"){
                this.isBusinessMailingAddressSet = false;
            }
        } else {
            if(this.screentype == "businessPhysicalAddress"){
                this.isBusinessPhysicalAddressSet = true;
            } else if(this.screentype == "businessMailingAddress"){
                this.isBusinessMailingAddressSet = true;
            }
        }

        // Update the OmniScript data
        let dataToPass;
        if (this.screentype == "businessPhysicalAddress") {
            dataToPass = {
                rolestreetAddress: this.street,
                rolecityAddress: this.city,
                rolestateAddress: this.state,
                rolezipcode: this.postalCode,
                rolephysicalFullAddres: this.fullAddress,
                isBusinessPhysicalAddressSet: this.isBusinessPhysicalAddressSet,
            };
        } else if (this.screentype == "businessMailingAddress") {
            dataToPass = {
                rolemailingStreet: this.street,
                rolemailingCity: this.city,
                rolemailingState: this.state,
                rolemailingZipcode: this.postalCode,
                rolemailingfullAddress: this.fullAddress,
                isBusinessMailingAddressSet: this.isBusinessMailingAddressSet,
            };
        } 

        this.omniApplyCallResp(dataToPass);
    }

    // Show pencil icon only if address is filled
    get isAddressFilled() {
        return this.fullAddress && this.fullAddress.trim() !== "";
    }
}