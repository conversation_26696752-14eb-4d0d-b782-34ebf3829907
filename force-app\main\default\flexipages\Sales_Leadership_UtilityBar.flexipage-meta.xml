<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>notes:utilityBarNoteList</componentName>
                <identifier>notes_utilityBarNoteList</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>console:history</componentName>
                <identifier>console_history</identifier>
            </componentInstance>
        </itemInstances>
        <name>utilityItems</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <name>backgroundComponents</name>
        <type>Background</type>
    </flexiPageRegions>
    <masterLabel>Sales Leadership UtilityBar</masterLabel>
    <template>
        <name>one:utilityBarTemplateDesktop</name>
        <properties>
            <name>isLeftAligned</name>
            <value>true</value>
        </properties>
    </template>
    <type>UtilityBar</type>
</FlexiPage>
