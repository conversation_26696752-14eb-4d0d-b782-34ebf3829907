@isTest
private class ApplicationControllerTest {

    @isTest
    static void testIsEmailFound_True() {
        // Arrange: create a test record with a known email
        String testEmail = '<EMAIL>';
        DAO_Application__c app = new DAO_Application__c(
            Email_Address__c = testEmail
        );
        insert app;

        // Act: call the method with the test email
        Test.startTest();
        Boolean result = ApplicationController.isEmailFound(testEmail);
        Test.stopTest();

        // Assert: should return true
        System.assertEquals(true, result, 'Email should be found');
    }

    @isTest
    static void testIsEmailFound_False() {
        // Act: call the method with an email that doesn't exist
        Test.startTest();
        Boolean result = ApplicationController.isEmailFound('<EMAIL>');
        Test.stopTest();

        // Assert: should return false
        System.assertEquals(false, result, 'Email should not be found');
    }
}