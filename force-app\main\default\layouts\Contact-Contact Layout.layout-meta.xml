<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>DataDotComClean</excludeButtons>
    <excludeButtons>DisableCustomerPortal</excludeButtons>
    <excludeButtons>DisablePartnerPortal</excludeButtons>
    <excludeButtons>DisablePrmSuperUser</excludeButtons>
    <excludeButtons>EnablePrmSuperUser</excludeButtons>
    <excludeButtons>RequestUseSfdc</excludeButtons>
    <excludeButtons>Share</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Contact Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Title</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Email</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Phone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MobilePhone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>NMLS__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Personal_Email_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Personal_Email_2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Personal_Email_3__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Work_Email_1__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Work_Email_2__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Work_Email_3__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Credit_Score__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Credit_Score_Date__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Member_RCUID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Member_RCUID_Short_Code__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Person_Account_Type__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BK_External_Contact_ID__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Role__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>county__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Office_Address__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ReportsToId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Department</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Fax</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Address Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MailingAddress</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Work_Address__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Additional Information</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Description Information</label>
        <layoutColumns/>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns>
            <layoutItems>
                <customLink>GoogleMaps</customLink>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <customLink>GoogleSearch</customLink>
            </layoutItems>
        </layoutColumns>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Clone</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>SendEmail</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>EmailHighlightAction</actionName>
            <actionType>ProductivityAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentNote</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.TextPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>10</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList>
        <quickActionListItems>
            <quickActionName>FeedItem.TextPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.ContentPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>LogACall</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>NewTask</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>NewEvent</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.ContentNote</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.LinkPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>FeedItem.PollPost</quickActionName>
        </quickActionListItems>
        <quickActionListItems>
            <quickActionName>SendEmail</quickActionName>
        </quickActionListItems>
    </quickActionList>
    <relatedLists>
        <fields>NAME</fields>
        <relatedList>et4ae5__IndividualEmailResult__c.et4ae5__Contact__c</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>OPPORTUNITY.NAME</fields>
        <fields>OPPORTUNITY.STAGE_NAME</fields>
        <fields>OPPORTUNITY.AMOUNT</fields>
        <fields>OPPORTUNITY.CLOSE_DATE</fields>
        <relatedList>RelatedOpportunityList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>QUOTE.QUOTENUMBER</fields>
        <fields>QUOTE.NAME</fields>
        <fields>QUOTE.EXPIRATIONDATE</fields>
        <fields>QUOTE.ISSYNCING</fields>
        <fields>QUOTE.SUBTOTAL</fields>
        <fields>QUOTE.TOTALPRICE</fields>
        <fields>CreatedBy</fields>
        <relatedList>RelatedQuoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CASES.CASE_NUMBER</fields>
        <fields>CASES.STATUS</fields>
        <fields>CASES.CREATED_DATE_DATE_ONLY</fields>
        <fields>CASES.SUBJECT</fields>
        <relatedList>RelatedCaseList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CAMPAIGN.NAME</fields>
        <fields>CAMPAIGN.START_DATE</fields>
        <fields>CAMPAIGN.CAMPAIGN_TYPE</fields>
        <fields>CM.STATUS</fields>
        <fields>CM.RESPONDED</fields>
        <fields>CM.LAST_UPDATE</fields>
        <relatedList>RelatedCampaignList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedContentNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHAT_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>EVENT.LOCATION</fields>
        <relatedList>RelatedActivityList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHAT_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>EVENT.LOCATION</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <relatedList>RelatedHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>FULL_NAME</fields>
        <fields>LEAD.COMPANY</fields>
        <fields>LEAD.PHONE</fields>
        <relatedList>Lead.ReferredBy__c</relatedList>
    </relatedLists>
    <relatedObjects>AccountId</relatedObjects>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00hDn000008iF3b</masterLabel>
        <sizeX>4</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
