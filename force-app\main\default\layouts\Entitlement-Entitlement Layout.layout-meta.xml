<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Entitlement Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ServiceContractId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AssetId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsPerIncident</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CasesPerEntitlement</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>StatusIndicator</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>StartDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EndDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BusinessHoursId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SlaProcessId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>RemainingCases</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>MT.MILESTONE</fields>
        <fields>MT.DESCRIPTION</fields>
        <fields>M.CRITERIA</fields>
        <fields>M.MINUTES_TO_COMPLETE</fields>
        <relatedList>RelatedMilestoneList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CASES.CASE_NUMBER</fields>
        <fields>NAME</fields>
        <fields>CASES.SUBJECT</fields>
        <fields>CASES.PRIORITY</fields>
        <fields>CASES.CREATED_DATE</fields>
        <fields>CASES.STATUS</fields>
        <fields>OWNER_NAME</fields>
        <relatedList>RelatedCaseList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>CONTACT.NAME</fields>
        <fields>CREATEDBY_USER.NAME</fields>
        <relatedList>RelatedEntitlementContactList</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
