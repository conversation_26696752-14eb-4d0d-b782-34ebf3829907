import { LightningElement, api, track ,wire} from 'lwc';
import {OmniscriptBaseMixin} from 'omnistudio/omniscriptBaseMixin';

import getRolesByApplicationId from '@salesforce/apex/RoleController.getRolesByApplicationId';
import { refreshApex } from '@salesforce/apex';
export default class DaoInCompleteRoleNames extends OmniscriptBaseMixin(LightningElement) {
    applicationId;
    @track daoRoles = [];
    @track error;

    connectedCallback() {
        this.applicationId = this.omniJsonData.DRId_DAO_Application__c;
       
            this.refreshInterval = setInterval(() => {
            this.refreshData();
        }, 5000); // Refresh every 5 seconds
        
        
    }

    @wire(getRolesByApplicationId, { applicationId: '$applicationId' }) 
        wiredData(value) {
            this.wiredResult = value;
            const { data, error } = value;
            if (data) {
                console.log('data : ',data);
                  this.daoRoles=data;
                console.log('this.daoRoles : ',this.daoRoles);
              
            } else if (error) {
                console.error('Error fetching roles:', error);
            }
                console.log("dataofdaoRoles",this.daoRoles);
                console.log("this.daoRoles.length",this.daoRoles.length);
            if(this.daoRoles.length > 0)
            {
                console.log("Enterted wired if");
                let dataToPass = {};
                dataToPass={
                    incompleteRoleFlag:true
                };
                this.omniApplyCallResp(dataToPass);
            } 
             else
            {
                console.log("Entered wired else");
                let dataToPass = {};
                dataToPass={
                    incompleteRoleFlag:false
                };
                this.omniApplyCallResp(dataToPass);
            }
            
    }
    refreshData() {
        refreshApex(this.wiredResult);
    }
    handleRoleClick(event) {
        const roleId = event.target.dataset.id;
        if (roleId) {
            const encodedId = btoa(roleId); // Encode the role ID
            const url = `https://rcu2022--devzennify.sandbox.my.site.com/s/dao-role?ContextId=${encodedId}`;
            window.open(url, '_blank'); // Open the URL in a new tab
        }
    }
    disconnectedCallback() {
        console.log("data",this.daoRoles);
        console.log("this.daoRoles.length",this.daoRoles.length);
        
        // if(this.daoRoles.length > 0)
        // {
        //     console.log("Enterted if");
        //     let dataToPass = {};
        //     dataToPass={
        //         incompleteRoleFlag:true
        //     };
        //     this.omniApplyCallResp(dataToPass);
        // } 
        // else
        // {
        //     console.log("Entered else");
        //     let dataToPass = {};
        //     dataToPass={
        //         incompleteRoleFlag:false
        //     };
        //     this.omniApplyCallResp(dataToPass);
        // }
    }
}