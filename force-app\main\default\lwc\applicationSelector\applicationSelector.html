<template>
    <lightning-radio-group
        name="applicationChoice"
        label="Choose an option"
        options={options}
        value={selectedOption}
        onchange={handleOptionChange}>
    </lightning-radio-group>

    <template if:true={showEmailField}>
        <lightning-input
            label="Enter Email Address"
            value={email}
            type="email"
            onchange={handleEmailChange}>
        </lightning-input>
    </template>

    <div class="button-container">
        <div class="left">
        </div>
        <div class="right">
            <button class="next-button" onclick={moveToNextStep}>Next</button>
        </div>
    </div>
</template>