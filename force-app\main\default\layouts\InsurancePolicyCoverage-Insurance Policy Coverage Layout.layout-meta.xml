<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CoverageName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Category</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CategoryGroup</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CategoryCode</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CategoryGroupType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LimitRange</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LimitAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LimitDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EffectiveDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpirationDate</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>InsurancePolicyId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InsurancePolicyAssetId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LimitPercentage</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DeductibleAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PremiumAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TermPremiumAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TermFeeAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TermTaxAmount</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
