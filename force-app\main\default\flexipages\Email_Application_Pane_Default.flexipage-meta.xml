<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>clients:emailTemplatesActionContainerFlex</componentName>
                <identifier>clients_emailTemplatesActionContainerFlex</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>lightningInbox:logItemButtonContainer</componentName>
                <identifier>lightningInbox_logItemButtonContainer</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>clients:peopleCardContainerFlex</componentName>
                <identifier>clients_peopleCardContainerFlex</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>entityApiName</name>
                    <value>Account</value>
                </componentInstanceProperties>
                <componentName>clients:relatedRecordsPageFlex</componentName>
                <identifier>clients_relatedRecordsPageFlex</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>entityApiName</name>
                    <value>Opportunity</value>
                </componentInstanceProperties>
                <componentName>clients:relatedRecordsPageFlex</componentName>
                <identifier>clients_relatedRecordsPageFlex2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>entityApiName</name>
                    <value>Case</value>
                </componentInstanceProperties>
                <componentName>clients:relatedRecordsPageFlex</componentName>
                <identifier>clients_relatedRecordsPageFlex3</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>relatedTabContent</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>clients:tasksActionContainerFlex</componentName>
                <identifier>clients_tasksActionContainerFlex</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>tasksTabContent</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>assistiveText</name>
                    <value>clients_tabset.assistive_related</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>relatedTabContent</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>id</name>
                    <value>relatedTabId</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Inbox.Tab.related</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>visibility</name>
                    <valueList>
                        <valueListItems>
                            <value>edit</value>
                        </valueListItems>
                        <valueListItems>
                            <value>view</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentName>clients:tab</componentName>
                <identifier>clients_tab</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>assistiveText</name>
                    <value>clients_tabset.assistive_tasks</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>tasksTabContent</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>id</name>
                    <value>taskTabId</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Inbox.Tab.tasks</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>visibility</name>
                    <valueList>
                        <valueListItems>
                            <value>edit</value>
                        </valueListItems>
                        <valueListItems>
                            <value>view</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentName>clients:tab</componentName>
                <identifier>clients_tab2</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>maintabs</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>defaultEditModeTab</name>
                    <value>relatedTabId</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>defaultViewModeTab</name>
                    <value>relatedTabId</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>maintabs</value>
                </componentInstanceProperties>
                <componentName>clients:tabset</componentName>
                <identifier>clients_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Email Application Pane Default</masterLabel>
    <parentFlexiPage>clients__default_app_L</parentFlexiPage>
    <template>
        <name>clients:defaultMailAppTemplate</name>
    </template>
    <type>MailAppAppPage</type>
</FlexiPage>
