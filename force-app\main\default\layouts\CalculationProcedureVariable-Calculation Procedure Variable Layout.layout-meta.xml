<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DataType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>UiDisplayOrder</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DefaultValue</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Precision</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CalculationProcedureVersionId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DisplayName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CalculationMatrixName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsEditable</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsUserDefined</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
