<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InitiatingAttendeeId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>StartDateTime</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Sentiment</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalIdentifier</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Reason</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Duration</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EndDateTime</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>CommunicationChannel</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>Name</fields>
        <fields>Engagement</fields>
        <fields>ExternalAttendee</fields>
        <fields>InternalAttendee</fields>
        <fields>StartDateTime</fields>
        <fields>EndDateTime</fields>
        <relatedList>EngagementAttendees</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>Engagement</fields>
        <fields>RelatedPerson</fields>
        <relatedList>EngagementTopics</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedContentNoteList</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
