public with sharing class rcuConsumerDAO {
 
    public rcuConsumerDAO() {
 
    }
 
    @AuraEnabled(cacheable=true)
    public static cdaoModel.Application getApplication(){
        try {
            // Create a fully initialized application structure
            cdaoModel.Application app = new cdaoModel.Application();
        app.primaryApplicant = new cdaoModel.Applicant();
        app.jointApplicants = new List<cdaoModel.Applicant>();
        app.products = new cdaoModel.Products();
        app.addionalInfromation = new cdaoModel.AddionalInfromation();
        app.fundingOptions = new cdaoModel.FundingOptions();

        // Primary Applicant
        cdaoModel.Applicant primary = new cdaoModel.Applicant();
        primary.MemberInformation = new cdaoModel.MemberInformation();
        primary.MemberInformation.SSN_TIN = '';
        primary.MemberInformation.First_Name = '';
        primary.MemberInformation.Middle_Name = '';
        primary.MemberInformation.Individual_Role = '';
        primary.MemberInformation.Last_Name = '';
        primary.MemberInformation.Date_of_Birth = '';
        primary.MemberInformation.Suffix = '';
        primary.MemberInformation.Phone = '';
        primary.MemberInformation.Email_Address = '';

        primary.MailingAddress = new cdaoModel.MailingAddress();
        primary.MailingAddress.Mailing_Street_Address = '';
        primary.MailingAddress.Mailing_City = '';
        primary.MailingAddress.Mailing_State = '';
        primary.MailingAddress.Mailing_Zip_code = '';

        primary.PhysicalAddress = new cdaoModel.PhysicalAddress();
        primary.PhysicalAddress.Physical_Street_Address = '';
        primary.PhysicalAddress.Physical_City = '';
        primary.PhysicalAddress.Physical_State = '';
        primary.PhysicalAddress.Physical_Zip_code = '';

        primary.Mailing_Address_same_as_Physical = false;

        primary.IdentityInfo = new cdaoModel.Identity();
        primary.IdentityInfo.Type = '';
        primary.IdentityInfo.ID_Number = '';
        primary.IdentityInfo.ID_Issued_Date = '';
        primary.IdentityInfo.ID_Expiration_Date = '';
        primary.IdentityInfo.ID_Country = '';
        primary.IdentityInfo.ID_State = '';

        app.primaryApplicant = primary;
            return app;
        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    

    
    @AuraEnabled
    public static String saveApplication(String applicationData) {
        try {
            if (applicationData == null) {
                return 'Error: Deserialization resulted in null object';
            }
            
            // Parse the application data
            Map<String, Object> appData = (Map<String, Object>) JSON.deserializeUntyped(applicationData);
            return 'Success';
        } catch (Exception e) {
            return 'Error: ' + e.getMessage();
        }
    }
}


