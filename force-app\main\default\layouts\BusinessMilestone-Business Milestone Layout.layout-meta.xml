<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>MilestoneDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>PrimaryAccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsTentative</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpirationDate</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>MilestoneType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MilestoneDescription</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MilestoneSiteId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsExpired</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExpirationDescription</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
