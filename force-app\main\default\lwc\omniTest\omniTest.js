import { LightningElement, api, track } from 'lwc';
import { ShowToastEvent } from "lightning/platformShowToastEvent";
import { OmniscriptBaseMixin } from "omnistudio/omniscriptBaseMixin";
export default class omniTest extends OmniscriptBaseMixin(LightningElement) {
    
    @api screentype;

    @track Marijuana_Business__c = false;
    @track MarijuanaLicensed__c = false;
    @track Show_MarijuanaLicensed__c = false;
    @track Intermediary__c = false;
    @track Show_Intermediary__c = false;


    @track questions = {
        Internet_Gambling__c: {
            field: 'Internet_Gambling__c',
            value: '',
            isMain: true,
            noChecked : false,
            yesChecked : false
        },
        Marijuana_Business__c: {
            field: 'Marijuana_Business__c',
            value: '',
            isMain: true,
            noChecked : false,
            yesChecked : false,
            showSubquestions: false,
            subquestions: {
                MarijuanaLicensed__c: {
                    parent: 'Marijuana_Business__c',
                    field: 'MarijuanaLicensed__c',
                    value: '',
                    type: 'radio',
                    isMain: false,
                    noChecked : false,
                    yesChecked : false
                },
                MarijuanaPercentage__c: {
                    parent: 'Marijuana_Business__c',
                    field: 'MarijuanaPercentage__c',
                    value: '',
                    type: 'picklist',
                    isMain: false
                },
                MarijuanaActivity__c: {
                    parent: 'Marijuana_Business__c',
                    field: 'MarijuanaActivity__c',
                    value: '',
                    type: 'multipicklist',
                    isMain: false
                }
            }
        },
        Intermediary__c: {
            field: 'Intermediary__c',
            value: '',
            isMain: true,
            noChecked : false,
            yesChecked : false,
            showSubquestions: false,
            subquestions: {
                ProfessionalType__c: {
                    parent: 'Intermediary__c',
                    field: 'ProfessionalType__c',
                    value: '',
                    type: 'picklist',
                    isMain: false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        ProfessionalTypeOtherText__c: {
                            parent: 'ProfessionalType__c',
                            grandParent: 'Intermediary__c',
                            field: 'ProfessionalTypeOtherText__c',
                            value: '',
                            type: 'text',
                            isMain: false
                        }
                    }
                },
                ServicesTypes__c: {
                    parent: 'Intermediary__c',
                    field: 'ServicesTypes__c',
                    value: '',
                    type: 'multipicklist',
                    isMain: false
                },
                ProfessionalOthersUsing__c: {
                    parent: 'Intermediary__c',
                    field: 'ProfessionalOthersUsing__c',
                    value: '',
                    type: 'radio',
                    isMain: false,
                    noChecked : false,
                    yesChecked : false
                }
            }
        },
        Courier_Services__c: {
            field: 'Courier_Services__c',
            value: '',
            isMain: true,
            noChecked : false,
            yesChecked : false
        },
        Third_Party_Payment_Processor__c: {
            field: 'Third_Party_Payment_Processor__c',
            value: '',
            isMain: true,
            noChecked : false,
            yesChecked : false,
            showSubquestions: false,
            subquestions: {
                TransactionsSendPayments__c: {
                    parent: 'Third_Party_Payment_Processor__c',
                    field: 'TransactionsSendPayments__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false
                },
                TransactionsReceivePayments__c: {
                    parent: 'Third_Party_Payment_Processor__c',
                    field: 'TransactionsReceivePayments__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false
                },
                PaymentServices__c: {
                    parent: 'Third_Party_Payment_Processor__c',
                    field: 'PaymentServices__c',
                    value: '',
                    type: 'multipicklist',
                    isMain: false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        PaymentServicesOtherText__c: {
                            parent: 'PaymentServices__c',
                            grandParent: 'Third_Party_Payment_Processor__c',
                            field: 'PaymentServicesOtherText__c',
                            value: '',
                            type: 'text',
                            isMain: false
                        }
                    }
                },
                PaymentsThroughAccounts__c: {
                    parent: 'Third_Party_Payment_Processor__c',
                    field: 'PaymentsThroughAccounts__c',
                    value: '',
                    type: 'radio',
                    isMain: false,
                    noChecked : false,
                    yesChecked : false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        PaymentsHowProcessed__c: {
                            parent: 'PaymentsThroughAccounts__c',
                            grandParent: 'Third_Party_Payment_Processor__c',
                            field: 'PaymentsHowProcessed__c',
                            value: '',
                            type: 'multipicklist',
                            isMain: false
                        }
                    }
                },
                CreateChecksRemotely__c: {
                    parent: 'Third_Party_Payment_Processor__c',
                    field: 'CreateChecksRemotely__c',
                    value: '',
                    type: 'radio',
                    isMain: false,
                    noChecked : false,
                    yesChecked : false
                },
                BusinessTypeRestrictions__c: {
                    parent: 'Third_Party_Payment_Processor__c',
                    field: 'BusinessTypeRestrictions__c',
                    value: '',
                    type: 'radio',
                    isMain: false,
                    noChecked : false,
                    yesChecked : false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        BusinessTypeRestrictionsText__c: {
                            parent: 'BusinessTypeRestrictions__c',
                            grandParent: 'Third_Party_Payment_Processor__c',
                            field: 'BusinessTypeRestrictionsText__c',
                            value: '',
                            type: 'text',
                            isMain: false
                        }
                    }
                }
            }
        },
        ATM_Business__c: {
            field: 'ATM_Business__c',
            value: '',
            isMain: true,
            noChecked : false,
            yesChecked : false,
            showSubquestions: false,
            subquestions: {
                NumberOfAtm__c: {
                    parent: 'ATM_Business__c',
                    field: 'NumberOfAtm__c',
                    value: '',
                    type: 'number',
                    isMain: false
                },
                ReplenishAtmCash__c: {
                    parent: 'ATM_Business__c',
                    field: 'ReplenishAtmCash__c',
                    value: '',
                    type: 'radio',
                    isMain: false,
                    noChecked : false,
                    yesChecked : false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        SourceOfAtmCash__c: {
                            parent: 'ReplenishAtmCash__c',
                            grandParent: 'ATM_Business__c',
                            field: 'SourceOfAtmCash__c',
                            value: '',
                            type: 'multipicklist',
                            isMain: false
                        }
                    }
                },
                AtmMaxHolding__c: {
                    parent: 'ATM_Business__c',
                    field: 'AtmMaxHolding__c',
                    value: '',
                    type: 'number',
                    isMain: false
                },
                AtmDenomination__c: {
                    parent: 'ATM_Business__c',
                    field: 'AtmDenomination__c',
                    value: '',
                    type: 'multipicklist',
                    isMain: false
                },
                PrivateAtmType__c: {
                    parent: 'ATM_Business__c',
                    field: 'PrivateAtmType__c',
                    value: '',
                    type: 'multipicklist',
                    isMain: false
                }
            }
        },
        Non_Bank_Financial_Institution__c: {
            field: 'Non_Bank_Financial_Institution__c',
            value: '',
            isMain: true,
            noChecked : false,
            yesChecked : false,
            showSubquestions: false,
            subquestions: {
                InvolveCasinos__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'InvolveCasinos__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false
                },
                InvolveSecurities__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'InvolveSecurities__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        SecuritiesFinancialInstitution__c: {
                            parent: 'InvolveSecurities__c',
                            grandParent: 'Non_Bank_Financial_Institution__c',
                            field: 'SecuritiesFinancialInstitution__c',
                            value: '',
                            type: 'multipicklist',
                            isMain: false
                        },
                        SecuritiesHowBusinessRegistered__c: {
                            parent: 'InvolveSecurities__c',
                            grandParent: 'Non_Bank_Financial_Institution__c',
                            field: 'SecuritiesHowBusinessRegistered__c',
                            value: '',
                            type: 'multipicklist',
                            isMain: false
                        },
                        SecuritiesInvolveSecurities__c: {
                            parent: 'InvolveSecurities__c',
                            grandParent: 'Non_Bank_Financial_Institution__c',
                            field: 'SecuritiesInvolveSecurities__c',
                            value: '',
                            type: 'radio',
                            isMain: false,
                            noChecked : false,
                            yesChecked : false,
                            showSubSubSubquestions: false,
                            subsubsubquestions: {
                                SecuritiesProductTypes__c: {
                                    grandParent: 'InvolveSecurities__c',
                                    greatgrandParent: 'Non_Bank_Financial_Institution__c',
                                    parent: 'SecuritiesInvolveSecurities__c',
                                    field: 'SecuritiesProductTypes__c',
                                    value: '',
                                    type: 'multipicklist',
                                    isMain: false
                                },
                                SecuritiesInvestFundsInternationally__c: {
                                    grandParent: 'InvolveSecurities__c',
                                    greatgrandParent: 'Non_Bank_Financial_Institution__c',
                                    parent: 'SecuritiesInvolveSecurities__c',
                                    field: 'SecuritiesInvestFundsInternationally__c',
                                    value: '',
                                    type: 'radio',
                                    isMain: false,
                                    noChecked : false,
                                    yesChecked : false,
                                    showSubSubSubSubquestions: false,
                                    subsubsubsubquestions: {
                                        SecuritiesCountriesText__c: {
                                            grandParent: 'SecuritiesInvolveSecurities__c',
                                            greatgrandParent: 'InvolveSecurities__c',
                                            greatGreategrandParent: 'Non_Bank_Financial_Institution__c',
                                            parent: 'SecuritiesInvestFundsInternationally__c',
                                            field: 'SecuritiesCountriesText__c',
                                            value: '',
                                            type: 'text',
                                            isMain: false
                                        }
                                    }
                                },
                                SecuritiesServiceTypes__c: {
                                    grandParent: 'InvolveSecurities__c',
                                    greatgrandParent: 'Non_Bank_Financial_Institution__c',
                                    parent: 'SecuritiesInvolveSecurities__c',
                                    field: 'SecuritiesServiceTypes__c',
                                    value: '',
                                    type: 'multipicklist',
                                    isMain: false
                                }
                            }
                        }
                    }
                },
                InvolveInsurance__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'InvolveInsurance__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        InvolveInsuranceStateRegIns__c: {
                            parent: 'InvolveInsurance__c',
                            grandParent: 'Non_Bank_Financial_Institution__c',
                            field: 'InvolveInsuranceStateRegIns__c',
                            value: '',
                            type: 'radio',
                            isMain: false,
                            noChecked : false,
                            yesChecked : false,
                        }
                    }
                },
                InvolveLoanFinance__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'InvolveLoanFinance__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false
                },
                InvolveCreditCards__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'InvolveCreditCards__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false
                },
                InvolvePreciousMetals__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'InvolvePreciousMetals__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        InvolvePreciousMetalsBuy50k__c: {
                            parent: 'InvolvePreciousMetals__c',
                            grandParent: 'Non_Bank_Financial_Institution__c',
                            field: 'InvolvePreciousMetalsBuy50k__c',
                            value: '',
                            type: 'radio',
                            isMain: false,
                            noChecked : false,
                            yesChecked : false,
                        },
                        InvolvePreciousMetalsSell50k__c: {
                            parent: 'InvolvePreciousMetals__c',
                            grandParent: 'Non_Bank_Financial_Institution__c',
                            field: 'InvolvePreciousMetalsSell50k__c',
                            value: '',
                            type: 'radio',
                            isMain: false,
                            noChecked : false,
                            yesChecked : false,
                        }
                    }
                },
                InvolvePawnBrokerage__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'InvolvePawnBrokerage__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false
                },
                InvolveTravelAgency__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'InvolveTravelAgency__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false
                },
                InvolveTelegraphCompany__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'InvolveTelegraphCompany__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false
                },
                InvolveVehicleSales__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'InvolveVehicleSales__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        InvolveVehicleTypes__c: {
                            parent: 'InvolveVehicleSales__c',
                            grandParent: 'Non_Bank_Financial_Institution__c',
                            field: 'InvolveVehicleTypes__c',
                            value: '',
                            type: 'multipicklist',
                            isMain: false
                        }
                    }
                },
                InvolveRealEstateClosing__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'InvolveRealEstateClosing__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false
                },
                InvolvePostalService__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'InvolvePostalService__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false
                },
                InvolveGovAgency__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'InvolveGovAgency__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false
                },
                DocBsaAmlProgram__c: {
                    parent: 'Non_Bank_Financial_Institution__c',
                    field: 'DocBsaAmlProgram__c',
                    value: '',
                    type: 'radio',
                    isMain: false,
                    noChecked : false,
                    yesChecked : false,
                }
            }
        },
        Money_Service_Business__c: {
            field: 'Money_Service_Business__c',
            value: '',
            isMain: true,
            noChecked : false,
            yesChecked : false,
            showSubquestions: false,
            subquestions: {
                InvolveCurrencyExchange__c: {
                    parent: 'Money_Service_Business__c',
                    field: 'InvolveCurrencyExchange__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        Involve_Curency_Exchange_Agent__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveCurrencyExchange__c',
                            field: 'Involve_Curency_Exchange_Agent__c',
                            value: false,
                            type: 'checkbox',
                            isMain: false
                        },
                        Involve_Curency_Exchange_Principal__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveCurrencyExchange__c',
                            field: 'Involve_Curency_Exchange_Principal__c',
                            value: false,
                            type: 'checkbox',
                            isMain: false
                        }
                    }
                },
                InvolveCashChecks__c: {
                    parent: 'Money_Service_Business__c',
                    field: 'InvolveCashChecks__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        InvolveCheckTypes__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveCashChecks__c',
                            field: 'InvolveCheckTypes__c',
                            value: '',
                            type: 'multipicklist',
                            isMain: false
                        }
                    }
                },
                InvolveMoneyOrders__c: {
                    parent: 'Money_Service_Business__c',
                    field: 'InvolveMoneyOrders__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        Involve_Money_Orders_Agent__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveMoneyOrders__c',
                            field: 'Involve_Money_Orders_Agent__c',
                            value: false,
                            type: 'checkbox',
                            isMain: false
                        },
                        Involve_Money_Orders_Principal__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveMoneyOrders__c',
                            field: 'Involve_Money_Orders_Principal__c',
                            value: false,
                            type: 'checkbox',
                            isMain: false
                        }
                    }                    
                },
                InvolveTransmitMoney__c: {
                    parent: 'Money_Service_Business__c',
                    field: 'InvolveTransmitMoney__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        Transmit_Money_Agent__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveTransmitMoney__c',
                            field: 'Transmit_Money_Agent__c',
                            value: false,
                            type: 'checkbox',
                            isMain: false
                        },
                        Transmit_Money_Principal__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveTransmitMoney__c',
                            field: 'Transmit_Money_Principal__c',
                            value: false,
                            type: 'checkbox',
                            isMain: false
                        },
                        TransmitMoneyNonUsLocations__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveTransmitMoney__c',
                            field: 'TransmitMoneyNonUsLocations__c',
                            value: '',
                            type: 'radio',
                            isMain: false,
                            noChecked : false,
                            yesChecked : false,
                            showSubSubSubquestions: false,
                            subsubsubquestions: {
                                TransmitForeignCountriesText__c : {
                                    grandParent: 'InvolveTransmitMoney__c',
                                    greatgrandParent: 'Money_Service_Business__c',
                                    parent: 'TransmitMoneyNonUsLocations__c',
                                    field: 'TransmitForeignCountriesText__c',
                                    value: '',
                                    type: 'text',
                                    isMain: false
                                }
                            }
                        },
                        TransmitMoneyTypes__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveTransmitMoney__c',
                            field: 'TransmitMoneyTypes__c',
                            value: '',
                            type: 'multipicklist',
                            isMain: false
                        },
                        TransmitMoneyCvc__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveTransmitMoney__c',
                            field: 'TransmitMoneyCvc__c',
                            value: '',
                            type: 'radio',
                            isMain: false,
                            noChecked : false,
                            yesChecked : false,
                            showSubSubSubquestions: false,
                            subsubsubquestions: {
                                TransmitMoneyActivities__c : {
                                    grandParent: 'InvolveTransmitMoney__c',
                                    greatgrandParent: 'Money_Service_Business__c',
                                    parent: 'TransmitMoneyCvc__c',
                                    field: 'TransmitMoneyActivities__c',
                                    value: '',
                                    type: 'multipicklist',
                                    isMain: false
                                }
                            }
                        }
                    }
                },
                InvolveVirtualCurrency__c: {
                    parent: 'Money_Service_Business__c',
                    field: 'InvolveVirtualCurrency__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false
                },
                InvolveGiftCards__c: {
                    parent: 'Money_Service_Business__c',
                    field: 'InvolveGiftCards__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false,
                    showSubSubquestions: false,
                    subsubquestions: {
                        Gift_Card_Agent__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveGiftCards__c',
                            field: 'Gift_Card_Agent__c',
                            value: false,
                            type: 'checkbox',
                            isMain: false
                        },
                        Gift_Card_Principal__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveGiftCards__c',
                            field: 'Gift_Card_Principal__c',
                            value: false,
                            type: 'checkbox',
                            isMain: false
                        },
                        GiftCardExceedDailyMax__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveGiftCards__c',
                            field: 'GiftCardExceedDailyMax__c',
                            value: '',
                            type: 'radio',
                            isMain: false,
                            noChecked : false,
                            yesChecked : false,
                            showSubSubSubquestions: false,
                            subsubsubquestions: {
                                GiftCardActivationProcess__c : {
                                    grandParent: 'InvolveGiftCards__c',
                                    greatgrandParent: 'Money_Service_Business__c',
                                    parent: 'GiftCardExceedDailyMax__c',
                                    field: 'GiftCardActivationProcess__c',
                                    value: '',
                                    type: 'radio',
                                    isMain: false,
                                    noChecked : false,
                                    yesChecked : false,
                                }
                            }
                        },
                        GiftCardNetworkBranded__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveGiftCards__c',
                            field: 'GiftCardNetworkBranded__c',
                            value: '',
                            type: 'radio',
                            isMain: false,
                            noChecked : false,
                            yesChecked : false,
                            showSubSubSubquestions: false,
                            subsubsubquestions: {
                                GiftCardAccessDailyMax__c : {
                                    grandParent: 'InvolveGiftCards__c',
                                    greatgrandParent: 'Money_Service_Business__c',
                                    parent: 'GiftCardNetworkBranded__c',
                                    field: 'GiftCardAccessDailyMax__c',
                                    value: '',
                                    type: 'radio',
                                    isMain: false,
                                    noChecked : false,
                                    yesChecked : false,
                                    showSubSubSubSubquestions: false,
                                    showSubSubSubSubquestionsYesNo: false,
                                    subsubsubsubquestions: {
                                        GiftCardReloaded__c: {
                                            grandParent: 'GiftCardNetworkBranded__c',
                                            greatgrandParent: 'InvolveGiftCards__c',
                                            greatGreategrandParent: 'Money_Service_Business__c',
                                            parent: 'GiftCardAccessDailyMax__c',
                                            field: 'GiftCardReloaded__c',
                                            value: '',
                                            type: 'radio',
                                            isMain: false,
                                            noChecked : false,
                                            yesChecked : false,
                                        },
                                        GiftCardTransferFunds__c: {
                                            grandParent: 'GiftCardNetworkBranded__c',
                                            greatgrandParent: 'InvolveGiftCards__c',
                                            greatGreategrandParent: 'Money_Service_Business__c',
                                            parent: 'GiftCardAccessDailyMax__c',
                                            field: 'GiftCardTransferFunds__c',
                                            value: '',
                                            type: 'radio',
                                            isMain: false,
                                            noChecked : false,
                                            yesChecked : false,
                                        },
                                        GiftCardTransferFundsInternationally__c: {
                                            grandParent: 'GiftCardNetworkBranded__c',
                                            greatgrandParent: 'InvolveGiftCards__c',
                                            greatGreategrandParent: 'Money_Service_Business__c',
                                            parent: 'GiftCardAccessDailyMax__c',
                                            field: 'GiftCardTransferFundsInternationally__c',
                                            value: '',
                                            type: 'radio',
                                            isMain: false,
                                            noChecked : false,
                                            yesChecked : false,
                                        },
                                        GiftCardRequireCustomerId__c: {
                                            grandParent: 'GiftCardNetworkBranded__c',
                                            greatgrandParent: 'InvolveGiftCards__c',
                                            greatGreategrandParent: 'Money_Service_Business__c',
                                            parent: 'GiftCardAccessDailyMax__c',
                                            field: 'GiftCardRequireCustomerId__c',
                                            value: '',
                                            type: 'radio',
                                            isMain: false,
                                            noChecked : false,
                                            yesChecked : false,
                                        }
                                    }
                                }
                            }
                        },
                        GiftCardPreventSales__c: {
                            grandParent: 'Money_Service_Business__c',
                            parent: 'InvolveGiftCards__c',
                            field: 'GiftCardPreventSales__c',
                            value: '',
                            type: 'radio',
                            isMain: false,
                            noChecked : false,
                            yesChecked : false
                        }
                    }
                },
                GiftCardBsaAmlProg__c: {
                    parent: 'Money_Service_Business__c',
                    field: 'GiftCardBsaAmlProg__c',
                    value: '',
                    type: 'radio',
                    isMain: false,
                    noChecked : false,
                    yesChecked : false
                },
                RegisteredFinCen__c: {
                    parent: 'Money_Service_Business__c',
                    field: 'RegisteredFinCen__c',
                    value: '',
                    type: 'radio',
                    isMain: false,
                    noChecked : false,
                    yesChecked : false
                },
                InvolveNone__c: {
                    parent: 'Money_Service_Business__c',
                    field: 'InvolveNone__c',
                    value: false,
                    type: 'checkbox',
                    isMain: false
                },
            }
        }
    };

    connectedCallback() {
        if(this?.omniJsonData?.DiligenceQuestions){
            this.questions = JSON.parse(JSON.stringify(this.omniJsonData.DiligenceQuestions));
        }
    }

    handleRadioChange(event){
        console.log('$Radio Value: ',event.target.value);
        console.log('$Radio Field: ',event.target.name);

        this.questions[event.target.name].value = event.target.value;
        this.questions[event.target.name].showSubquestions = this.questions[event.target.name].value == 'Yes';
        if(this.questions[event.target.name].value == 'Yes'){
            this.questions[event.target.name].yesChecked = true;
            this.questions[event.target.name].noChecked = false;
        }else{
            this.questions[event.target.name].yesChecked = false;
            this.questions[event.target.name].noChecked = true;
            this.handleSubRadioChange1(event.target.name);
        }
        console.log('%c$QUESTIONS:', 'color: blue;', JSON.stringify(this.questions, null, 2));        
    }

    handleSubRadioChange1(name){

       if(name == 'Marijuana_Business__c'){
            this.questions.Marijuana_Business__c.subquestions.MarijuanaLicensed__c.value = "";
            this.questions.Marijuana_Business__c.subquestions.MarijuanaLicensed__c.yesChecked = false;
            this.questions.Marijuana_Business__c.subquestions.MarijuanaLicensed__c.noChecked = false;

            this.questions.Marijuana_Business__c.subquestions.MarijuanaPercentage__c.value = "";

            this.questions.Marijuana_Business__c.subquestions.MarijuanaActivity__c.value = "";
        }

        if(name == 'Intermediary__c'){        
            this.questions.Intermediary__c.subquestions.ProfessionalType__c.value = "";		
        	this.questions.Intermediary__c.subquestions.ProfessionalType__c.subsubquestions.ProfessionalTypeOtherText__c.value = "";

            this.questions.Intermediary__c.subquestions.ServicesTypes__c.value = "";

            this.questions.Intermediary__c.subquestions.ProfessionalOthersUsing__c.value = "";
            this.questions.Intermediary__c.subquestions.ProfessionalOthersUsing__c.yesChecked = false;
            this.questions.Intermediary__c.subquestions.ProfessionalOthersUsing__c.noChecked = false;
        }

        if(name == 'Third_Party_Payment_Processor__c'){        
            this.questions.Third_Party_Payment_Processor__c.subquestions.TransactionsSendPayments__c.value = false;	
            this.questions.Third_Party_Payment_Processor__c.subquestions.TransactionsReceivePayments__c.value = false;	

            this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.value = "";
        	this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.subsubquestions.PaymentServicesOtherText__c.value = "";
            this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.showSubSubquestions = false;

            this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.value = "";
            this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.yesChecked = false;
            this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.noChecked = false;
            this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.subsubquestions.PaymentsHowProcessed__c.value = "";
            this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.showSubSubquestions = false;

            this.questions.Third_Party_Payment_Processor__c.subquestions.CreateChecksRemotely__c.value = "";
            this.questions.Third_Party_Payment_Processor__c.subquestions.CreateChecksRemotely__c.yesChecked = false;
            this.questions.Third_Party_Payment_Processor__c.subquestions.CreateChecksRemotely__c.noChecked = false;

            this.questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.value = "";
            this.questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.yesChecked = false;
            this.questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.noChecked = false;
            this.questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.subsubquestions.BusinessTypeRestrictionsText__c.value = "";
            this.questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.showSubSubquestions = false;
        }

        if(name == 'ATM_Business__c'){        
            this.questions.ATM_Business__c.subquestions.NumberOfAtm__c.value = '';	

            this.questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.value = "";
            this.questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.yesChecked = false;
            this.questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.noChecked = false;
            this.questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.subsubquestions.SourceOfAtmCash__c.value = "";
            this.questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.showSubSubquestions = false;

            this.questions.ATM_Business__c.subquestions.AtmMaxHolding__c.value = '';	

            this.questions.ATM_Business__c.subquestions.AtmDenomination__c.value = "";            

            this.questions.ATM_Business__c.subquestions.PrivateAtmType__c.value = "";
        }

        if(name == 'Non_Bank_Financial_Institution__c'){    
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.showSubSubquestions = false;    
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveCasinos__c.value = false;	

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.value = false;	
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesFinancialInstitution__c.value = "";
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesHowBusinessRegistered__c.value = "";

        	this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.value = "";
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.noChecked = false;
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.yesChecked = false;

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.showSubSubSubquestions = false;     
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesProductTypes__c.value = "";

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.value = "";
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.yesChecked = false;
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.noChecked = false;
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.showSubSubSubSubquestions = false;
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.subsubsubsubquestions.SecuritiesCountriesText__c.value = "";
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesServiceTypes__c.value = "";

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.value = false;	
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.showSubSubquestions = false;
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.subsubquestions.InvolveInsuranceStateRegIns__c.value = "";
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.subsubquestions.InvolveInsuranceStateRegIns__c.yesChecked = false;
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.subsubquestions.InvolveInsuranceStateRegIns__c.noChecked = false;            

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveLoanFinance__c.value = false;

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveCreditCards__c.value = false;

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.value = false;
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.showSubSubquestions = false;    
            
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsBuy50k__c.value = "";
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsBuy50k__c.noChecked = false;
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsBuy50k__c.yesChecked = false;

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsSell50k__c.value = "";
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsSell50k__c.noChecked = false;
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsSell50k__c.yesChecked = false;

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePawnBrokerage__c.value = false;

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveTravelAgency__c.value = false;

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveTelegraphCompany__c.value = false;

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveVehicleSales__c.value = false;
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveVehicleSales__c.showSubSubquestions = false; 
            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveVehicleSales__c.subsubquestions.InvolveVehicleTypes__c.value = "";

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveRealEstateClosing__c.value = false;

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePostalService__c.value = false;

            this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveGovAgency__c.value = false;

            this.questions.Non_Bank_Financial_Institution__c.subquestions.DocBsaAmlProgram__c.value = "";
            this.questions.Non_Bank_Financial_Institution__c.subquestions.DocBsaAmlProgram__c.noChecked = false;
            this.questions.Non_Bank_Financial_Institution__c.subquestions.DocBsaAmlProgram__c.yesChecked = false;
        }
        if(name == 'Money_Service_Business__c'){    
            this.questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.showSubSubquestions = false;    
            this.questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.value = false;	
	
            this.questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.subsubquestions.Involve_Curency_Exchange_Agent__c.value = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.subsubquestions.Involve_Curency_Exchange_Principal__c.value = false;

        	this.questions.Money_Service_Business__c.subquestions.InvolveCashChecks__c.value = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveCashChecks__c.showSubSubquestions = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveCashChecks__c.subsubquestions.InvolveCheckTypes__c.value = "";

            this.questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.value = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.showSubSubquestions = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.subsubquestions.Involve_Money_Orders_Agent__c.value = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.subsubquestions.Involve_Money_Orders_Principal__c.value = false;

            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.value = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.showSubSubquestions = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.Transmit_Money_Agent__c.value = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.Transmit_Money_Principal__c.value = false;

            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.showSubSubSubquestions = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.yesChecked = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.noChecked = false;            
            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.subsubsubquestions.TransmitForeignCountriesText__c.value = '';

            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyTypes__c.value = '';

            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.showSubSubSubquestions = false; 
            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.yesChecked = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.noChecked = false;  
            this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.subsubsubquestions.TransmitMoneyActivities__c.value = '';

            this.questions.Money_Service_Business__c.subquestions.InvolveVirtualCurrency__c.value = false;

            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.value = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.showSubSubquestions = false;

            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.Gift_Card_Agent__c.value = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.Gift_Card_Principal__c.value = false;
            
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.showSubSubSubquestions = false; 
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.yesChecked = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.noChecked = false;  

            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.noChecked = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.yesChecked = false;

            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.showSubSubSubquestions = false; 
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.yesChecked = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.noChecked = false;  

            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestions = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestionsYesNo = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.noChecked = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.yesChecked = false;
            
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.noChecked = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.yesChecked = false;

            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.noChecked = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.yesChecked = false;

            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.noChecked = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.yesChecked = false;

            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.noChecked = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.yesChecked = false;

            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardPreventSales__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardPreventSales__c.yesChecked = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardPreventSales__c.noChecked = false;  

            this.questions.Money_Service_Business__c.subquestions.GiftCardBsaAmlProg__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.GiftCardBsaAmlProg__c.noChecked = false;
            this.questions.Money_Service_Business__c.subquestions.GiftCardBsaAmlProg__c.yesChecked = false;

            this.questions.Money_Service_Business__c.subquestions.RegisteredFinCen__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.RegisteredFinCen__c.noChecked = false;
            this.questions.Money_Service_Business__c.subquestions.RegisteredFinCen__c.yesChecked = false;

            this.questions.Money_Service_Business__c.subquestions.InvolveNone__c.value = '';
            this.questions.Money_Service_Business__c.subquestions.InvolveNone__c.noChecked = false;
            this.questions.Money_Service_Business__c.subquestions.InvolveNone__c.yesChecked = false;
        }
        console.log('Blank Val -> ',this.questions.Intermediary__c.subquestions.ProfessionalType__c.subsubquestions.ProfessionalTypeOtherText__c.value);
    }

    handleSubRadioChange(event){
        console.log('$Radio Value: ',event.target.value);
        console.log('$Radio Field: ',event.target.name);
        console.log('$Radio Parent: ',event.target.dataset.parent);

        this.questions[event.target.dataset.parent]['subquestions'][event.target.name].value = event.target.value;

        if(this.questions[event.target.dataset.parent]['subquestions'][event.target.name].value == 'Yes'){
            this.questions[event.target.dataset.parent]['subquestions'][event.target.name].yesChecked = true;
            this.questions[event.target.dataset.parent]['subquestions'][event.target.name].noChecked = false;

            //Handle sub sub visibility
            if(event.target.name == 'PaymentsThroughAccounts__c'){
                this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.showSubSubquestions = true;
            }
            if(event.target.name == 'BusinessTypeRestrictions__c'){
                this.questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.showSubSubquestions = true;
            }
            if(event.target.name == 'ReplenishAtmCash__c'){
                this.questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.showSubSubquestions = true;
            }

        }else{
            this.questions[event.target.dataset.parent]['subquestions'][event.target.name].yesChecked = false;
            this.questions[event.target.dataset.parent]['subquestions'][event.target.name].noChecked = true;

            //Handle sub sub visibility
            if(event.target.name == 'PaymentsThroughAccounts__c'){
                this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.showSubSubquestions = false;
                this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.subsubquestions.PaymentsHowProcessed__c.value = "";
            }
            if(event.target.name == 'BusinessTypeRestrictions__c'){
                this.questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.showSubSubquestions = false;
                this.questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.subsubquestions.BusinessTypeRestrictionsText__c.value = "";
            }
            if(event.target.name == 'ReplenishAtmCash__c'){
                this.questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.showSubSubquestions = false;
                this.questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.subsubquestions.SourceOfAtmCash__c.value = "";
            }
        }

        console.log(`%c${event.target.name}:` , 'color: blue;', JSON.stringify(this.questions[event.target.dataset.parent]['subquestions'][event.target.name], null, 2));
    }

    handleSubSubRadioChange(event){
        console.log('$Radio Value: ',event.target.value);
        console.log('$Radio Field: ',event.target.name);
        console.log('$Radio Parent: ',event.target.dataset.parent);
        console.log('$Radio grandparent: ',event.target.dataset.grandparent);

        // this.questions[event.target.dataset.parent]['subquestions'][event.target.name].value = event.target.value;
        this.questions[event.target.dataset.grandparent]['subquestions'][event.target.dataset.parent]['subsubquestions'][event.target.name].value = event.target.value;
        // console.log(`%c${event.target.dataset.field}:` , 'color: blue;', JSON.stringify(this.questions[event.target.dataset.grandparent]['subquestions'][event.target.dataset.parent]['subsubquestions'][event.target.dataset.field], null, 2));

        if(this.questions[event.target.dataset.grandparent]['subquestions'][event.target.dataset.parent]['subsubquestions'][event.target.name].value == 'Yes'){
            this.questions[event.target.dataset.grandparent]['subquestions'][event.target.dataset.parent]['subsubquestions'][event.target.name].yesChecked = true;
            this.questions[event.target.dataset.grandparent]['subquestions'][event.target.dataset.parent]['subsubquestions'][event.target.name].noChecked = false;

            if(event.target.name == 'SecuritiesInvolveSecurities__c'){
                this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.showSubSubSubquestions = true;
            }
            if(event.target.name == 'TransmitMoneyNonUsLocations__c'){
                this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.showSubSubSubquestions = true;
            }
            if(event.target.name == 'TransmitMoneyCvc__c'){
                this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.showSubSubSubquestions = true;
            }
            if(event.target.name == 'GiftCardExceedDailyMax__c'){
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.showSubSubSubquestions = true;
            }
            if(event.target.name == 'GiftCardNetworkBranded__c'){
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.showSubSubSubquestions = true;
            }
        }else{
            this.questions[event.target.dataset.grandparent]['subquestions'][event.target.dataset.parent]['subsubquestions'][event.target.name].yesChecked = false;
            this.questions[event.target.dataset.grandparent]['subquestions'][event.target.dataset.parent]['subsubquestions'][event.target.name].noChecked = true;

            if(event.target.name == 'SecuritiesInvolveSecurities__c'){
                this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.showSubSubSubquestions = false;
                this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesProductTypes__c.value = "";
                this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.value = "";                
                this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.yesChecked = false;
                this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.noChecked = false;
                this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.showSubSubSubSubquestions = false;
                this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.subsubsubsubquestions.SecuritiesCountriesText__c.value = "";
                this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesServiceTypes__c.value = "";

            }
            if(event.target.name == 'TransmitMoneyNonUsLocations__c'){
                this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.showSubSubSubquestions = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.subsubsubquestions.TransmitForeignCountriesText__c.value = '';

            }
            if(event.target.name == 'TransmitMoneyCvc__c'){
                this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.showSubSubSubquestions = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.subsubsubquestions.TransmitMoneyActivities__c.value = '';
            }
            if(event.target.name == 'GiftCardExceedDailyMax__c'){
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.showSubSubSubquestions = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.value = '';
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.noChecked = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.yesChecked = false;
            }
            if(event.target.name == 'GiftCardNetworkBranded__c'){
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.showSubSubSubquestions = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.value = '';
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.yesChecked = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.noChecked = false;  

                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.value = '';
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestions = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestionsYesNo = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.noChecked = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.yesChecked = false;
                
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.value = '';
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.noChecked = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.yesChecked = false;

                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.value = '';
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.noChecked = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.yesChecked = false;

                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.value = '';
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.noChecked = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.yesChecked = false;

                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.value = '';
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.noChecked = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.yesChecked = false;           
            }
        }

        console.log(`%c${event.target.name}:` , 'color: blue;', JSON.stringify(this.questions[event.target.dataset.grandparent]['subquestions'][event.target.dataset.parent]['subsubquestions'][event.target.name], null, 2));
    }

    handleSubSubSubRadioChange(event){
        console.log('$Radio Value: ',event.target.value);
        console.log('$Radio Field: ',event.target.name);
        console.log('$Radio Parent: ',event.target.dataset.parent);
        console.log('$Radio grandparent: ',event.target.dataset.grandparent);
        console.log('$Radio greatgrandparent: ',event.target.dataset.greatgrandparent);

        this.questions[event.target.dataset.greatgrandparent]['subquestions'][event.target.dataset.grandparent]['subsubquestions'][event.target.dataset.parent]['subsubsubquestions'][event.target.name].value = event.target.value;
        // this.questions[event.target.dataset.grandparent]['subquestions'][event.target.dataset.parent]['subsubquestions'][event.target.name].value = event.target.value;

        if(this.questions[event.target.dataset.greatgrandparent]['subquestions'][event.target.dataset.grandparent]['subsubquestions'][event.target.dataset.parent]['subsubsubquestions'][event.target.name].value == 'Yes'){
            this.questions[event.target.dataset.greatgrandparent]['subquestions'][event.target.dataset.grandparent]['subsubquestions'][event.target.dataset.parent]['subsubsubquestions'][event.target.name].yesChecked = true;
            this.questions[event.target.dataset.greatgrandparent]['subquestions'][event.target.dataset.grandparent]['subsubquestions'][event.target.dataset.parent]['subsubsubquestions'][event.target.name].noChecked = false;
            if(event.target.name == 'SecuritiesInvestFundsInternationally__c'){
                this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.showSubSubSubSubquestions = true;
            }
            if(event.target.name == 'GiftCardAccessDailyMax__c'){
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestions = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestionsYesNo = true;

                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.value = '';
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.noChecked = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.yesChecked = false;

                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.value = '';
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.noChecked = false;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.yesChecked = false;
            }
        }else{
            this.questions[event.target.dataset.greatgrandparent]['subquestions'][event.target.dataset.grandparent]['subsubquestions'][event.target.dataset.parent]['subsubsubquestions'][event.target.name].yesChecked = false;
            this.questions[event.target.dataset.greatgrandparent]['subquestions'][event.target.dataset.grandparent]['subsubquestions'][event.target.dataset.parent]['subsubsubquestions'][event.target.name].noChecked = true;
            if(event.target.name == 'SecuritiesInvestFundsInternationally__c'){
                this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.showSubSubSubSubquestions = false;
                this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.subsubsubsubquestions.SecuritiesCountriesText__c.value = "";                
            }
            if(event.target.name == 'GiftCardAccessDailyMax__c'){
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestions = true;
                this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestionsYesNo = true;
            }
        }

        console.log(`%c${event.target.name}:` , 'color: blue;', JSON.stringify(this.questions[event.target.dataset.greatgrandparent]['subquestions'][event.target.dataset.grandparent]['subsubquestions'][event.target.dataset.parent]['subsubsubquestions'][event.target.name], null, 2));
    }
    

    handleSubSubSubSubRadioChange(event){
        console.log('$Radio Value: ',event.target.value);
        console.log('$Radio Field: ',event.target.name);
        console.log('$Radio Parent: ',event.target.dataset.parent);
        console.log('$Radio grandparent: ',event.target.dataset.grandparent);
        console.log('$Radio greatgrandparent: ',event.target.dataset.greatgrandparent);
        console.log('$Radio greatgreatgrandparent: ',event.target.dataset.greatgreatgrandparent);

        this.questions[event.target.dataset.greatgreatgrandparent]['subquestions'][event.target.dataset.greatgrandparent]['subsubquestions'][event.target.dataset.grandparent]['subsubsubquestions'][event.target.dataset.parent]['subsubsubsubquestions'][event.target.name].value = event.target.value;

        if(this.questions[event.target.dataset.greatgreatgrandparent]['subquestions'][event.target.dataset.greatgrandparent]['subsubquestions'][event.target.dataset.grandparent]['subsubsubquestions'][event.target.dataset.parent]['subsubsubsubquestions'][event.target.name].value == 'Yes'){
            this.questions[event.target.dataset.greatgreatgrandparent]['subquestions'][event.target.dataset.greatgrandparent]['subsubquestions'][event.target.dataset.grandparent]['subsubsubquestions'][event.target.dataset.parent]['subsubsubsubquestions'][event.target.name].yesChecked = true;
            this.questions[event.target.dataset.greatgreatgrandparent]['subquestions'][event.target.dataset.greatgrandparent]['subsubquestions'][event.target.dataset.grandparent]['subsubsubquestions'][event.target.dataset.parent]['subsubsubsubquestions'][event.target.name].noChecked = false;
        }else{
            this.questions[event.target.dataset.greatgreatgrandparent]['subquestions'][event.target.dataset.greatgrandparent]['subsubquestions'][event.target.dataset.grandparent]['subsubsubquestions'][event.target.dataset.parent]['subsubsubsubquestions'][event.target.name].yesChecked = false;
            this.questions[event.target.dataset.greatgreatgrandparent]['subquestions'][event.target.dataset.greatgrandparent]['subsubquestions'][event.target.dataset.grandparent]['subsubsubquestions'][event.target.dataset.parent]['subsubsubsubquestions'][event.target.name].noChecked = true;
        }

        console.log(`%c${event.target.name}:` , 'color: blue;', JSON.stringify(this.questions[event.target.dataset.greatgreatgrandparent]['subquestions'][event.target.dataset.greatgrandparent]['subsubquestions'][event.target.dataset.grandparent]['subsubsubquestions'][event.target.dataset.parent]['subsubsubsubquestions'][event.target.name], null, 2));
    }

    handlePicklistChange(event){
        console.log('$Picklist Value: ',event.detail.value);
        console.log('$Picklist Field: ',event.target.dataset.field);
        console.log('$Radio Parent: ',event.target.dataset.parent);
        const value = event.detail.value;
        const selectedValues = value ? value.split(';') : [];

        if (event.target.dataset.field === 'PaymentServices__c') {
            const showOther = selectedValues.includes('Other');
            // console.log('showOther - ',showOther);
            this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.showSubSubquestions = showOther;

            if (!showOther) {
                    this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.subsubquestions.PaymentServicesOtherText__c.value = "";
                //    console.log('showOther not - ',showOther);
                }
        }

        if (event.target.dataset.field === 'ProfessionalType__c') {
            const showOther = selectedValues.includes('Other');
            this.questions.Intermediary__c.subquestions.ProfessionalType__c.showSubSubquestions = showOther;

            if (!showOther) {
                this.questions.Intermediary__c.subquestions.ProfessionalType__c.subsubquestions.ProfessionalTypeOtherText__c.value = "";
            }
        }

        // if(event.target.dataset.field == 'PaymentServices__c' && event.detail.value == 'Other'){
        //     this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.showSubSubquestions = true;
        // }else{
        //     this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.showSubSubquestions = false;
        //     this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.subsubquestions.PaymentServicesOtherText__c.value = "";
        // }

        // if(event.target.dataset.field == 'ProfessionalType__c' && event.detail.value == 'Other'){
        //     this.questions.Intermediary__c.subquestions.ProfessionalType__c.showSubSubquestions = true;
        // }else{
        //     console.log('In PF -----');
        //     this.questions.Intermediary__c.subquestions.ProfessionalType__c.showSubSubquestions = false;
        //     this.questions.Intermediary__c.subquestions.ProfessionalType__c.subsubquestions.ProfessionalTypeOtherText__c.value = "";
        // }

        this.questions[event.target.dataset.parent]['subquestions'][event.target.dataset.field].value = event.detail.value;
        console.log(`%c${event.target.dataset.field}:` , 'color: blue;', JSON.stringify(this.questions[event.target.dataset.parent]['subquestions'][event.target.dataset.field], null, 2));
    }

    handleCheckboxChange(event){
        console.log('$Picklist Value: ',event.detail.value);
        console.log('$Picklist Field: ',event.target.dataset.field);
        console.log('$Radio Parent: ',event.target.dataset.parent);

        this.questions[event.target.dataset.parent]['subquestions'][event.target.dataset.field].value = event.detail.value;

        if(event.target.dataset.field == 'InvolveSecurities__c' || event.target.dataset.field == 'InvolveInsurance__c' || event.target.dataset.field == 'InvolvePreciousMetals__c' || event.target.dataset.field == 'InvolveVehicleSales__c' || event.target.dataset.field == 'SecuritiesHowBusinessRegistered__c' || event.target.dataset.field == 'SecuritiesInvolveSecurities__c'){
            if(event.detail.value){
                this.questions.Non_Bank_Financial_Institution__c.subquestions[event.target.dataset.field].showSubSubquestions = true;
            }else{
                this.questions.Non_Bank_Financial_Institution__c.subquestions[event.target.dataset.field].showSubSubquestions = false;

                if(event.target.dataset.field == 'InvolveSecurities__c'){
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesFinancialInstitution__c.value = "";
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesHowBusinessRegistered__c.value = "";

                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.value = "";
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.noChecked = false;
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.yesChecked = false;     

                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.showSubSubSubquestions = false;     
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesProductTypes__c.value = "";

                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.value = "";
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.yesChecked = false;
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.noChecked = false;
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.showSubSubSubSubquestions = false;
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.subsubsubsubquestions.SecuritiesCountriesText__c.value = "";
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesServiceTypes__c.value = "";  
                }
                if(event.target.dataset.field == 'InvolveInsurance__c'){
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.subsubquestions.InvolveInsuranceStateRegIns__c.value = "";
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.subsubquestions.InvolveInsuranceStateRegIns__c.noChecked = false;
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.subsubquestions.InvolveInsuranceStateRegIns__c.yesChecked = false; 
                }
                if(event.target.dataset.field == 'InvolvePreciousMetals__c'){
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsBuy50k__c.value = "";
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsBuy50k__c.noChecked = false;
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsBuy50k__c.yesChecked = false;

                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsSell50k__c.value = "";
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsSell50k__c.noChecked = false;
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsSell50k__c.yesChecked = false;
                }
                if(event.target.dataset.field == 'InvolveVehicleSales__c'){
                    this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveVehicleSales__c.subsubquestions.InvolveVehicleTypes__c.value = "";
                }   
            }
        }
        if(event.target.dataset.field == 'InvolveGiftCards__c' || event.target.dataset.field == 'InvolveTransmitMoney__c' || event.target.dataset.field == 'InvolveCurrencyExchange__c' || event.target.dataset.field == 'InvolveCashChecks__c' || event.target.dataset.field == 'InvolveMoneyOrders__c'){
            if(event.detail.value){
                this.questions.Money_Service_Business__c.subquestions[event.target.dataset.field].showSubSubquestions = true;
            }else{
                this.questions.Money_Service_Business__c.subquestions[event.target.dataset.field].showSubSubquestions = false;

                if(event.target.dataset.field == 'InvolveCurrencyExchange__c'){
                    this.questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.subsubquestions.Involve_Curency_Exchange_Agent__c.value = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.subsubquestions.Involve_Curency_Exchange_Principal__c.value = false;    
                }
                if(event.target.dataset.field == 'InvolveCashChecks__c'){
                    this.questions.Money_Service_Business__c.subquestions.InvolveCashChecks__c.subsubquestions.InvolveCheckTypes__c.value = '';    
                }
                if(event.target.dataset.field == 'InvolveMoneyOrders__c'){
                    this.questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.subsubquestions.Involve_Money_Orders_Agent__c.value = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.subsubquestions.Involve_Money_Orders_Principal__c.value = false;    
                }
                if(event.target.dataset.field == 'InvolveTransmitMoney__c'){
                    this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.Transmit_Money_Agent__c.value = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.Transmit_Money_Principal__c.value = false; 

                    this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.value = '';
                    this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.showSubSubSubquestions = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.yesChecked = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.noChecked = false;            
                    this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.subsubsubquestions.TransmitForeignCountriesText__c.value = '';

                    this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyTypes__c.value = '';

                    this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.showSubSubSubquestions = false; 
                    this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.value = '';
                    this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.yesChecked = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.noChecked = false;  
                    this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.subsubsubquestions.TransmitMoneyActivities__c.value = '';
                }
                if(event.target.dataset.field == 'InvolveGiftCards__c'){
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.Gift_Card_Agent__c.value = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.Gift_Card_Principal__c.value = false;
                    
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.showSubSubSubquestions = false; 
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.value = '';
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.yesChecked = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.noChecked = false;  

                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.value = '';
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.noChecked = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.yesChecked = false;

                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.showSubSubSubquestions = false; 
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.value = '';
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.yesChecked = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.noChecked = false;  

                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.value = '';
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestions = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestionsYesNo = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.noChecked = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.yesChecked = false;
                    
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.value = '';
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.noChecked = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.yesChecked = false;

                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.value = '';
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.noChecked = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.yesChecked = false;

                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.value = '';
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.noChecked = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.yesChecked = false;

                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.value = '';
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.noChecked = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.yesChecked = false;

                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardPreventSales__c.value = '';
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardPreventSales__c.yesChecked = false;
                    this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardPreventSales__c.noChecked = false; 
                }
            }
        }
        console.log(`%c${event.target.dataset.field}:` , 'color: blue;', JSON.stringify(this.questions[event.target.dataset.parent]['subquestions'][event.target.dataset.field], null, 2));
        
    }

    handleSubSubPicklistChange(event){
        console.log('Enter In handleSubSubPicklistChange------');
        console.log('$Picklist Value: ',event.detail.value);
        console.log('$Picklist Field: ',event.target.dataset.field);
        console.log('$Radio Parent: ',event.target.dataset.parent);
        console.log('$Radio grandparent: ',event.target.dataset.grandparent);

        this.questions[event.target.dataset.grandparent]['subquestions'][event.target.dataset.parent]['subsubquestions'][event.target.dataset.field].value = event.detail.value;
        console.log(`%c${event.target.dataset.field}:` , 'color: blue;', JSON.stringify(this.questions[event.target.dataset.grandparent]['subquestions'][event.target.dataset.parent]['subsubquestions'][event.target.dataset.field], null, 2));
    }

    handleSubSubSubPicklistChange(event){
        console.log('$Picklist Value: ',event.detail.value);
        console.log('$Picklist Field: ',event.target.dataset.field);
        console.log('$Radio Parent: ',event.target.dataset.parent);
        console.log('$Radio grandparent: ',event.target.dataset.grandparent);
        console.log('$Radio greatgrandparent: ',event.target.dataset.greatgrandparent);

        this.questions[event.target.dataset.greatgrandparent]['subquestions'][event.target.dataset.grandparent]['subsubquestions'][event.target.dataset.parent]['subsubsubquestions'][event.target.dataset.field].value = event.detail.value;
        console.log(`%c${event.target.dataset.field}:` , 'color: blue;', JSON.stringify(this.questions[event.target.dataset.greatgrandparent]['subquestions'][event.target.dataset.grandparent]['subsubquestions'][event.target.dataset.parent]['subsubsubquestions'][event.target.dataset.field], null, 2));
    }

    handleSubSubTextChange(event){
        console.log('$Picklist Value: ',event.detail.value);
        console.log('$Picklist Field: ',event.target.dataset.field);
        console.log('$Radio Parent: ',event.target.dataset.parent);
        console.log('$Radio grandparent: ',event.target.dataset.grandparent);

        this.questions[event.target.dataset.grandparent]['subquestions'][event.target.dataset.parent]['subsubquestions'][event.target.dataset.field].value = event.detail.value;
        console.log(`%c${event.target.dataset.field}:` , 'color: blue;', JSON.stringify(this.questions[event.target.dataset.grandparent]['subquestions'][event.target.dataset.parent]['subsubquestions'][event.target.dataset.field], null, 2));
    }

    handleSubSubSubSubTextChange(event){
        console.log('$Picklist Value: ',event.detail.value);
        console.log('$Picklist Field: ',event.target.dataset.field);
        console.log('$Radio Parent: ',event.target.dataset.parent);
        console.log('$Radio grandparent: ',event.target.dataset.grandparent);
        console.log('$Radio greatgrandparent: ',event.target.dataset.greatgrandparent);
        console.log('$Radio greatgreatgrandparent: ',event.target.dataset.greatgreatgrandparent);

        this.questions[event.target.dataset.greatgreatgrandparent]['subquestions'][event.target.dataset.greatgrandparent]['subsubquestions'][event.target.dataset.grandparent]['subsubsubquestions'][event.target.dataset.parent]['subsubsubsubquestions'][event.target.dataset.field].value = event.detail.value;
        console.log(`%c${event.target.dataset.field}:` , 'color: blue;', JSON.stringify(this.questions[event.target.dataset.greatgreatgrandparent]['subquestions'][event.target.dataset.greatgrandparent]['subsubquestions'][event.target.dataset.grandparent]['subsubsubquestions'][event.target.dataset.parent]['subsubsubsubquestions'][event.target.dataset.field], null, 2));
    }

    handleNextPage() {

        let isValid = true;

        if(!this.questions.Internet_Gambling__c.value){
            isValid = false;
        }
        if(!this.questions.Marijuana_Business__c.value){
            isValid = false;
        }else{
            if(this.questions.Marijuana_Business__c.showSubquestions){
                if(!this.questions.Marijuana_Business__c.subquestions.MarijuanaLicensed__c.value){
                    isValid = false;
                }
                if(!this.questions.Marijuana_Business__c.subquestions.MarijuanaPercentage__c.value){
                    isValid = false;
                }
                if(!this.questions.Marijuana_Business__c.subquestions.MarijuanaActivity__c.value){
                    isValid = false;
                }
            }
        }
        if(!this.questions.Intermediary__c.value){
            isValid = false;
        }else{
            if(this.questions.Intermediary__c.showSubquestions){
                if(!this.questions.Intermediary__c.subquestions.ProfessionalType__c.value){
                    isValid = false;
                }
                if(this.questions.Intermediary__c.subquestions.ProfessionalType__c.showSubSubquestions){
                    if(!this.questions.Intermediary__c.subquestions.ProfessionalType__c.subsubquestions.ProfessionalTypeOtherText__c.value){
                        isValid = false;
                    }
                }
                if(!this.questions.Intermediary__c.subquestions.ServicesTypes__c.value){
                    isValid = false;
                }
                if(!this.questions.Intermediary__c.subquestions.ProfessionalOthersUsing__c.value){
                    isValid = false;
                }
            }
        }
        if(!this.questions.Courier_Services__c.value){
            isValid = false;
        }
        if(!this.questions.Third_Party_Payment_Processor__c.value){
            isValid = false;
        }else{
            if(this.questions.Third_Party_Payment_Processor__c.showSubquestions){
                if(!this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.value){
                    isValid = false;
                }
                if(this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.showSubSubquestions){
                    if(!this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentServices__c.subsubquestions.PaymentServicesOtherText__c.value){
                        isValid = false;
                    }
                }
                if(!this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.value){
                    isValid = false;
                }
                if(this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.showSubSubquestions){
                    if(!this.questions.Third_Party_Payment_Processor__c.subquestions.PaymentsThroughAccounts__c.subsubquestions.PaymentsHowProcessed__c.value){
                        isValid = false;
                    }
                }
                if(!this.questions.Third_Party_Payment_Processor__c.subquestions.CreateChecksRemotely__c.value){
                    isValid = false;
                }
                if(!this.questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.value){
                    isValid = false;
                }
                if(this.questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.showSubSubquestions){
                    if(!this.questions.Third_Party_Payment_Processor__c.subquestions.BusinessTypeRestrictions__c.subsubquestions.BusinessTypeRestrictionsText__c.value){
                        isValid = false;
                    }
                }
            }
        }
        if(!this.questions.ATM_Business__c.value){
            isValid = false;
        }else{
            if(this.questions.ATM_Business__c.showSubquestions){
                if(!this.questions.ATM_Business__c.subquestions.NumberOfAtm__c.value){
                    isValid = false;
                }
                if(!this.questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.value){
                    isValid = false;
                }
                if(this.questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.showSubSubquestions){
                    if(!this.questions.ATM_Business__c.subquestions.ReplenishAtmCash__c.subsubquestions.SourceOfAtmCash__c.value){
                        isValid = false;
                    }
                }
                if(!this.questions.ATM_Business__c.subquestions.AtmMaxHolding__c.value){
                    isValid = false;
                }
                if(!this.questions.ATM_Business__c.subquestions.AtmDenomination__c.value){
                    isValid = false;
                }
                if(!this.questions.ATM_Business__c.subquestions.PrivateAtmType__c.value){
                    isValid = false;
                }
            }
        }
        if(!this.questions.Non_Bank_Financial_Institution__c.value){
            isValid = false;
        }else{
            if(this.questions.Non_Bank_Financial_Institution__c.showSubquestions){
                if(this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.showSubSubquestions){
                    if(!this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesFinancialInstitution__c.value){
                        isValid = false;
                    }
                    if(!this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesHowBusinessRegistered__c.value){
                        isValid = false;
                    }
                    if(!this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.value){
                        isValid = false;
                    }
                    if(this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.showSubSubSubquestions){
                        if(!this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesProductTypes__c.value){
                            isValid = false;
                        }
                        if(!this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.value){
                            isValid = false;
                        }
                        if(this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.showSubSubSubSubquestions){
                            if(!this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesInvestFundsInternationally__c.subsubsubsubquestions.SecuritiesCountriesText__c.value){
                                isValid = false;
                            }
                        }
                        if(!this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveSecurities__c.subsubquestions.SecuritiesInvolveSecurities__c.subsubsubquestions.SecuritiesServiceTypes__c.value){
                            isValid = false;
                        }
                    }
                }
                if(this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.showSubSubquestions){
                    if(!this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveInsurance__c.subsubquestions.InvolveInsuranceStateRegIns__c.value){
                        isValid = false;
                    }
                }
                if(this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.showSubSubquestions){
                    if(!this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsBuy50k__c.value){
                        isValid = false;
                    }
                    if(!this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolvePreciousMetals__c.subsubquestions.InvolvePreciousMetalsSell50k__c.value){
                        isValid = false;
                    }
                }
                if(this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveVehicleSales__c.showSubSubquestions){
                    if(!this.questions.Non_Bank_Financial_Institution__c.subquestions.InvolveVehicleSales__c.subsubquestions.InvolveVehicleTypes__c.value){
                        isValid = false;
                    }
                }
                if(!this.questions.Non_Bank_Financial_Institution__c.subquestions.DocBsaAmlProgram__c.value){
                    isValid = false;
                }
                
            }
        }
        if(!this.questions.Money_Service_Business__c.value){
            isValid = false;
        }else{
            if(this.questions.Money_Service_Business__c.showSubquestions){
                if(this.questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.showSubSubquestions){
                    if(!this.questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.subsubquestions.Involve_Curency_Exchange_Agent__c.value && !this.questions.Money_Service_Business__c.subquestions.InvolveCurrencyExchange__c.subsubquestions.Involve_Curency_Exchange_Principal__c.value){
                        isValid = false;
                    }
                }
                if(this.questions.Money_Service_Business__c.subquestions.InvolveCashChecks__c.showSubSubquestions){
                    if(!this.questions.Money_Service_Business__c.subquestions.InvolveCashChecks__c.subsubquestions.InvolveCheckTypes__c.value){
                        isValid = false;
                    }
                }
                if(this.questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.showSubSubquestions){
                    if(!this.questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.subsubquestions.Involve_Money_Orders_Agent__c.value && !this.questions.Money_Service_Business__c.subquestions.InvolveMoneyOrders__c.subsubquestions.Involve_Money_Orders_Principal__c.value){
                        isValid = false;
                    }
                }
                if(this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.showSubSubquestions){
                    if(!this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.Transmit_Money_Agent__c.value && !this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.Transmit_Money_Principal__c.value){
                        isValid = false;
                    }
                    if(!this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.value){
                        isValid = false;
                    }
                    if(this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.showSubSubSubquestions){
                        if(!this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyNonUsLocations__c.subsubsubquestions.TransmitForeignCountriesText__c.value){
                            isValid = false;
                        }
                    }
                    if(!this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyTypes__c.value){
                        isValid = false;
                    }
                    if(!this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.value){
                        isValid = false;
                    }
                    if(this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.showSubSubSubquestions){
                        if(!this.questions.Money_Service_Business__c.subquestions.InvolveTransmitMoney__c.subsubquestions.TransmitMoneyCvc__c.subsubsubquestions.TransmitMoneyActivities__c.value){
                            isValid = false;
                        }
                    }
                }
                if(this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.showSubSubquestions){
                    if(!this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.Gift_Card_Agent__c.value && !this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.Gift_Card_Principal__c.value){
                        isValid = false;
                    }
                    if(!this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.value){
                        isValid = false;
                    }
                    if(this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.showSubSubSubquestions){
                        if(!this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardExceedDailyMax__c.subsubsubquestions.GiftCardActivationProcess__c.value){
                            isValid = false;
                        }
                    }
                    if(!this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.value){
                        isValid = false;
                    }
                    if(this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.showSubSubSubquestions){
                        if(!this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.value){
                            isValid = false;
                        }
                        if(this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestionsYesNo){
                            
                            if(this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.showSubSubSubSubquestions){
                                if(!this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardReloaded__c.value){
                                    isValid = false;
                                }
                                if(!this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFunds__c.value){
                                    isValid = false;
                                }
                            }
                            if(!this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardTransferFundsInternationally__c.value){
                                isValid = false;
                            }
                            if(!this.questions.Money_Service_Business__c.subquestions.InvolveGiftCards__c.subsubquestions.GiftCardNetworkBranded__c.subsubsubquestions.GiftCardAccessDailyMax__c.subsubsubsubquestions.GiftCardRequireCustomerId__c.value){
                                isValid = false;
                            }
                        }
                    }
                    
                }
                if(!this.questions.Money_Service_Business__c.subquestions.GiftCardBsaAmlProg__c.value){
                    isValid = false;
                }
                if(!this.questions.Money_Service_Business__c.subquestions.RegisteredFinCen__c.value){
                    isValid = false;
                }
                
            }
        }
        if(isValid){
            this.omniApplyCallResp({DiligenceQuestions: this.questions});
            this.omniNextStep();
        }else{
            this.showMissingFieldsError();
        }
    }
    handlePreviousPage() {
        this.omniPrevStep();
    }

    showMissingFieldsError() {
        const evt = new ShowToastEvent({
            title: 'Error',
            message: 'Fields cannot be blank',
            variant: 'error',
        });
        this.dispatchEvent(evt);
    }
}