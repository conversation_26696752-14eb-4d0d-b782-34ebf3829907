<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Omni Data Transformation Item Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>OmniDataTransformationId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>DefaultValue</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FilterDataType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FilterGroup</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FilterOperator</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FilterValue</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FormulaConverted</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FormulaExpression</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FormulaResultPath</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FormulaSequence</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>GlobalKey</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InputObjectName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InputObjectQuerySequence</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsDisabled</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsRequiredForUpsert</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsUpsertKey</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LinkedFieldName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LinkedObjectSequence</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LookupByFieldName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LookupObjectName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LookupReturnedFieldName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MigrationAttribute</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MigrationCategory</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MigrationGroup</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MigrationKey</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MigrationPattern</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MigrationProcess</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MigrationType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MigrationValue</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OutputCreationSequence</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OutputFieldFormat</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OutputObjectName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TransformValueMappings</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OutputFieldName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InputFieldName</field>
            </layoutItems>
        </layoutColumns>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
