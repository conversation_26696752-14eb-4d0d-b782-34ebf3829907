<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Account.Assign_Agent_to_Office</value>
                            <visibilityRule>
                                <criteria>
                                    <leftValue>{!Record.RecordType.Name}</leftValue>
                                    <operator>NE</operator>
                                    <rightValue>Personal Referrals</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>required</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Name</fieldItem>
                <identifier>RecordNameField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Mortgage_Preferred_Name__c</fieldItem>
                <identifier>RecordMortgage_Preferred_Name_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 OR 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Real Estate Agent</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Mortgage Referrer</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Phone</fieldItem>
                <identifier>RecordPhoneField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.PersonEmail</fieldItem>
                <identifier>RecordPersonEmailField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Alternate_Email__c</fieldItem>
                <identifier>RecordAlternate_Email_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Real Estate Agent</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Website</fieldItem>
                <identifier>RecordWebsiteField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Mortgage Referrer</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Mortgage_Referral_Type__c</fieldItem>
                <identifier>RecordMortgage_Referrer_Type__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Personal Referrals</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-3af5a22d-a94f-4d9b-b69f-448315fcc6c2</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ParentId</fieldItem>
                <identifier>RecordParentIdField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Mortgage Referrer</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Real_Estate_License__pc</fieldItem>
                <identifier>RecordReal_Estate_License_pcField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Real Estate Agent</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>readonly</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Agent_Office__c</fieldItem>
                <identifier>RecordAgent_Office_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Real Estate Agent</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.License_Anniversary_Date__c</fieldItem>
                <identifier>RecordLicense_Anniversary_Date_cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Real Estate Agent</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Birthdate_MM_DD__pc</fieldItem>
                <identifier>RecordBirthdate_MM_DD_pcField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Real Estate Agent</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentName>flexipage:blankSpace</componentName>
                <identifier>flexipage_blankSpace</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Comments_Notes__c</fieldItem>
                <identifier>RecordComments_Notes_cField</identifier>
            </fieldInstance>
        </itemInstances>
        <name>Facet-57c43b91-2793-4d25-968e-866220fe83f3</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-3af5a22d-a94f-4d9b-b69f-448315fcc6c2</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-57c43b91-2793-4d25-968e-866220fe83f3</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column2</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-0355401c-1e02-4d00-96db-c68b03c96e75</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.ShippingAddress</fieldItem>
                <identifier>RecordShippingAddressField</identifier>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Mortgage_County__c</fieldItem>
                <identifier>RecordMortgage_County__cField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Mortgage Referrer</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.Other_County__c</fieldItem>
                <identifier>RecordMortgage_Other_County_Name_cField</identifier>
                <visibilityRule>
                    <booleanFilter>1 AND 2</booleanFilter>
                    <criteria>
                        <leftValue>{!Record.Mortgage_County__c}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Other</rightValue>
                    </criteria>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>NE</operator>
                        <rightValue>Mortgage Referrer</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-9ad82cd8-8a88-461a-85d8-2ef1c94de618</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <fieldInstance>
                <fieldInstanceProperties>
                    <name>uiBehavior</name>
                    <value>none</value>
                </fieldInstanceProperties>
                <fieldItem>Record.BillingAddress</fieldItem>
                <identifier>RecordBillingAddressField</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Real Estate Agent</rightValue>
                    </criteria>
                </visibilityRule>
            </fieldInstance>
        </itemInstances>
        <name>Facet-92751699-230f-46de-b765-66b5748a1fe5</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-9ad82cd8-8a88-461a-85d8-2ef1c94de618</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column3</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-92751699-230f-46de-b765-66b5748a1fe5</value>
                </componentInstanceProperties>
                <componentName>flexipage:column</componentName>
                <identifier>flexipage_column5</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-371120e2-c596-4892-9aa8-919acf66ab2a</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-0355401c-1e02-4d00-96db-c68b03c96e75</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-371120e2-c596-4892-9aa8-919acf66ab2a</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>horizontalAlignment</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Address</value>
                </componentInstanceProperties>
                <componentName>flexipage:fieldSection</componentName>
                <identifier>flexipage_fieldSection2</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>displayOption</name>
                    <value>BOTH</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_merge:mergeCandidatesPreviewCard</componentName>
                <identifier>runtime_sales_merge_mergeCandidatesPreviewCard</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>showLegacyActivityComposer</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentName>runtime_sales_activities:activityPanel</componentName>
                <identifier>runtime_sales_activities_activityPanel2</identifier>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>New</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>10</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>Lead_Account_Relationships__r</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>Mortgage_Lead__c</value>
                        </valueListItems>
                        <valueListItems>
                            <value>Mortgage_Lead_Role__c</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Related Leads</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList2</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Real Estate Agent</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>AddRelation</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>adminFilters</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>maxRecordsToDisplay</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>parentFieldApiName</name>
                    <value>Account.Id</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListApiName</name>
                    <value>AccountContactRelations</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListDisplayType</name>
                    <value>ADVGRID</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListFieldAliases</name>
                    <valueList>
                        <valueListItems>
                            <value>CONTACT.FULL_NAME</value>
                        </valueListItems>
                        <valueListItems>
                            <value>ACCCONRELATION.ROLES</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CONTACT.EMAIL</value>
                        </valueListItems>
                        <valueListItems>
                            <value>CONTACT.PHONE1</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>relatedListLabel</name>
                    <value>Office Employees</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>showActionBar</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldAlias</name>
                    <value>__DEFAULT__</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>sortFieldOrder</name>
                    <value>Default</value>
                </componentInstanceProperties>
                <componentName>lst:dynamicRelatedList</componentName>
                <identifier>lst_dynamicRelatedList3</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!Record.RecordType.Name}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>Real Estate Offices</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <name>sidebar</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>RCU FSC Accounts</masterLabel>
    <sobjectType>Account</sobjectType>
    <template>
        <name>flexipage:recordHomeTemplateDesktop</name>
        <properties>
            <name>actionNames</name>
            <valueList>
                <valueListItems>
                    <value>Edit</value>
                </valueListItems>
                <valueListItems>
                    <value>CallHighlightAction</value>
                </valueListItems>
                <valueListItems>
                    <value>Account.Assign_Agent_to_Office</value>
                </valueListItems>
                <valueListItems>
                    <value>Delete</value>
                    <visibilityRule>
                        <criteria>
                            <leftValue>{!Record.RecordType.Name}</leftValue>
                            <operator>NE</operator>
                            <rightValue>Real Estate Offices</rightValue>
                        </criteria>
                    </visibilityRule>
                </valueListItems>
            </valueList>
        </properties>
        <properties>
            <name>enablePageActionConfig</name>
            <value>true</value>
        </properties>
    </template>
    <type>RecordPage</type>
</FlexiPage>
