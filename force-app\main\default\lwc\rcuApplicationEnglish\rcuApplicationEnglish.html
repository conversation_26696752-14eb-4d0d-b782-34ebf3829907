<template>
    <div dir={_dir}>
        <template if:true={hasErrors}>
            <lightning-layout vertical-align="stretch">
                <lightning-layout-item flexibility="auto" padding="around-small" class="slds-text-align_center slds-p-around_small" size="1">
                    <lightning-icon icon-name="utility:error" alternative-text="Error!" variant="error" size="large">
                    </lightning-icon>
                </lightning-layout-item>
                <lightning-layout-item flexibility="auto" padding="around-small" class="custom-box">
                    <p class="slds-text-heading_medium">{allCustomLabelsUtil.OmniScriptError}</p>
                    <p class="bold">{_errorMsg}</p>
                    <dl class="slds-dl_inline" if:false={_isActiveOs}>
                        <dt class="slds-dl_inline__label"><strong>{allCustomLabelsUtil.OmniScriptType}:</strong></dt>
                        <dd class="slds-dl_inline__detail">rcu</dd>
                        <dt class="slds-dl_inline__label"><strong>{allCustomLabelsUtil.OmniScriptSubType}:</strong></dt>
                        <dd class="slds-dl_inline__detail">application</dd>
                        <dt class="slds-dl_inline__label"><strong>{allCustomLabelsUtil.OmniScriptLang}:</strong></dt>
                        <dd class="slds-dl_inline__detail">English</dd>
                    </dl>
                    <p class="footer-message">{allCustomLabelsUtil.OmniScriptNotFound2}</p>
                </lightning-layout-item>
            </lightning-layout>
        </template>
        <template if:false={hasErrors}>
            <template if:false={compLoaded}>
                <div class="slds-spinner-container__wrapper">
                    <omnistudio-spinner variant="brand"
                                        alternative-text={allCustomLabelsUtil.OmniSpinnerTextLoading}
                                        size="medium">
                    </omnistudio-spinner>
                </div>
            </template>
            <template if:true={bSflValid}>
                <template if:true={firstRender}>
                    <template if:true={isLauncherVisible}>
                        <omnistudio-button label={inlineLabel}
                                            variant={inlineVariant}
                                            theme={_theme}
                                            onclick={setScriptVisibility}
                                            extraclass="slds-button_stretch">
                        </omnistudio-button>
                    </template>
                    <article class={containerClasses}
                            data-content-position={stepChartProps.position}>
                        
                        <div class={_sideContentClasses}>
                            <omnistudio-omniscript-step-chart json-def={jsonDef}
                                        json-data={jsonDef.response}
                                        if:false={jsonDef.propSetMap.hideStepChart}
                                        data-omni-key='omniscriptStepChart'
                                        props={stepChartProps}
                                        layout={layout}
                                        script-header-def={scriptHeaderDef}>
                            </omnistudio-omniscript-step-chart>
                            <template if:true={_isKbEnabledOnScript}>
                                <omnistudio-omniscript-knowledge-base knowledge-options={kbOptions}
                                                             data-omni-key='omniscriptKnowledgeBase'
                                                             layout={layout}
                                                             data-stepchart-placement={_stepChartPlacement}
                                                             omniscript-key={scriptHeaderDef.omniscriptKey}
                                                             kb-label={knowledgeLabel}>
                                </omnistudio-omniscript-knowledge-base>
                            </template>
                        </div>
                        <div data-stepborder={stepChartProps.position}
                            class={contentSldsClass}>
                            <template if:true={isPageLoading}>
                                <omnistudio-spinner variant="brand"
                                                    alternative-text={allCustomLabelsUtil.OmniSpinnerTextLoading}
                                                    extraouterclass="slds-theme_default"
                                                    message={spinnerMessage}
                                                    size="medium">
                                </omnistudio-spinner>
                            </template>
                            <template if:true={bSflAuto}>
                                <omnistudio-omniscript-save-for-later-acknowledge result={bSflResult}
                                                layout={layout}
                                                auto></omnistudio-omniscript-save-for-later-acknowledge>
                            </template>
                            <template for:each={jsonDef.children} for:item="item" for:index="idx"><omnistudio-omniscript-remote-action if:true={item.bRemoteAction} key={item.lwcId} json-def={item} data-omni-key={item.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-remote-action><omnistudio-omniscript-step if:true={item.bStep} key={item.lwcId} json-def={item} data-omni-key={item.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><template for:each={item.children} for:item="itemChild0" for:index="itemChild0Idx"><template if:true={itemChild0.bEB}>
    <omnistudio-omniscript-edit-block-wrapper key={itemChild0.lwcId} class={itemChild0.sldsCls} json-def={itemChild0} layout={layout} script-header-def={scriptHeaderDef}>
        <omnistudio-omniscript-edit-block-label if:true={itemChild0.bEditBlockLabel} slot="label" json-def={itemChild0} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} run-mode={runMode} script-header-def={scriptHeaderDef}></omnistudio-omniscript-edit-block-label>
        <template for:each={itemChild0.eleArray} for:item="itemChild0Ele" for:index="itemChild0Idx"><omnistudio-omniscript-edit-block if:true={itemChild0Ele.bEditBlock} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON} mode={itemChild0Ele.propSetMap.mode}><template for:each={itemChild0Ele.children} for:item="itemChild0EleChild0" for:index="itemChild0EleChild0Idx"><template for:each={itemChild0EleChild0.eleArray} for:item="itemChild0EleChild0Ele" for:index="itemChild0EleChild0Idx"><omnistudio-omniscript-checkbox if:true={itemChild0EleChild0Ele.bCheckbox} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-checkbox><omnistudio-omniscript-text if:true={itemChild0EleChild0Ele.bText} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-text><omnistudio-omniscript-email if:true={itemChild0EleChild0Ele.bEmail} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-email><omnistudio-omniscript-telephone if:true={itemChild0EleChild0Ele.bTelephone} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-telephone></template></template></omnistudio-omniscript-edit-block></template>
        <omnistudio-omniscript-edit-block-new if:true={itemChild0.bEditBlockNew} slot="new" json-def={itemChild0} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} run-mode={runMode} script-header-def={scriptHeaderDef}></omnistudio-omniscript-edit-block-new>
    </omnistudio-omniscript-edit-block-wrapper>
</template><template for:each={itemChild0.eleArray} for:item="itemChild0Ele" for:index="itemChild0Idx"><omnistudio-omniscript-radio if:true={itemChild0Ele.bRadio} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-radio><omnistudio-omniscript-block if:true={itemChild0Ele.bBlock} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><template for:each={itemChild0Ele.children} for:item="itemChild0EleChild0" for:index="itemChild0EleChild0Idx"><template for:each={itemChild0EleChild0.eleArray} for:item="itemChild0EleChild0Ele" for:index="itemChild0EleChild0Idx"><omnistudio-omniscript-radio if:true={itemChild0EleChild0Ele.bRadio} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-radio><omnistudio-omniscript-text-block if:true={itemChild0EleChild0Ele.bTextBlock} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-text-block><omnistudio-omniscript-custom-lwc if:true={itemChild0EleChild0Ele.bcustomlightningwebcomponent1} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-rcu-opa-selection-option if:true={itemChild0EleChild0Ele.bcustomlightningwebcomponent1} key={itemChild0EleChild0Ele.lwcId} data-omni-key={itemChild0EleChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0EleChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" selectedopavalue={jsonDef.response.selectedOPAValue} selectedopalabel={jsonDef.response.selectedOPALabel}></c-rcu-opa-selection-option></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-line-break if:true={itemChild0EleChild0Ele.bLineBreak} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-line-break><omnistudio-omniscript-text if:true={itemChild0EleChild0Ele.bText} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-text><omnistudio-omniscript-multiselect if:true={itemChild0EleChild0Ele.bMultiselect} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-multiselect><omnistudio-omniscript-select if:true={itemChild0EleChild0Ele.bSelect} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-select><omnistudio-omniscript-checkbox if:true={itemChild0EleChild0Ele.bCheckbox} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-checkbox><omnistudio-omniscript-formula if:true={itemChild0EleChild0Ele.bFormula} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-formula><omnistudio-omniscript-date if:true={itemChild0EleChild0Ele.bDate} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-date><omnistudio-omniscript-email if:true={itemChild0EleChild0Ele.bEmail} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-email><omnistudio-omniscript-telephone if:true={itemChild0EleChild0Ele.bTelephone} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-telephone><omnistudio-omniscript-currency if:true={itemChild0EleChild0Ele.bCurrency} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-currency><omnistudio-omniscript-pdf-action if:true={itemChild0EleChild0Ele.bPDFAction} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-pdf-action></template></template></omnistudio-omniscript-block><omnistudio-omniscript-text-block if:true={itemChild0Ele.bTextBlock} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-text-block><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent1} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-product-selection if:true={itemChild0Ele.bcustomlightningwebcomponent1} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input=""></c-product-selection></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-text if:true={itemChild0Ele.bText} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-text><omnistudio-omniscript-checkbox if:true={itemChild0Ele.bCheckbox} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-checkbox><omnistudio-omniscript-select if:true={itemChild0Ele.bSelect} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-select><omnistudio-omniscript-email if:true={itemChild0Ele.bEmail} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-email><omnistudio-omniscript-date if:true={itemChild0Ele.bDate} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-date><omnistudio-omniscript-telephone if:true={itemChild0Ele.bTelephone} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-telephone><omnistudio-omniscript-formula if:true={itemChild0Ele.bFormula} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-formula><omnistudio-omniscript-currency if:true={itemChild0Ele.bCurrency} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-currency><omnistudio-omniscript-number if:true={itemChild0Ele.bNumber} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-number><omnistudio-omniscript-typeahead-block if:true={itemChild0Ele.bTypeaheadBlock} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><template for:each={itemChild0Ele.children} for:item="itemChild0EleChild0" for:index="itemChild0EleChild0Idx"><template for:each={itemChild0EleChild0.eleArray} for:item="itemChild0EleChild0Ele" for:index="itemChild0EleChild0Idx"><omnistudio-omniscript-typeahead if:true={itemChild0EleChild0Ele.bTypeahead} key={itemChild0EleChild0Ele.lwcId} json-def={itemChild0EleChild0Ele} data-omni-key={itemChild0EleChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}></omnistudio-omniscript-typeahead></template></template></omnistudio-omniscript-typeahead-block><omnistudio-omniscript-line-break if:true={itemChild0Ele.bLineBreak} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-line-break><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent2} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-smarty if:true={itemChild0Ele.bcustomlightningwebcomponent2} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" screentype="physicalAddress"></c-smarty></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent3} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-smarty if:true={itemChild0Ele.bcustomlightningwebcomponent3} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" screentype="mailingAddress"></c-smarty></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-messaging if:true={itemChild0Ele.bMessaging} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-messaging><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent4} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-smarty if:true={itemChild0Ele.bcustomlightningwebcomponent4} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" screentype="mailingAddress"></c-smarty></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent5} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-d-a-o_-document-uploader if:true={itemChild0Ele.bcustomlightningwebcomponent5} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" application-id={jsonDef.response.DRId_DAO_Application__c}></c-d-a-o_-document-uploader></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent6} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-buisness-role-details if:true={itemChild0Ele.bcustomlightningwebcomponent6} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input=""></c-buisness-role-details></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent7} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-d-a-o_-document-uploader if:true={itemChild0Ele.bcustomlightningwebcomponent7} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" application-id={jsonDef.response.DRId_DAO_Application__c}></c-d-a-o_-document-uploader></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent8} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-smartyapplicant if:true={itemChild0Ele.bcustomlightningwebcomponent8} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" screentype="physicalAddressApplicant"></c-smartyapplicant></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent9} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-smarty if:true={itemChild0Ele.bcustomlightningwebcomponent9} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" screentype="mailingAddress"></c-smarty></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent10} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-d-a-o_-document-uploader if:true={itemChild0Ele.bcustomlightningwebcomponent10} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" application-id={jsonDef.response.DRId_DAO_Application__c}></c-d-a-o_-document-uploader></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent11} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-dao-add-applicant if:true={itemChild0Ele.bcustomlightningwebcomponent11} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" applicationidvalue={jsonDef.response.DRId_DAO_Application__c}></c-dao-add-applicant></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent12} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-d-a-o_-document-uploader if:true={itemChild0Ele.bcustomlightningwebcomponent12} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" application-id={jsonDef.response.DRId_DAO_Application__c}></c-d-a-o_-document-uploader></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent13} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-d-a-o_-document-uploader if:true={itemChild0Ele.bcustomlightningwebcomponent13} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" application-id={jsonDef.response.DRId_DAO_Application__c}></c-d-a-o_-document-uploader></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent14} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-d-a-o_-document-uploader if:true={itemChild0Ele.bcustomlightningwebcomponent14} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" application-id={jsonDef.response.DRId_DAO_Application__c}></c-d-a-o_-document-uploader></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent15} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-diligence-questions if:true={itemChild0Ele.bcustomlightningwebcomponent15} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" gambling={jsonDef.response.gambling} marijuana={jsonDef.response.marijuana} professional={jsonDef.response.professional} charitable={jsonDef.response.charitable} armored={jsonDef.response.armored} thirdparty={jsonDef.response.thirdParty} replenish={jsonDef.response.replenish} nonbank={jsonDef.response.nonBank} moneyservice={jsonDef.response.moneyService}></c-diligence-questions></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent16} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-d-a-o_-document-uploader if:true={itemChild0Ele.bcustomlightningwebcomponent16} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" application-id={jsonDef.response.DRId_DAO_Application__c}></c-d-a-o_-document-uploader></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent17} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-d-a-o_-document-uploader if:true={itemChild0Ele.bcustomlightningwebcomponent17} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" application-id={jsonDef.response.DRId_DAO_Application__c}></c-d-a-o_-document-uploader></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent18} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-dao-in-complete-role-names if:true={itemChild0Ele.bcustomlightningwebcomponent18} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input=""></c-dao-in-complete-role-names></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent19} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-d-a-o_-document-uploader if:true={itemChild0Ele.bcustomlightningwebcomponent19} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input="" application-id={jsonDef.response.DRId_DAO_Application__c}></c-d-a-o_-document-uploader></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent20} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-rcu-lending-needs if:true={itemChild0Ele.bcustomlightningwebcomponent20} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input=""></c-rcu-lending-needs></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent21} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-rcu-loans-and-credits if:true={itemChild0Ele.bcustomlightningwebcomponent21} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input=""></c-rcu-loans-and-credits></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent22} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-rcu-money-market-and-cds if:true={itemChild0Ele.bcustomlightningwebcomponent22} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input=""></c-rcu-money-market-and-cds></omnistudio-omniscript-custom-lwc><omnistudio-omniscript-custom-lwc if:true={itemChild0Ele.bcustomlightningwebcomponent23} key={itemChild0Ele.lwcId} json-def={itemChild0Ele} data-omni-key={itemChild0Ele.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode} seed-json={jsonDef.propSetMap.seedDataJSON}><c-rcu-products-and-services if:true={itemChild0Ele.bcustomlightningwebcomponent23} key={itemChild0Ele.lwcId} data-omni-key={itemChild0Ele.name} omni-resume={resume} data-omni-layout={layout} omni-json-def={itemChild0Ele} omni-json-data={jsonDef.response} omni-seed-json={jsonDef.propSetMap.seedDataJSON} omni-custom-state={jsonDef.customSaveState} omni-script-header-def={scriptHeaderDef} omni-json-data-str={jsonDataStr} data-omni-input=""></c-rcu-products-and-services></omnistudio-omniscript-custom-lwc></template></template></omnistudio-omniscript-step><omnistudio-omniscript-navigate-action if:true={item.bNavigate} key={item.lwcId} json-def={item} data-omni-key={item.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-navigate-action><omnistudio-omniscript-dr-extract-action if:true={item.bDataRaptorExtractAction} key={item.lwcId} json-def={item} data-omni-key={item.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-dr-extract-action><omnistudio-omniscript-ip-action if:true={item.bIntegrationProcedureAction} key={item.lwcId} json-def={item} data-omni-key={item.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-ip-action><omnistudio-omniscript-set-errors if:true={item.bSetErrors} key={item.lwcId} json-def={item} data-omni-key={item.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-set-errors><omnistudio-omniscript-set-values if:true={item.bSetValues} key={item.lwcId} json-def={item} data-omni-key={item.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-set-values><omnistudio-omniscript-dr-post-action if:true={item.bDataRaptorPostAction} key={item.lwcId} json-def={item} data-omni-key={item.name} json-data={jsonDef.response} json-data-str={jsonDataStr} layout={layout} resume={resume} script-header-def={scriptHeaderDef} run-mode={runMode}></omnistudio-omniscript-dr-post-action></template>
                            <div class="slds-grid slds-wrap slds-gutters slds-p-horizontal_medium slds-show_medium">
                                <div class="slds-col slds-order_2 slds-medium-order_1 slds-size_12-of-12 slds-medium-size_6-of-12">
                                    <div class="omniscript-sfl-actions">
                                        <div if:true={cancelAction}>
                                            <omnistudio-button
                                                type="button"
                                                label={cancelLabel}
                                                variant="base"
                                                class={navButton.save.classes}
                                                onclick={cancel}>
                                            </omnistudio-button>
                                        </div>

                                        <omnistudio-navigate-action
                                            data-omni-key="DEFAULT-CANCEL"
                                            target-type={_defaultCancel.type}
                                            target-params={_defaultCancel.params}>
                                        </omnistudio-navigate-action>

                                        <template if:true={allowSfl}>
                                            <div>
                                                <omnistudio-button type="button"
                                                        label={navButton.save.label}
                                                        variant="base"
                                                        class={navButton.save.classes}
                                                        onclick={saveForLater}>
                                                </omnistudio-button>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-order_1 slds-medium-order_2 slds-size_12-of-12 slds-medium-size_6-of-12">
                                    <div class="slds-grid slds-wrap slds-grid_align-end omniscript-button-position">
                                        <template if:true={hasPrev}>
                                            <omnistudio-navigate-action
                                                if:true={_isSeoEnabled}
                                                target-type="Current Page"
                                                class={navButton.previous.classes}
                                                use-href
                                                target-params={navButton.previous.targetParams}>
                                                <omnistudio-button
                                                    type="button"
                                                    label={navButton.previous.label}
                                                    aria-label={navButton.previous.label}
                                                    variant="neutral"
                                                    extraclass="slds-size_1-of-1 slds-p-horizontal_none slds-text-align_center">
                                                </omnistudio-button>
                                            </omnistudio-navigate-action>

                                            <omnistudio-button
                                                if:false={_isSeoEnabled}
                                                type="button"
                                                label={navButton.previous.label}
                                                aria-label={navButton.previous.label}
                                                variant="neutral"
                                                class={navButton.previous.classes}
                                                extraclass="slds-size_1-of-1 slds-p-horizontal_none slds-text-align_center"
                                                onclick={prevStep}>
                                            </omnistudio-button>
                                        </template>
                                        <template if:true={hasNext}>
                                            <omnistudio-navigate-action
                                                if:true={_isSeoEnabled}
                                                target-type="Current Page"
                                                use-href
                                                class={navButton.next.classes}
                                                target-params={navButton.next.targetParams}>
                                                <omnistudio-button
                                                    type="button"
                                                    label={navButton.next.label}
                                                    aria-label={navButton.next.label}
                                                    variant="brand"
                                                    extraclass="slds-size_1-of-1 slds-p-horizontal_none slds-text-align_center">
                                                </omnistudio-button>
                                            </omnistudio-navigate-action>

                                            <omnistudio-button
                                                if:false={_isSeoEnabled}
                                                type="button"
                                                label={navButton.next.label}
                                                aria-label={navButton.next.label}
                                                variant="brand"
                                                class={navButton.next.classes}
                                                extraclass="slds-size_1-of-1 slds-p-horizontal_none slds-text-align_center"
                                                onclick={nextStep}>
                                            </omnistudio-button>
                                        </template>
                                    </div>
                                </div>
                            </div>
                            <div class="slds-grid slds-wrap slds-gutters slds-p-horizontal_medium slds-hide_medium">
                                <div class="slds-col slds-order_1 slds-medium-order_2 slds-size_12-of-12 slds-medium-size_6-of-12">
                                    <div class="slds-grid slds-wrap slds-grid_align-end">
                                        <template if:true={hasNext}>
                                            <omnistudio-navigate-action
                                                if:true={_isSeoEnabled}
                                                target-type="Current Page"
                                                use-href
                                                class={navButton.next.classes}
                                                target-params={navButton.next.targetParams}>
                                                <omnistudio-button
                                                    type="button"
                                                    label={navButton.next.label}
                                                    aria-label={navButton.next.label}
                                                    variant="brand"
                                                    extraclass="slds-size_1-of-1 slds-p-horizontal_none slds-text-align_center">
                                                </omnistudio-button>
                                            </omnistudio-navigate-action>
                                            <omnistudio-button
                                                if:false={_isSeoEnabled}
                                                type="button"
                                                label={navButton.next.label}
                                                aria-label={navButton.next.label}
                                                variant="brand"
                                                class={navButton.next.classes}
                                                extraclass="slds-size_1-of-1 slds-p-horizontal_none slds-text-align_center"
                                                onclick={nextStep}>
                                            </omnistudio-button>
                                        </template>
                                        <template if:true={hasPrev}>
                                            <omnistudio-navigate-action
                                                if:true={_isSeoEnabled}
                                                target-type="Current Page"
                                                class={navButton.previous.classes}
                                                use-href
                                                target-params={navButton.previous.targetParams}>
                                                <omnistudio-button
                                                    type="button"
                                                    label={navButton.previous.label}
                                                    aria-label={navButton.previous.label}
                                                    variant="neutral"
                                                    extraclass="slds-size_1-of-1 slds-p-horizontal_none slds-text-align_center">
                                                </omnistudio-button>
                                            </omnistudio-navigate-action>
                                            <omnistudio-button
                                                if:false={_isSeoEnabled}
                                                type="button"
                                                label={navButton.previous.label}
                                                aria-label={navButton.previous.label}
                                                variant="neutral"
                                                class={navButton.previous.classes}
                                                extraclass="slds-size_1-of-1 slds-p-horizontal_none slds-text-align_center"
                                                onclick={prevStep}>
                                            </omnistudio-button>
                                        </template>
                                    </div>
                                </div>
                                <div class="slds-col slds-order_2 slds-medium-order_1 slds-size_12-of-12 slds-medium-size_6-of-12">
                                    <div class="omniscript-sfl-actions">
                                        <div if:true={cancelAction}>
                                            <omnistudio-button
                                                type="button"
                                                label={cancelLabel}
                                                variant="base"
                                                class={navButton.save.classes}
                                                onclick={cancel}>
                                            </omnistudio-button>
                                        </div>
                                        <omnistudio-navigate-action
                                            data-omni-key="DEFAULT-CANCEL"
                                            target-type={_defaultCancel.type}
                                            target-params={_defaultCancel.params}>
                                        </omnistudio-navigate-action>
                                        <template if:true={allowSfl}>
                                            <div>
                                                <omnistudio-button type="button"
                                                        label={navButton.save.label}
                                                        variant="base"
                                                        class={navButton.save.classes}
                                                        onclick={saveForLater}>
                                                </omnistudio-button>
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </article>

                    <template if:true={bSflComplete}>
                        <omnistudio-omniscript-save-for-later-acknowledge result={bSflResult} layout={layout}></omnistudio-omniscript-save-for-later-acknowledge>
                    </template>
                    <template for:each={modalEvents}
                            for:item="modallvl1"
                            for:index="indexlvl1">
                        <omnistudio-omniscript-modal data-omni-key='omnimodal'
                                key={modallvl1.modalHeader}
                                type={modallvl1.type}
                                layout={layout}
                                triggered-on-step={modallvl1.triggeredOnStep}
                                hide-footer={modallvl1.hideFooter}
                                hide-header={modallvl1.hideHeader}>
                            <div slot="header">
                                <h1>{modallvl1.modalHeader}</h1>
                            </div>
                            <div slot="content">
                                <omnistudio-omniscript-formatted-rich-text value={modallvl1.modalMessage} disable-linkify></omnistudio-omniscript-formatted-rich-text>
                            </div>
                            <div slot="footer">
                                <ul class="slds-button-group-row">
                                    <template for:each={modallvl1.buttons}
                                            for:item="modalbutton"
                                            for:index="modalbuttonindex">
                                        <template if:true={modalbutton.label}>
                                            <li class="slds-button-group-item"
                                                key={modalbutton.key}>
                                                <lightning-button label={modalbutton.label}
                                                                value={indexlvl1}
                                                                onclick={modalbutton.handleClick}>
                                                </lightning-button>
                                            </li>
                                        </template>
                                    </template>
                                </ul>
                            </div>
                        </omnistudio-omniscript-modal>
                    </template>
                </template>
            </template>

            <template if:false={bSflValid}>
                <div class="slds-grid slds-wrap slds-gutters slds-p-horizontal_medium">
                    <div class="slds-col slds-size_12-of-12 slds-medium-size_2-of-12"></div>
                    <div class="slds-col slds-size_12-of-12 slds-medium-size_8-of-12">
                        <div class='slds-card omniscript-save-for-later'>
                            <div class="slds-card__header slds-grid">
                                <header class="slds-media slds-media_center slds-has-flexi-truncate">
                                    <div class="slds-media__figure">
                                        <lightning-icon icon-name="utility:warning"
                                                        alternative-text="Ok"
                                                        variant="warning"
                                                        size="large"></lightning-icon>
                                    </div>
                                    <div class="slds-media__body">
                                        <h2 class="slds-card__header-title">
                                            <span class="slds-text-heading_large">{allCustomLabelsUtil.OmniInvalidLwcComponent}</span>
                                        </h2>
                                    </div>
                                </header>
                            </div>
                            <div class='slds-card__body slds-card__body_inner'>
                                <p class="bold">{allCustomLabelsUtil.OmniInvalidLwcComponentMessage}</p>
                                <p>&nbsp;</p>
                                <div class="slds-grid slds-wrap slds-gutters slds-p-horizontal_medium">
                                    <div class="slds-col slds-size_12-of-12 slds-medium-size_6-of-12">
                                        <lightning-button variant="brand"
                                                        label={allCustomLabelsUtil.OmniContinue}
                                                        title={allCustomLabelsUtil.OmniContinue}
                                                        class="slds-m-left_x-small"
                                                        onclick={handleContinueInvalidSfl}
                                                        style="padding-left: 10px"></lightning-button>
                                    </div>
                                    <div class="slds-col slds-size_12-of-12 slds-medium-size_6-of-12"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="slds-col slds-size_12-of-12 slds-medium-size_2-of-12"></div>
                </div>
            </template>
        </template>
    </div>
</template>