import { LightningElement, api } from "lwc";
import { OmniscriptBaseMixin } from "omnistudio/omniscriptBaseMixin";
import getAddress from "@salesforce/apex/DAOAddressAutoComplete.getAddress";


export default class DaoApplicantAddressAutoComplete extends OmniscriptBaseMixin(LightningElement) {
        name = "";
        phone = "";
        website = "";
        accountNumber = "";
        fullAddress = "";
        doneTypingInterval = 300;
        typingTimer;
        suggestion;
        city = "";
        state = "";
        postalCode = "";
        street = "";
        showAddressForm = false;
        isApplicantPhysicalAddressSet;
        isApplicantMailingAddressSet;
        _omniJsonData;
        @api omniScript;
        @api screentype;

        @api
        set omniJsonData(data) {
            this._omniJsonData = data;
            if(this._omniJsonData){
              this.initializeConnectedCallback();
            }
        }
    
        get omniJsonData() {
            return this._omniJsonData;
        }
    
      initializeConnectedCallback() {
        if (this.screentype == "applicantPhysicalAddress" && this._omniJsonData?.RoleInfo?.Same_as_Business_Address) {
          console.log('applicantPhysicalAddress and Same_as_Business_Address');
          this.street = this._omniJsonData.rolestreetAddress;
          this.city = this._omniJsonData.rolecityAddress;
          this.state = this._omniJsonData.rolestateAddress;
          this.postalCode = this._omniJsonData.rolezipcode;
          this.fullAddress = this._omniJsonData.rolephysicalFullAddres;
          this.isApplicantPhysicalAddressSet = this.omniJsonData.isApplicantPhysicalAddressSet;
          
        }else if (this.screentype == "applicantPhysicalAddress") {
          console.log('applicantPhysicalAddress and not Same_as_Business_Address');
          this.street = this._omniJsonData?.rolestreetAddress1;
          this.city = this._omniJsonData?.rolecityAddress1;
          this.state = this._omniJsonData?.rolestateAddress1;
          this.postalCode = this._omniJsonData?.rolezipcode1;
          this.fullAddress = this._omniJsonData?.rolephysicalFullAddres1;
        } else if (this.screentype == "applicantMailingAddress") {
          console.log('applicantMailingAddress');
          this.street = this._omniJsonData?.rolemailingStreet;
          this.city = this._omniJsonData?.rolemailingCity;
          this.state = this._omniJsonData?.rolemailingState;
          this.postalCode = this._omniJsonData?.rolemailingZipcode;
          this.fullAddress = this._omniJsonData?.rolemailingfullAddress;
        }
        const state = this.omniGetSaveState();
        if (state) this.value = state;
      }
      
    
        handleInputChange(event) {
            clearTimeout(this.typingTimer);
            let value = event.target.value;
            this.fullAddress = value;
            console.log('handle input change fullAddress', this.fullAddress);
            // Validation: set isSet to false if address is empty
            if (!value || value.trim() === "") {
                if(this.screentype == "applicantPhysicalAddress"){
                    this.isApplicantPhysicalAddressSet = false;
                } else if(this.screentype == "applicantMailingAddress"){
                    this.isApplicantMailingAddressSet = false;
                }
            } else {
                if(this.screentype == "applicantPhysicalAddress"){
                    this.isApplicantPhysicalAddressSet = true;
                } else if(this.screentype == "applicantMailingAddress"){
                    this.isApplicantMailingAddressSet = true;
                }
            }
           
            this.typingTimer = setTimeout(() => {
                if (value) {
                    getAddress({ search: value })
                        .then((result) => {
                            let temp = JSON.parse(result);
                            let suggestionList = [];
                            temp.suggestions.forEach((elem) => {
                                let address =
                                    "" +
                                    elem.street_line +
                                    "," +
                                    elem.city +
                                    "," +
                                    elem.state +
                                    "," +
                                    elem.zipcode;
    
                                suggestionList.push({ id: Date.now(), value: address });
                            });
                            this.suggestion = suggestionList;
                        })
                        .catch((error) => {
                            console.log("## error in creating records: " + error);
                        });
                }
            }, this.doneTypingInterval);
    
            let dataToPass;
            if (this.screentype == "applicantPhysicalAddress") {
                dataToPass = {
                    rolestreetAddress: this.street,
                    rolecityAddress: this.city,
                    rolestateAddress: this.state,
                    rolezipcode: this.postalCode,
                    rolephysicalFullAddres: this.fullAddress,
                    isApplicantPhysicalAddressSet: this.isApplicantPhysicalAddressSet,
                    isApplicantMailingAddressSet: this.isApplicantMailingAddressSet,
                };
            } else if (this.screentype == "applicantMailingAddress") {
                dataToPass = {
                    rolemailingStreet: this.street,
                    rolemailingCity: this.city,
                    rolemailingState: this.state,
                    rolemailingZipcode: this.postalCode,
                    rolemailingfullAddress: this.fullAddress,
                    isApplicantMailingAddressSet: this.isApplicantMailingAddressSet,
                };
            } 
            this.omniApplyCallResp(dataToPass);
        }
    
        setAddress(event) {
            let placeId = event.currentTarget.dataset.value.split(",");
            let key = event.currentTarget.dataset.key;
            this.suggestion = undefined;
            this.street = placeId.length > 0 ? placeId[0] : "";
            this.city = placeId.length > 1 ? placeId[1] : "";
            this.state = placeId.length > 2 ? placeId[2] : "";
            this.postalCode = placeId.length > 3 ? placeId[3] : "";
            this.country = placeId.length > 4 ? placeId[4] : "";
            this.fullAddress =
                this.street +
                " " +
                this.city +
                " " +
                this.state +
                " " +
                this.postalCode +
                " " +
                "US";

                
            // Validation: set isSet to true if address is filled, false otherwise
            if (this.isAddressEmpty(this.street, this.city, this.state, this.postalCode)) {
                this.isApplicantPhysicalAddressSet = false;
                this.isApplicantMailingAddressSet = false;
            } else {
                this.isApplicantPhysicalAddressSet = true;
                this.isApplicantMailingAddressSet = true;
            }
            this.omniSaveState({
                Address: placeId,
            });
    
            this.omniUpdateDataJson({
                Address: placeId,
            });
            let dataToPass;
            if (this.screentype == "applicantPhysicalAddress") {
                dataToPass = {
                    rolestreetAddress: this.street,
                    rolecityAddress: this.city,
                    rolestateAddress: this.state,
                    rolezipcode: this.postalCode,
                    rolephysicalFullAddres: this.fullAddress,
                    isApplicantPhysicalAddressSet: this.isApplicantPhysicalAddressSet,
                };
            } else if (this.screentype == "applicantMailingAddress") {
                dataToPass = {
                    rolemailingStreet: this.street,
                    rolemailingCity: this.city,
                    rolemailingState: this.state,
                    rolemailingZipcode: this.postalCode,
                    rolemailingfullAddress: this.fullAddress,
                    isApplicantMailingAddressSet: this.isApplicantMailingAddressSet,
                };
            } 
    
            this.omniApplyCallResp(dataToPass);
        }
        handlePencilClick() {
            this.showAddressForm = !this.showAddressForm; // Toggle the visibility
        }
    
        handleAddressChange(event) {
            // Extract address details from event.detail
            const { street, city, province, postalCode } = event.detail;
            this.street = street || "";
            this.city = city || "";
            this.state = province || "";
            this.postalCode = postalCode || "";
    
            // Compose fullAddress for validation
            this.fullAddress = `${this.street} ${this.city} ${this.state} ${this.postalCode} US`;
    
            // Validation: set isSet to false if all address fields are empty
            if (this.isAddressEmpty(this.street, this.city, this.state, this.postalCode)) {
                if(this.screentype == "applicantPhysicalAddress"){
                    this.isApplicantPhysicalAddressSet = false;
                } else if(this.screentype == "applicantMailingAddress"){
                    this.isApplicantMailingAddressSet = false;
                }
            } else {
                if(this.screentype == "applicantPhysicalAddress"){
                    this.isApplicantPhysicalAddressSet = true;
                } else if(this.screentype == "applicantMailingAddress"){
                    this.isApplicantMailingAddressSet = true;
                }
            }
    
            // Update the OmniScript data
            let dataToPass;
            if (this.screentype == "applicantPhysicalAddress") {
                dataToPass = {
                    rolestreetAddress: this.street,
                    rolecityAddress: this.city,
                    rolestateAddress: this.state,
                    rolezipcode: this.postalCode,
                    rolephysicalFullAddres: this.fullAddress,
                    isApplicantPhysicalAddressSet: this.isApplicantPhysicalAddressSet,
                };
            } else if (this.screentype == "applicantMailingAddress") {
                dataToPass = {
                    rolemailingStreet: this.street,
                    rolemailingCity: this.city,
                    rolemailingState: this.state,
                    rolemailingZipcode: this.postalCode,
                    rolemailingfullAddress: this.fullAddress,
                    isApplicantMailingAddressSet: this.isApplicantMailingAddressSet,
                };
            } 
    
            this.omniApplyCallResp(dataToPass);
        }
    
        // Show pencil icon only if address is filled
        get isAddressFilled() {
            return this.fullAddress && this.fullAddress.trim() !== "";
        }

        isAddressEmpty(street, city, state, postalCode) {
            console.log('isAddressEmpty', street, city, state, postalCode);
            return (
                (!street || street.trim() === "") &&
                (!city || city.trim() === "") &&
                (!state || state.trim() === "") &&
                (!postalCode || postalCode.trim() === "")
            );
        }
}