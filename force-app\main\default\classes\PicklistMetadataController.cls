/**
 * @description       : This class is used to get the picklist values from the metadata and also to get the application documents and roles for the application.
 * <AUTHOR> Zennify
 * @last modified on  : 02-11-2025
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
public with sharing class PicklistMetadataController {
    
    @AuraEnabled(cacheable=true)
    public static List<String> getFirstPicklistValues() {
        List<String> picklistValues = new List<String>();
        for (Identification__mdt metadata : [SELECT Document_Type__c FROM Identification__mdt]) {
            System.debug(' #### metadata.Document_Type__c'+ metadata.Document_Type__c);
            picklistValues.add(metadata.Document_Type__c);
        }
        return picklistValues;
    }

    @AuraEnabled
    public static List<DAO_Application_Documents__c> getApplicationDocuments(Id applicationId) {
        return [SELECT Id, Document_Type__c, Description__c, CreatedDate, Role__c, DAO_Roles__c, DAO_Application__c, DocumentId__c FROM DAO_Application_Documents__c 
                WHERE DAO_Application__c = :applicationId];
    }

    
    @AuraEnabled
    public static List<DAO_Roles__c> getRolesForApplication(Id applicationId) {
        System.debug( ' #### roles for application : ' + [SELECT Id, Name  FROM DAO_Roles__c WHERE DAO_Application__c = :applicationId]);
        return [SELECT Id, Name  FROM DAO_Roles__c WHERE DAO_Application__c = :applicationId];
    }
    
    @AuraEnabled
    public static Boolean uploadFilesToApplication(List<Map<String, Object>> documents, Id applicationRecordId) {
        System.debug('#### uploading documents'+documents);
        if (documents == null || applicationRecordId == null) {
            throw new AuraHandledException('Invalid parameters provided.');
        }
        createApplicationDocuments(documents,applicationRecordId);
        List<ContentDocumentLink> contentDocumentLinks = new List<ContentDocumentLink>();
        
        for (Map<String, Object> doc : documents) {
            System.debug(' #### each doc : ' + doc);
            String documentId = (String) doc.get('id');
            
            if (String.isEmpty(documentId)) {
                continue;
            }
            ContentDocument contentDoc = [SELECT Id FROM ContentDocument WHERE Id = :documentId LIMIT 1];
            
            List<ContentDocumentLink> existingLinks = [SELECT Id FROM ContentDocumentLink WHERE ContentDocumentId = :contentDoc.Id AND LinkedEntityId = :applicationRecordId LIMIT 1];
            
            if (existingLinks.isEmpty()) {
                ContentDocumentLink contentDocumentLink = new ContentDocumentLink(
                    ContentDocumentId = contentDoc.Id,
                    LinkedEntityId = applicationRecordId,
                    ShareType = 'V'
                );
                
                contentDocumentLinks.add(contentDocumentLink);
            }
        }
        if (!contentDocumentLinks.isEmpty()) {
            System.debug('contentDocumentLinks'+contentDocumentLinks);
            try{
                insert contentDocumentLinks;
            }catch(Exception e){
                System.debug('Error while creating ContentDocumentLink records'+e.getMessage());
            }
            return true;
        }
        return false;
    }
    
    @AuraEnabled
    public static void deleteFile(Id contentDocumentId) {
        System.debug('contentDocumentId'+contentDocumentId);
        try {
            ContentDocument doc = [SELECT Id FROM ContentDocument WHERE Id = :contentDocumentId LIMIT 1];
            delete doc;
        } catch (Exception e) {
            throw new AuraHandledException('An error occurred while deleting the file: ' + e.getMessage());
        }
    }
    
    @AuraEnabled
    public static void deleteDocumentAndFile(Id applicationDocumentId, Id contentDocumentId) {
        System.debug('$$contentDocumentId: '+contentDocumentId);
        System.debug('$$applicationDocumentId: '+applicationDocumentId);
        try {
            delete new DAO_Application_Documents__c(Id = applicationDocumentId);
            ContentDocument doc = [SELECT Id FROM ContentDocument WHERE Id = :contentDocumentId LIMIT 1];
            delete doc;
        } catch (Exception e) {
            throw new AuraHandledException('An error occurred while deleting the file: ' + e.getMessage());
        }
    }
    
    @AuraEnabled
    public static void createApplicationDocuments(List<Map<String, Object>> documents, Id applicationRecordId) {
        if (documents == null || documents.isEmpty() || applicationRecordId == null) {
            throw new IllegalArgumentException('Invalid parameters provided. Documents or applicationRecordId is null or empty.');
        }
        List<ContentDocumentLink> contentDocumentLinks = new List<ContentDocumentLink>();
        List<DAO_Application_Documents__c> addDocData = new List<DAO_Application_Documents__c>();
        for (Map<String, Object> doc : documents) {
            String documentId = (String) doc.get('id');
            String documentType = (String) doc.get('documentType');
            String description = (String) doc.get('description');
            String role='';
            String userRole='';
            if(documentType=='Government Issued Identification')
            {
                role = (String) doc.get('role');
                userRole = (String) doc.get('userrole');
                updateRoleIdAttach((Id)doc.get('userrole'));
            }
                       
            DAO_Application_Documents__c appdoc = new DAO_Application_Documents__c();
            // DAO_Roles__c roleIdAttch=new DAO_Roles__c();
            appdoc.Description__c = description;
            appdoc.Document_Type__c = documentType;
            appdoc.DocumentId__c = documentId;
            if(documentType=='Government Issued Identification')
            {
                appdoc.Role__c = role;
                appdoc.DAO_Roles__c = userRole;
                
            }
           
            appdoc.DAO_Application__c = applicationRecordId;
            
            addDocData.add(appdoc);
        }
        
        try {
            if (!addDocData.isEmpty()) {
                System.debug('addDocData>>> ' + addDocData);
                insert addDocData;
                
                for(DAO_Application_Documents__c appdoc : addDocData){
                    contentDocumentLinks.add(new ContentDocumentLink(
                            ContentDocumentId = appdoc.DocumentId__c,
                            LinkedEntityId = appdoc.Id,
                            ShareType = 'V'
                        )
                    );
                }
                
                insert contentDocumentLinks;
            }
        } catch (DmlException e) {
            throw new AuraHandledException('Failed to insert documents: ' + e.getMessage());
        }
    }
    
    @AuraEnabled
    public static List<ContentVersion> getFileSizeByDocumentId(List<Id> documentIds) {
        return [SELECT ContentDocumentId, ContentSize FROM ContentVersion WHERE ContentDocumentId IN :documentIds];
    }

    @AuraEnabled
    public static void updateRoleIdAttach(Id appId){
        DAO_Roles__c roleIdAttach = [SELECT Id,Is_ID_Attached__c from DAO_Roles__c where Id =: appId];
        roleIdAttach.Is_ID_Attached__c=true;
        try
        {
            update roleIdAttach;
        }
        catch (DmlException e) {
            throw new AuraHandledException('Failed to insert documents: ' + e.getMessage());
        }
        
    }

    @AuraEnabled
    public static List<String> getDocumentTypes(String businessStructure, String role, Boolean fbnDBA) {
        List<String> documentTypes = new List<String>();

        // Error if the business structure or role is not provided and fbnDBA is null or false
        if ((businessStructure == null || String.isEmpty(businessStructure)) && (role == null || String.isEmpty(role)) && (fbnDBA == null || !fbnDBA)) {
            throw new AuraHandledException('Invalid parameters provided. Business structure or role is required.');
        }
        
        // Query for the document types based on the business structure
        List<Document_Type__mdt> documentTypeByBusinessStructure = new List<Document_Type__mdt>();
        if (businessStructure != null && !String.isEmpty(businessStructure) && Schema.sObjectType.Document_Type__mdt.isAccessible()) {
            documentTypeByBusinessStructure = [SELECT Document_Type__c, Business_Structure__c, Available_For_All__c FROM Document_Type__mdt WHERE Business_Structure__c = :businessStructure];
        }

        // Query for the document types based on the role
        List<Document_Type__mdt> documentTypeByRole = new List<Document_Type__mdt>();
        if (role != null && !String.isEmpty(role) && Schema.sObjectType.Document_Type__mdt.isAccessible()) {
            documentTypeByRole = [SELECT Document_Type__c, Role__c, Available_For_All__c FROM Document_Type__mdt WHERE Role__c = :role];
        }

        // Query for the document types based on the FBN/DBA being true
        List<Document_Type__mdt> documentTypeByFbnDba = new List<Document_Type__mdt>();
        if (fbnDBA && Schema.sObjectType.Document_Type__mdt.isAccessible()) {
            documentTypeByFbnDba = [SELECT Document_Type__c, FBN_DBA__c, Available_For_All__c FROM Document_Type__mdt WHERE FBN_DBA__c = true];
        }

        // Query for the document types that are available for all
        List<Document_Type__mdt> documentTypeForAll = new List<Document_Type__mdt>();
        if (Schema.sObjectType.Document_Type__mdt.isAccessible()) {
            documentTypeForAll = [SELECT Document_Type__c, Available_For_All__c FROM Document_Type__mdt WHERE Available_For_All__c = true];
        }

        // Combine all document types into a single list and add to documentTypes
        List<Document_Type__mdt> allDocumentTypes = new List<Document_Type__mdt>();
        allDocumentTypes.addAll(documentTypeByBusinessStructure);
        allDocumentTypes.addAll(documentTypeByRole);
        allDocumentTypes.addAll(documentTypeByFbnDba);
        allDocumentTypes.addAll(documentTypeForAll);

        for (Document_Type__mdt documentType : allDocumentTypes) {
            documentTypes.add(documentType.Document_Type__c);
        }

        return documentTypes;
    }
    
    @AuraEnabled(cacheable=true)
    public static List<Map<String, String>> getPicklistValues(String objectName, String fieldName) {
        List<Map<String, String>> res = new List<Map<String, String>>();
        for (Schema.PicklistEntry p : Schema.getGlobalDescribe().get(objectName)
            .getDescribe().fields.getMap().get(fieldName)
            .getDescribe().getPicklistValues()) {
                res.add(new Map<String, String>{ 'label' => p.getLabel(), 'value' => p.getValue() });
        }
        return res;
    }
}