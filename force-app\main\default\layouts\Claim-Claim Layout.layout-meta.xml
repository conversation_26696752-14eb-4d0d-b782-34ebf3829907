<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ClaimType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EstimatedAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ActualAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ApprovedAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Severity</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FnolChannel</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IncidentSite</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PolicyNumberId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InsuredAssetId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LossType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InitiationDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AssessmentDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FinalizedDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ClaimReason</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IncidentId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>Name</fields>
        <fields>ParticipantAccount</fields>
        <fields>ParticipantContact</fields>
        <relatedList>ClaimParticipants</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>TASK.PRIORITY</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <relatedList>RelatedActivityList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>Description</fields>
        <relatedList>ClaimItems</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>ActionPlanState</fields>
        <fields>Owner</fields>
        <fields>StartDate</fields>
        <relatedList>ActionPlan</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
