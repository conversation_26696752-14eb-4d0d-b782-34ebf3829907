.main-question{
	display: grid;
    color: #464545;
	margin-top: 15px;
	font-weight: bold;
	grid-column-gap: 5px !important;
	grid-template-columns: max-content auto !important;
}
.bold{
	font-weight: bold;
	margin-top: 15px;	
}
.required-label::before {
    content: '* ';
    color: red;
}
.input{
	display: grid;
	grid-column-gap: 1px !important;
	grid-template-columns: max-content max-content !important;
}
.input-picklist{
	max-width: 200px;
    margin-left: 10px;
}
.input-multi-picklist{
	max-width: 500px;
    margin-left: 10px;
}
.ques-width{
	max-width: 780px;
}
.sub-question{
	display: grid;
	margin-top: 15px;
	padding-left: 15px;
	grid-column-gap: 5px !important;
	grid-template-columns: max-content auto !important;
}
.sub-sub-question{
	display: grid;
	margin-top: 15px;
	padding-left: 25px;
	grid-column-gap: 5px !important;
	grid-template-columns: max-content auto !important;
}
.sub-sub-sub-question{
	display: grid;
	margin-top: 15px;
	padding-left: 40px;
	grid-column-gap: 5px !important;
	grid-template-columns: max-content auto !important;
}
.sub-sub-sub-sub-question{
	display: grid;
	margin-top: 15px;
	padding-left: 55px;
	grid-column-gap: 5px !important;
	/* grid-template-columns: max-content auto !important; */
	grid-template-columns: 500px auto !important;
}
.independent-text{
	max-width: 780px;
	margin-left: 15px;
    padding-top: 10px;
}
.independent-text-2{
	max-width: 780px;
	margin-left: 11px;
    padding-top: 10px;
}
.button-container {
	display: flex;
	justify-content: flex-end;
	/* Align buttons to the right */
	gap: 20px;
	/* Increase space between buttons */
	margin-top: 20px;
	/* Optional: Add some top margin */
  }
  
  .previous-btn {
	padding: 20px 30px;
	/* Padding */
	background: #333333;
	/* Dark background */
	color: #ffffff;
	/* Text color */
	border-radius: 5px;
	/* Rounded corners */
	text-decoration: none;
	/* No underline */
	font-weight: bold;
	/* Bold text */
	border: none;
	/* Remove default border */
	cursor: pointer;
	/* Pointer on hover */
	transition: background 0.3s ease;
	/* Smooth transition for background */
  }
  
  /* Hover style for Previous button */
  .previous-btn:hover {
	background: #65656A;
	/* Lighten background on hover */
  }
  
  .save-continue-btn {
	padding: 20px 30px;
	/* Padding */
	background: linear-gradient(45deg, #046a38, #84bd00);
	/* Gradient background */
	color: #ffffff;
	/* Text color */
	border-radius: 5px;
	/* Rounded corners */
	text-decoration: none;
	/* No underline */
	font-weight: bold;
	/* Bold text */
	border: none;
	/* Remove default border */
	cursor: pointer;
	/* Pointer on hover */
	transition: background 0.3s ease;
	/* Smooth transition for background */
  }
  
  /* Hover style for Save and Continue button */
  .save-continue-btn:hover {
	background: #046a38;
	/* Solid color on hover */
  }