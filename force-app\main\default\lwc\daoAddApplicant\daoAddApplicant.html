<!--
  @description       : 
  <AUTHOR> Zennify
  @last modified on  : 03-07-2025
  @last modified by  : <PERSON><PERSON><PERSON>
-->
<template>
    <lightning-card>
        <div class="slds-grid slds-wrap">
            <!-- First grid for Add Role section -->
            <div class="slds-col slds-size_1-of-1 slds-medium-size_1-of-3 slds-p-around_medium">
                <div class="slds-card slds-card_boundary">
                    <div class="slds-card__header">
                        <h2 class="slds-text-heading_medium">Add Role</h2>
                    </div>
                    <div class="slds-card__body slds-p-around_small">
                        <lightning-input label="First Name" value={firstName} onchange={handleFieldChange} data-field="firstName" required></lightning-input>
                        <lightning-input label="Middle Name" value={middleName} onchange={handleFieldChange} data-field="middleName"></lightning-input>
                        <lightning-input label="Last Name" value={lastName} onchange={handleFieldChange} data-field="lastName" required></lightning-input>
                        <lightning-input label="Email" type="email" value={email} onchange={handleFieldChange} data-field="email" required></lightning-input>
                        <div style="display: flex;align-items: baseline;">
                            <lightning-radio-group label="Role:" options={roleOptions} value={selectedRoleType} onchange={handleRoleTypeChange} required></lightning-radio-group>
                            <div  style="display: flex;flex-direction: column;" >
                                <lightning-helptext content="Controlling Individual* - The Owners, Officers, Directors, or other individuals that have the ability to enter into a contract on behalf of the business. Examples: CEO,CFO,Treasurer,Manager,Owner,etc."></lightning-helptext>
                                <lightning-helptext content="Beneficial Owner (Shareholder)* - A person who owns 25% or more of the business. Example: Investor,Partners,Owners,Managers,etc." style="margin-left: -31px;"></lightning-helptext>
                                <lightning-helptext content="Authorized Signer - All persons who are authorized to conduct transactions on the account and appointed by the Controlling Individual(s) and are optional for accounts. Examples: Secretary,Bookkeeper,Assistant, etc." style="margin-left: -24px;"></lightning-helptext>
                            </div>
                        </div></br>
                        <button onclick={handleAddRole} class="addRoleButton">Add Role</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Table section moved below Add Role -->
        <div class="slds-col slds-size_1-of-1 slds-p-around_medium slds-scrollable_x">
            <div class="slds-card slds-card_boundary">
                <div class="slds-card__body slds-p-around_small">
                    <div class="slds-table slds-table_bordered slds-table_cell-buffer slds-scrollable_x">
                        <table class="slds-table slds-table_bordered slds-table_cell-buffer">
                            <thead>
                                <tr class="slds-line-height_reset heading">
                                    <th scope="col" colspan="8">
                                        <center>Business Roles</center>
                                    </th>
                                </tr>
                                <tr class="slds-line-height_reset sub-heading">
                                    <th scope="col" style="background-color: #d5e3cf !important">Update</th>
                                    <th scope="col" style="background-color: #d5e3cf !important">Name</th>
                                    <th scope="col" style="background-color: #d5e3cf !important">Role</th>
                                    <th scope="col" style="background-color: #d5e3cf !important">Beneficial Owner %</th>
                                    <th scope="col" style="background-color: #d5e3cf !important">Email</th>
                                    <th scope="col" style="background-color: #d5e3cf !important">ID Attached</th>
                                    <th scope="col" style="background-color: #d5e3cf !important">Completed</th>
                                    <th scope="col" style="background-color: #d5e3cf !important">Applicant</th>
                                </tr>
                            </thead>
                            <tbody>
                                <template for:each={roles} for:item="role" for:index="index">
                                    <tr key={role.Id} class="evenodd">
                                        <td>
                                            <lightning-button-icon icon-name="utility:edit" alternative-text="Update Role" onclick={handleOpenRole} data-id={role.Id}></lightning-button-icon>
                                            <!-- <lightning-button-icon icon-name="utility:email" alternative-text="Send link to complete role information" onclick={handleSendEmail} data-id={role.Id}></lightning-button-icon> -->
                                            <lightning-button-icon icon-name="utility:delete" alternative-text="Delete" onclick={handleDelete} data-id={role.Id} disabled={role.disableButton}></lightning-button-icon>
                                        </td>
                                        <td>{role.Name}</td>
                                        <td>{role.Individual_Role__c}</td>
                                        <td>{role.BusinessOwned}</td>
                                        <td>{role.Email__c}</td>
                                        <td>
                                            <lightning-input type="checkbox" checked={role.Is_ID_Attached__c} disabled="true"></lightning-input>
                                        </td>
                                        <td>
                                            <lightning-input type="checkbox" checked={role.IsCompleted__c} disabled="true"></lightning-input>
                                        </td>
                                        <td>
                                            <lightning-input type="checkbox" checked="true" disabled={role.disableButton}></lightning-input>
                                        </td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </lightning-card>
</template>