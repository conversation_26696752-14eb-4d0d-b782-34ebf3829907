// Screen definitions
export const SCREENS = {
    WELCOME: 'welcomePage',
    QUESTIONS: 'questionScreen',
    PRIMARY_MEMBER: 'primaryMemberInfo',
    ADDRESS_ID: 'addressAndIdScreen',
    PRODUCT_SELECTION: 'productSelection',
    DOCUMENTS: 'documents',
    FUNDING: 'funding',
    REVIEW: 'review',
    CONFIRMATION: 'confirmation'
};

// Screen sequence for navigation
export const SCREEN_SEQUENCE = [
    SCREENS.WELCOME,
    SCREENS.QUESTIONS,
    SCREENS.PRIMARY_MEMBER,
    SCREENS.ADDRESS_ID,
    SCREENS.PRODUCT_SELECTION,
    SCREENS.DOCUMENTS,
    SCREENS.FUNDING,
    SCREENS.REVIEW,
    SCREENS.CONFIRMATION
];

// Screen data mapping
export const SCREEN_DATA_MAP = {
    [SCREENS.WELCOME]: 'welcomeInfo',
    [SCREENS.QUESTIONS]: 'questionInfo',
    [SCREENS.PRIMARY_MEMBER]: 'primaryMemberInfo',
    [SCREENS.ADDRESS_ID]: 'addressAndIdInfo',
    [SCREENS.PRODUCT_SELECTION]: 'productSelectionInfo',
    [SCREENS.DOCUMENTS]: 'documentsInfo',
    [SCREENS.FUNDING]: 'fundingInfo',
    [SCREENS.REVIEW]: 'reviewInfo'
};

// Screens requiring server-side save
export const SCREENS_REQUIRING_SAVE = [
    SCREENS.PRIMARY_MEMBER,
    SCREENS.ADDRESS_ID,
    SCREENS.PRODUCT_SELECTION,
    SCREENS.FUNDING
];

// Idle timeout configuration
export const IDLE_TIMEOUT = {
    DURATION: 1200000, // 20 minutes in milliseconds
    WARNING_DURATION: 120000 // 2 minutes in milliseconds
};

// Error messages
export const ERROR_MESSAGES = {
    SAVE_ERROR: 'There was a problem saving your information. Please try again.',
    LOAD_ERROR: 'There was a problem loading your application. Please refresh the page and try again.',
    VALIDATION_ERROR: 'Please correct the errors before continuing.',
    NO_APPLICATION: 'No application data to save'
};

// Success messages
export const SUCCESS_MESSAGES = {
    SAVE_SUCCESS: 'Your information has been saved successfully.',
    APPLICATION_SUBMITTED: 'Your application has been submitted successfully.'
};

// Application data structure
export const DEFAULT_APPLICATION_DATA = {
    application: null,
    welcomeInfo: {},
    questionInfo: {},
   
};

// Field mappings for different screens
export const FIELD_MAPPINGS = {
    ADDRESS_ID: {
        PHYSICAL_ADDRESS: 'PhysicalAddress',
        MAILING_ADDRESS: 'MailingAddress',
        SAME_ADDRESS: 'Mailing_Address_same_as_Physical',
        IDENTITY_INFO: 'IdentityInfo'
    },
    QUESTIONS: {
        MEMBERSHIP_STATUS: 'membershipStatus',
        ACCOUNT_TYPE: 'selectedAccountType'
    }
};

// CSS class names
export const CSS_CLASSES = {
    RIGHT_BG: 'rcu-right-background-image',
    LEFT_BG: 'rcu-left-background-image',
    TIMEOUT_WARNING: 'rcu-timeout-warning-banner',
    CONTAINER: 'rcu-app-container',
    HEADER_ADJUSTMENT: 'rcuHeader-content-adjustment'
};

// Event names
export const EVENTS = {
    NEXT: 'next',
    BACK: 'back',
    SAVE: 'save',
    ERROR: 'error',
    SCREEN_NAVIGATE: 'screennavigate',
    ADD_APPLICANT: 'addapplicant',
    SAVE_INFO: 'saveinfo'
};