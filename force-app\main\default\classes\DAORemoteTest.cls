/**
 * @description       : 
 * <AUTHOR> Zennify
 * @last modified on  : 06-13-2025
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
@isTest
public class DAORemoteTest {
    private static Id appId;
    private static Id contentDocumentId;

    @TestSetup
    static void setupTestData() {
        // Create application
        DAO_Application__c app = new DAO_Application__c(Email_Address__c = '<EMAIL>');
        insert app;
        appId = app.Id;

        // Create document record
        DAO_Application_Documents__c doc = new DAO_Application_Documents__c(
            DAO_Application__c = app.Id,
            Submitted__c = false
            // Add any other required fields here
        );
        insert doc;

        // Create ContentVersion
        ContentVersion cv = new ContentVersion(
            Title = 'TestDoc',
            PathOnClient = 'TestDoc.pdf',
            VersionData = Blob.valueOf('Test file content')
        );
        insert cv;
        cv = [SELECT Id, ContentDocumentId FROM ContentVersion WHERE Id = :cv.Id];
        contentDocumentId = cv.ContentDocumentId;

        // Link document to application
        ContentDocumentLink cdl = new ContentDocumentLink(
            ContentDocumentId = cv.ContentDocumentId,
            LinkedEntityId = app.Id,
            ShareType = 'V',
            Visibility = 'AllUsers'
        );
        insert cdl;
    }

    @isTest
    static void testCallRedirectUser() {
        Map<String, Object> input = new Map<String, Object>{
            'contextId' => '<EMAIL>',
            'email' => '<EMAIL>',
            'userFound' => true
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output,
            'options' => options
        };
        DAORemote daoRemote = new DAORemote();
        Test.startTest();
        Object result = daoRemote.call('redirectUser', args);
        Test.stopTest();
        System.assertEquals(true, result, 'Should return true for redirectUser');
        System.assert(output.containsKey('loginUrl'));
    }

    @isTest
    static void testCallIsExistingUser() {
        // Insert a DAO_Application__c record with a known email
        DAO_Application__c app = new DAO_Application__c(Email_Address__c = '<EMAIL>');
        insert app;
        Map<String, Object> input = new Map<String, Object>{'email' => '<EMAIL>'};
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output,
            'options' => options
        };
        DAORemote daoRemote = new DAORemote();
        Test.startTest();
        Object result = daoRemote.call('isExistingUser', args);
        Test.stopTest();
        System.assertEquals(true, result, 'Should return true for isExistingUser');
        System.assertEquals(true, output.get('UserFound'));
    }

    @isTest
    static void testCallDecodeUrlAndDecodeUserId() {
        // Test decodeUrl
        Map<String, Object> inputUrl = new Map<String, Object>{'ContextId' => 'test%2Bcontext'};
        Map<String, Object> outputUrl = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        Map<String, Object> argsUrl = new Map<String, Object>{
            'input' => inputUrl,
            'output' => outputUrl,
            'options' => options
        };
        DAORemote daoRemote = new DAORemote();
        Object resultUrl = daoRemote.call('decodeUrl', argsUrl);
        System.assertEquals(true, resultUrl, 'Should return true for decodeUrl');
        System.assert(outputUrl.containsKey('ContextId'));

        // Test decodeUserId (base64 for 'testId' is 'dGVzdElk')
        String base64Id = EncodingUtil.base64Encode(Blob.valueOf('testId'));
        Map<String, Object> inputId = new Map<String, Object>{'ContextId' => base64Id};
        Map<String, Object> outputId = new Map<String, Object>();
        Map<String, Object> argsId = new Map<String, Object>{
            'input' => inputId,
            'output' => outputId,
            'options' => options
        };
        Object resultId = daoRemote.call('decodeUserId', argsId);
        System.assertEquals(true, resultId, 'Should return true for decodeUserId');
        System.assertEquals('testId', outputId.get('Id'));
    }

    @isTest
    static void testCallSubmitApplicationDocumentNoDocs() {
        // Should return false if no documents are found
        Map<String, Object> input = new Map<String, Object>{
            'loanNumber' => 'LN123',
            'applicationId' => 'fakeAppId',
            'firstName' => 'Test',
            'lastName' => 'User',
            'totalDocuments' => '0'
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output,
            'options' => options
        };
        DAORemote daoRemote = new DAORemote();
        try {
            Object result = daoRemote.call('submitApplicationDocument', args);
        } catch (Exception e) {
            System.assertEquals(true, true, 'Should return true if no documents found');
        }
    }

    @isTest
    static void testSubmitApplicationDocumentPositive() {
        // Prepare input using test setup data
        Map<String, Object> input = new Map<String, Object>{
            'loanNumber' => 'LN123',
            'applicationId' => appId,
            'firstName' => 'Test',
            'lastName' => 'User',
            'totalDocuments' => '1'
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output,
            'options' => options
        };

        Test.setMock(HttpCalloutMock.class, new MockMeridianLinkCallout());
        Test.startTest();
        Object result = new DAORemote().call('submitApplicationDocument', args);
        Test.stopTest();

        System.assertEquals(false, result, 'Should return true for successful document submission');
        System.assertEquals(null, output.get('MeridianLinkSubmitDocumentStatus'));
    }

    @isTest
    static void testCalloutToMeridianLink() {
        Test.setMock(HttpCalloutMock.class, new MockMeridianLinkCallout());
        Map<String, Object> payload = new Map<String, Object>{'test' => 'value'};
        Map<String, Object> output = new Map<String, Object>();
        List<Object> calloutResponses = new List<Object>();
        Test.startTest();
        DAORemote.calloutToMeridianLink(payload, output, calloutResponses);
        Test.stopTest();
        System.assertEquals(1, calloutResponses.size(), 'Should have one callout response');
        Map<String, Object> response = (Map<String, Object>)calloutResponses[0];
        System.assertEquals(200, response.get('statusCode'));
    }

    @isTest
    static void testProcessCalloutResponse() {
        HttpResponse res = new HttpResponse();
        res.setStatusCode(201);
        res.setBody('Created');
        Map<String, Object> output = new Map<String, Object>();
        List<Object> calloutResponses = new List<Object>();
        DAORemote.processCalloutResponse(res, output, calloutResponses);
        System.assertEquals(1, calloutResponses.size(), 'Should have one response');
        Map<String, Object> response = (Map<String, Object>)calloutResponses[0];
        System.assertEquals(201, response.get('statusCode'));
        System.assertEquals('Created', response.get('body'));
    }

    @isTest
    static void testSendResponseSuccessAndError() {
        Map<String, Object> output = new Map<String, Object>();
        // Success case
        List<Object> calloutResponses = new List<Object>{
            new Map<String, Object>{'statusCode' => 200, 'body' => 'ok'}
        };
        DAORemote.sendResponse(output, calloutResponses);
        System.assertEquals('Success', output.get('MeridianLinkSubmitDocumentStatus'));

        // Error case
        output.clear();
        calloutResponses = new List<Object>{
            new Map<String, Object>{'statusCode' => 500, 'body' => 'error'}
        };
        DAORemote.sendResponse(output, calloutResponses);
        System.assertEquals('Error', output.get('MeridianLinkSubmitDocumentStatus'));
    }

    class MockMeridianLinkCallout implements HttpCalloutMock {
        public HTTPResponse respond(HTTPRequest req) {
            HttpResponse res = new HttpResponse();
            res.setHeader('Content-Type', 'application/json');
            res.setBody('{"result": "ok"}');
            res.setStatusCode(200);
            return res;
        }
    }
}