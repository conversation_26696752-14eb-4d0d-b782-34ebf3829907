<?xml version="1.0" encoding="UTF-8"?>
<CustomObjectTranslation xmlns="http://soap.sforce.com/2006/04/metadata">
    <fieldSets>
        <label><!-- Financial Accounts Summary --></label>
        <name>FSC_Banker_Profile_Fin_Acct_Summary</name>
    </fieldSets>
    <fieldSets>
        <label><!-- Financial Accounts Summary --></label>
        <name>WM_Client_Profile_Fin_Acct_Summary</name>
    </fieldSets>
    <fieldSets>
        <label><!-- Household Summary --></label>
        <name>WM_Client_Profile_Relations_HH_Summary</name>
    </fieldSets>
    <fieldSets>
        <label><!-- Relationship Group Members --></label>
        <name>WM_Client_Relationship_Group_Members</name>
    </fieldSets>
    <fieldSets>
        <label><!-- Relationship Groups --></label>
        <name>WM_Client_Relationship_Groups</name>
    </fieldSets>
    <layouts>
        <layout>Account (Business Referral) Layout</layout>
        <sections>
            <label><!-- Custom Links --></label>
            <section>Custom Links</section>
        </sections>
        <sections>
            <label><!-- Financial Summary --></label>
            <section>Financial Summary</section>
        </sections>
        <sections>
            <label><!-- Office Manager/Broker --></label>
            <section>Office Manager/Broker</section>
        </sections>
        <sections>
            <label><!-- Referral Information --></label>
            <section>Referral Information</section>
        </sections>
    </layouts>
    <layouts>
        <layout>Account (Business) Layout</layout>
        <sections>
            <label><!-- Custom Links --></label>
            <section>Custom Links</section>
        </sections>
        <sections>
            <label><!-- Office Manager/Broker --></label>
            <section>Office Manager/Broker</section>
        </sections>
    </layouts>
    <layouts>
        <layout>Account (Household) Layout</layout>
        <sections>
            <label><!-- Custom Links --></label>
            <section>Custom Links</section>
        </sections>
        <sections>
            <label><!-- Financial Summary --></label>
            <section>Financial Summary</section>
        </sections>
        <sections>
            <label><!-- Referral Information --></label>
            <section>Referral Information</section>
        </sections>
    </layouts>
    <layouts>
        <layout>Account (Individual) Layout</layout>
        <sections>
            <label><!-- Address 1 --></label>
            <section>Address 1</section>
        </sections>
        <sections>
            <label><!-- Client Services --></label>
            <section>Client Services</section>
        </sections>
        <sections>
            <label><!-- Know Your Client --></label>
            <section>Know Your Client</section>
        </sections>
        <sections>
            <label><!-- Referral Information --></label>
            <section>Referral Information</section>
        </sections>
    </layouts>
    <layouts>
        <layout>Account (Institution) Layout</layout>
        <sections>
            <label><!-- Custom Links --></label>
            <section>Custom Links</section>
        </sections>
    </layouts>
    <layouts>
        <layout>Account (Policyholder) Layout</layout>
        <sections>
            <label><!-- Address --></label>
            <section>Address</section>
        </sections>
        <sections>
            <label><!-- Custom Links --></label>
            <section>Custom Links</section>
        </sections>
        <sections>
            <label><!-- Financial Interest --></label>
            <section>Financial Interest</section>
        </sections>
        <sections>
            <label><!-- Policyholder Information --></label>
            <section>Policyholder Information</section>
        </sections>
        <sections>
            <label><!-- Policyholder Services --></label>
            <section>Policyholder Services</section>
        </sections>
        <sections>
            <label><!-- System Information --></label>
            <section>System Information</section>
        </sections>
    </layouts>
    <layouts>
        <layout>Account (Retail Client - Individual) Layout</layout>
        <sections>
            <label><!-- Billing/Shipping Address --></label>
            <section>Billing/Shipping Address</section>
        </sections>
        <sections>
            <label><!-- Client Services --></label>
            <section>Client Services</section>
        </sections>
        <sections>
            <label><!-- Finance Profile --></label>
            <section>Finance Profile</section>
        </sections>
        <sections>
            <label><!-- Referral Information --></label>
            <section>Referral Information</section>
        </sections>
        <sections>
            <label><!-- Relationship Information --></label>
            <section>Relationship Information</section>
        </sections>
    </layouts>
    <layouts>
        <layout>Account Layout</layout>
        <sections>
            <label><!-- Office Manager Details --></label>
            <section>Office Manager Details</section>
        </sections>
    </layouts>
    <layouts>
        <layout>Organization Layout</layout>
        <sections>
            <label><!-- Custom Links --></label>
            <section>Custom Links</section>
        </sections>
    </layouts>
    <layouts>
        <layout>Person Account Layout</layout>
        <layoutType>PersonAccount</layoutType>
        <sections>
            <label><!-- Email Information --></label>
            <section>Email Information</section>
        </sections>
        <sections>
            <label><!-- Employer Information --></label>
            <section>Employer Information</section>
        </sections>
        <sections>
            <label><!-- Other Information --></label>
            <section>Other Information</section>
        </sections>
    </layouts>
    <quickActions>
        <label><!-- Add Employee Photo --></label>
        <name>Add_Employee_Photo</name>
    </quickActions>
    <quickActions>
        <label><!-- Assign Agent to Office --></label>
        <name>Assign_Agent_to_Office</name>
    </quickActions>
    <quickActions>
        <label><!-- Child Account --></label>
        <name>Child_Account</name>
    </quickActions>
    <quickActions>
        <label><!-- Create Event --></label>
        <name>Create_Event</name>
    </quickActions>
    <quickActions>
        <label><!-- Event --></label>
        <name>ELO_Events</name>
    </quickActions>
    <quickActions>
        <label><!-- Log a Call --></label>
        <name>ELO_Log_a_call</name>
    </quickActions>
    <quickActions>
        <label><!-- Task --></label>
        <name>ELO_Task</name>
    </quickActions>
    <quickActions>
        <aspect>Master</aspect>
        <label>Rediger gruppe</label>
        <name>FinServ__EditGroup</name>
    </quickActions>
    <recordTypes>
        <description>En forretning eller en organisation</description>
        <label>Forretning</label>
        <name>FinServ__IndustriesBusiness</name>
    </recordTypes>
    <recordTypes>
        <description>En gruppering af relaterede personer og institutioner</description>
        <label>Husstand</label>
        <name>FinServ__IndustriesHousehold</name>
    </recordTypes>
    <recordTypes>
        <description>En person, der er et kundeemne eller en klient</description>
        <label>Individuel</label>
        <name>FinServ__IndustriesIndividual</name>
    </recordTypes>
    <recordTypes>
        <description>En institution, der er et kundeemne eller en klient</description>
        <label>Institution</label>
        <name>FinServ__IndustriesInstitution</name>
    </recordTypes>
    <recordTypes>
        <label><!-- Organization --></label>
        <name>Organization</name>
    </recordTypes>
    <recordTypes>
        <description><!-- The Brokerage a Real Estate Agent works for --></description>
        <label><!-- Real Estate Offices --></label>
        <name>Real_Estate_Offices</name>
    </recordTypes>
    <validationRules>
        <errorMessage><!-- Please correct the birthdate value to match the (MM/DD) format. --></errorMessage>
        <name>Birthdate_MM_DD_Validation</name>
    </validationRules>
    <validationRules>
        <errorMessage><!-- You don&apos;t have permission to create Real Estate Offices --></errorMessage>
        <name>Block_Real_Estate_Office_Creation</name>
    </validationRules>
    <validationRules>
        <errorMessage>Interne henvisere skal være interne Salesforce-brugere.</errorMessage>
        <name>FinServ__NonPortalUserReferrer</name>
    </validationRules>
    <validationRules>
        <errorMessage>Registreringstypen Enkeltperson kan ikke ændres</errorMessage>
        <name>FinServ__NotAllowingConversionFromIndividual</name>
    </validationRules>
    <validationRules>
        <errorMessage>Registreringstypen kan ikke ændres til Enkeltperson.</errorMessage>
        <name>FinServ__NotAllowingConversionToIndividual</name>
    </validationRules>
    <validationRules>
        <errorMessage>Angiv en intern henviser eller en ekstern henviser. Du kan kun angive en henviser for hver henvisning.</errorMessage>
        <name>FinServ__SingleReferredByEntryByRecord</name>
    </validationRules>
    <webLinks>
        <label><!-- Create_Real_Estate_Office --></label>
        <name>Create_Real_Estate_Office</name>
    </webLinks>
    <webLinks>
        <label>Tilføj medlem</label>
        <name>FinServ__AddMember</name>
    </webLinks>
    <webLinks>
        <label>Tilføj relation</label>
        <name>FinServ__AddRelation</name>
    </webLinks>
    <webLinks>
        <label><!-- GoogleMaps --></label>
        <name>GoogleMaps</name>
    </webLinks>
    <webLinks>
        <label><!-- GoogleNews --></label>
        <name>GoogleNews</name>
    </webLinks>
    <webLinks>
        <label><!-- GoogleSearch --></label>
        <name>GoogleSearch</name>
    </webLinks>
    <webLinks>
        <label><!-- Mass_Delete_Records --></label>
        <name>Mass_Delete_Records</name>
    </webLinks>
    <webLinks>
        <label><!-- ExactTarget_Resubscribe --></label>
        <name>et4ae5__ExactTarget_Resubscribe</name>
    </webLinks>
    <webLinks>
        <label><!-- ExactTarget_Unsubscribe --></label>
        <name>et4ae5__ExactTarget_Unsubscribe</name>
    </webLinks>
    <webLinks>
        <label><!-- Send_ExactTarget_Email --></label>
        <name>et4ae5__Send_ExactTarget_Email</name>
    </webLinks>
    <webLinks>
        <label><!-- View_ExactTarget_Analytics --></label>
        <name>et4ae5__View_ExactTarget_Analytics</name>
    </webLinks>
</CustomObjectTranslation>
