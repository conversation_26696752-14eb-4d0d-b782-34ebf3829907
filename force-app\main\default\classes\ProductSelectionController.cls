/**
 * @description       : This class is used return product codes to productSelection lwc from custom metadata
 * <AUTHOR> Zennify
 * @last modified on  : 04-23-2025
 * @last modified by  : <PERSON><PERSON><PERSON><PERSON><PERSON>
**/
public without sharing class ProductSelectionController {
	
    @AuraEnabled(cacheable=true)
    public static List<DAO_Product_Codes__mdt> getProductCodes(){
        return [Select Id, DeveloperName, Label, Name__c, Product_Code__c, IsActive__c, Vertical__c 
                From DAO_Product_Codes__mdt];
    }
}