import { LightningElement } from 'lwc';
import {OmniscriptBaseMixin} from 'omnistudio/omniscriptBaseMixin';

export default class HasControlBox  extends OmniscriptBaseMixin(LightningElement) {
    isShowModal = false;
    hasControl;
    get options() {
        return [
            { label: '--Clear--', value: '' },
            { label: 'Yes', value: 'Yes' },
            { label: 'No', value: 'No' },
        ];
    }
    // Function to show the modal
    showModalBox() {  
        this.isShowModal = true;
    }

    // Function to hide the modal
    hideModalBox() {  
        this.isShowModal = false;
    }
    handleChange(event){
        this.hasControl = event.target.value;
        let datatopass={};
        datatopass = {
                    hasControl:this.hasControl
                };
        this.omniApplyCallResp(datatopass);
    }
}