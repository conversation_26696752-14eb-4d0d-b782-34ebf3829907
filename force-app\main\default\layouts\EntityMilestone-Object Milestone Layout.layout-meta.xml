<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>BusinessHoursId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ParentEntityId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>MilestoneId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CompletionDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>IsViolated</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>IsCompleted</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>StartDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TargetDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TargetResponseInMins</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>TimeRemainingInMins</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>ElapsedTimeInMins</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <miniLayout>
        <fields>BusinessHoursId</fields>
    </miniLayout>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
