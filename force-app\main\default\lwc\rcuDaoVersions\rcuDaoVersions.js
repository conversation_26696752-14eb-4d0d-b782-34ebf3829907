import { LightningElement, track } from 'lwc';
import getDAOVersions from '@salesforce/apex/RcuDaoVersions.getDAOVersions';
export default class RcuDaoVersions extends LightningElement {
    @track data;
    connectedCallback() {
        console.log('Called-rcuDaoVersions');
        getDAOVersions({param: this.param}).then((result) => {
            console.log('$getDAOVersions-result: ',result);

            let groupedMap = new Map();

            for (let item of result) {
                if (!groupedMap.has(item.Name)) {
                    groupedMap.set(item.Name, []);
                }
                groupedMap.get(item.Name).push({
                    ...item,
                    CreatedDate: this.getConvertedDateTime(item.CreatedDate),
                    LastModifiedDate: this.getConvertedDateTime(item.LastModifiedDate),
                    link: `https://rcu2022--devzennify.sandbox.lightning.force.com/builder_omnistudio/omnistudioBuilder.app?type=omniscript&id=${item.Id}`
                });
            }

            let groupedArray = Array.from(groupedMap.entries()).map(([name, versions]) => ({
                Name: name,
                versions: versions
            }));

            console.log('$groupedArray: ',JSON.parse(JSON.stringify(groupedArray)));
            this.data = groupedArray;

        }).catch((error) => {
            console.error('$getDAOVersions-error: ',error); 
        });
    }
    getConvertedDateTime(dateTimeString){
        let date_time = Intl.DateTimeFormat('en-IN', { timeZone: 'Asia/Kolkata', month: 'short', day: '2-digit', hour: '2-digit', minute: '2-digit'}).format(new Date(dateTimeString));
        date_time = date_time.substring(0, date_time.length - 2) + date_time.substring(date_time.length - 2).toUpperCase();
        date_time = date_time.split(', ');
        date_time = date_time[1] + ', ' + date_time[0];
        return date_time;
    }
}