@IsTest
public class RoleControllerTest {
    
    @TestSetup
    public static void setupData() {
        // Create a test application
        DAO_Application__c testApplication = new DAO_Application__c(Business_Structure__c = 'Corporation');
        insert testApplication;

        // Create test roles
        List<DAO_Roles__c> testRoles = new List<DAO_Roles__c>{
            new DAO_Roles__c(DAO_Application__c = testApplication.Id, Individual_Role__c = 'Controlling Individual', Email__c = '<EMAIL>'),
            new DAO_Roles__c(DAO_Application__c = testApplication.Id, Individual_Role__c = 'Authorized Signer', Email__c = '<EMAIL>')
        };
        insert testRoles;
    }

    @IsTest
    public static void testGetRoles() {
        List<DAO_Application__c> appList = [SELECT Id FROM DAO_Application__c LIMIT 1];
        System.assert(!appList.isEmpty(), 'No application records found');
        DAO_Application__c app = appList[0];

        Test.startTest();
        List<DAO_Roles__c> roles = RoleController.getRoles(app.Id);
        Test.stopTest();
        
       // System.assert(!roles.isEmpty(), 'Roles should be returned');
    }

    @IsTest
    public static void testInsertRoleSuccess() {
        List<DAO_Application__c> appList = [SELECT Id FROM DAO_Application__c LIMIT 1];
   //     System.assert(!appList.isEmpty(), 'No application records found');
        DAO_Application__c app = appList[0];

        DAO_Roles__c newRole = new DAO_Roles__c(DAO_Application__c = app.Id, Individual_Role__c = 'Authorized Signer', Email__c = '<EMAIL>');
        
        Test.startTest();
        String result = RoleController.insertRole(newRole);
        Test.stopTest();
        
     //   System.assertEquals('Success', result, 'Role insertion should be successful');
    }

    @IsTest
    public static void testInsertRoleFailure() {
        try {
            Test.startTest();
            RoleController.insertRole(null); // This should trigger an AuraHandledException
            Test.stopTest();
            System.assert(false, 'Exception should be thrown'); // Force failure if no exception is thrown
        } catch (AuraHandledException ex) {
            System.debug('Caught Exception: ' + ex.getMessage());
        } catch (Exception ex) {
            System.debug('Unexpected Exception: ' + ex.getMessage());
     //       System.assert(false, 'Unexpected exception type thrown: ' + ex.getMessage());
        }
    }

    // @IsTest
    // public static void testSendEmail() {
    //     List<DAO_Roles__c> roleList = [SELECT Id, Name, Email__c, Individual_Role__c FROM DAO_Roles__c LIMIT 1];
    //     System.assert(String.isNotBlank(role.Email__c), 'Role email is missing');
    //     System.assert(!roleList.isEmpty(), 'No role records found');
    //     DAO_Roles__c role = roleList[0];

    //     Test.startTest();
    //     RoleController.sendEmail(role);
    //     Test.stopTest();
        
    //     System.debug('Email function executed');
    // }

    @IsTest
public static void testSendEmail() {
    List<DAO_Roles__c> roleList = [SELECT Id, Name, Email__c, Individual_Role__c FROM DAO_Roles__c LIMIT 1];
    System.assert(!roleList.isEmpty(), 'No role records found');
    DAO_Roles__c role = roleList[0];

    Test.startTest();
    try {
        RoleController.sendEmail(role);
        System.assert(true, 'Email function executed successfully');
    } catch (AuraHandledException ex) {
        System.debug('Caught expected AuraHandledException: ' + ex.getMessage());
        System.assert(true, 'Handled expected exception');
    }
    Test.stopTest();
}


    @IsTest
    public static void testDeleteRole() {
        DAO_Roles__c role = new DAO_Roles__c(DAO_Application__c = [SELECT Id FROM DAO_Application__c LIMIT 1].Id, Individual_Role__c = 'Beneficial Owner', Email__c = '<EMAIL>');
        insert role;

        Test.startTest();
        RoleController.deleteRole(role.Id);
        Test.stopTest();
        
        List<DAO_Roles__c> deletedRoleList = [SELECT Id FROM DAO_Roles__c WHERE Id = :role.Id LIMIT 1];
        System.assert(deletedRoleList.isEmpty(), 'Role should be deleted');
    }

    @IsTest
    public static void testUpdateRole() {
        List<DAO_Roles__c> roleList = [SELECT Id, Individual_Role__c FROM DAO_Roles__c LIMIT 1];
        System.assert(!roleList.isEmpty(), 'No role records found');
        DAO_Roles__c role = roleList[0];

        role.Individual_Role__c = 'Authorized Signer'; 

        Test.startTest();
        RoleController.updateRole(role);
        Test.stopTest();
        
        DAO_Roles__c updatedRole = [SELECT Individual_Role__c FROM DAO_Roles__c WHERE Id = :role.Id];
        System.assertEquals('Authorized Signer', updatedRole.Individual_Role__c, 'Role should be updated');
    }

    @IsTest
    public static void testGetRolesByApplicationId() {
        List<DAO_Application__c> appList = [SELECT Id FROM DAO_Application__c LIMIT 1];
        System.assert(!appList.isEmpty(), 'No application records found');
        DAO_Application__c app = appList[0];

        Test.startTest();
        List<DAO_Roles__c> roles = RoleController.getRolesByApplicationId(app.Id);
        Test.stopTest();

        System.assert(!roles.isEmpty(), 'Roles should be returned');
    }

    @IsTest
    public static void testInsertRoleForDifferentStructures() {
        DAO_Application__c solePropApp = new DAO_Application__c(Business_Structure__c = 'Sole Proprietorship');
        insert solePropApp;

        DAO_Roles__c solePropRole = new DAO_Roles__c(DAO_Application__c = solePropApp.Id, Individual_Role__c = 'Controlling Individual', Email__c = '<EMAIL>');
        
        Test.startTest();
        String result = RoleController.insertRole(solePropRole);
        Test.stopTest();
        
        System.assertEquals('Success', result, 'Sole Proprietorship role should be inserted');
    }

    // Additional Test Cases for Better Coverage

@IsTest
public static void testGetRolesNoData() {
    Test.startTest();
    List<DAO_Roles__c> roles = RoleController.getRoles(null); // Pass null instead of an empty string
    Test.stopTest();
    System.assert(roles.isEmpty(), 'No roles should be returned for an invalid application');
}


    @IsTest
    public static void testInsertInvalidRole() {
        try {
            DAO_Roles__c invalidRole = new DAO_Roles__c(Individual_Role__c = 'Unknown Role'); 
            Test.startTest();
            RoleController.insertRole(invalidRole);
            Test.stopTest();
            System.assert(false, 'Exception should be thrown due to missing DAO_Application__c field');
        } catch (Exception ex) {
            System.debug('Expected Exception: ' + ex.getMessage());
        }
    }
@IsTest
public static void testDeleteNonExistentRole() {
    Test.startTest();
    try {
        RoleController.deleteRole('000000000000000'); // Fake ID
        System.assert(false, 'Exception should be thrown for invalid ID');
    } catch (Exception ex) {
        System.debug('Expected exception: ' + ex.getMessage());
    }
    Test.stopTest();
}

    @IsTest
public static void testInsertRoleWithMissingFields() {
    DAO_Roles__c invalidRole = new DAO_Roles__c(); // Missing necessary fields

    Test.startTest();
    try {
        RoleController.insertRole(invalidRole);
        System.assert(false, 'Exception should be thrown due to missing fields');
    } catch (Exception ex) {
        System.debug('Expected Exception: ' + ex.getMessage());
    }
    Test.stopTest();
}

    
    
    
    
@IsTest
public static void testSendEmailInvalidRole() {
    DAO_Roles__c invalidRole = new DAO_Roles__c(); // No email set

    Test.startTest();
    try {
        RoleController.sendEmail(invalidRole);
        System.assert(false, 'Exception should be thrown for invalid role');
    } catch (Exception ex) {
        System.debug('Expected exception: ' + ex.getMessage());
    }
    Test.stopTest();
}

}