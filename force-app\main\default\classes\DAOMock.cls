/**
 * @description       : 
 * <AUTHOR> Zennify
 * @last modified on  : 12-04-2024
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
 global class DAOMock implements Callable{

    // public Object invokeMethod ( String methodName, Map<String,Object> input, Map<String,Object> output, Map<String,Object> options){
    //     if(methodName == 'getMockResponse'){
    //         output.put('mockResponse', getMockResponse());
    //         return true;
    //     }
    //     return null; 
    // }
    global Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>)args.get('input');
        Map<String, Object> output = (Map<String, Object>)args.get('output');
        Map<String, Object> options = (Map<String, Object>)args.get('options');
       
        if (action == 'getMockResponse') {
            output.put('mockResponse', getMockResponse());
            return true;
        } 
        return null;
    }
    /**
     * Method to return a mock response.
     *
     * @return List<Map<String, Object>> representing the mock response.
     */
    global static List<Map<String, Object>> getMockResponse() {
        // Create the mock response
        List<Map<String, Object>> mockResponse = new List<Map<String, Object>>();
        
        // Create the first record
        Map<String, Object> record = new Map<String, Object>();
        record.put('firstName', 'AEXTYON');
        record.put('lastName', 'YEFAD');
        record.put('Result', 'HIGH');
        record.put('EmailRisk', 'NONE');
        record.put('PhoneRisk', 'NONE');
        record.put('AddressRisk', 'NONE');
        record.put('FraudRisk', 'HIGH');
        record.put('KycRisk', 'LOW');
        record.put('Other', 'MEDIUM');
        
        // Add comments
        record.put('Comments', new List<String>{
            'DOB cannot be resolved to the individual',
            'DOB cannot be resolved to the individual',
            'Member fraud score is high',
            'Applicant identity cannot be correlated with address. Please obtain proof of address from applicant.'
        });
        
        // Add guidance
        record.put('Guidance', new List<String>{
            'Member fraud score is high. Please send to Account Fraud for review.',
            'Applicant identity cannot be correlated with address. Please obtain proof of address from applicant.'
        });
        
        // Add the record to the mock response
        mockResponse.add(record);
        
        // Return the mock response
        return mockResponse;
    }
}