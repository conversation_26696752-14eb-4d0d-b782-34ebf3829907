/**
 * @description       : Provide test coverage for DAORemoteUtil class
 * <AUTHOR> Zennify
 * @last modified on  : 06-13-2025
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
@isTest
public class DAORemoteUtilTest {
    @isTest
    static void testGenerateStrongPassword() {
        String password = DAORemoteUtil.generateStrongPassword(20, 'abc');
        System.assertNotEquals(null, password, 'Password should not be null');
        System.assertEquals(20, password.length(), 'Password should have the requested length');
    }

    @isTest
    static void testGenerateStrongPasswordDefaultLength() {
        String password = DAORemoteUtil.generateStrongPassword(null, null);
        System.assertEquals(16, password.length(), 'Default length should be 16');
    }

    @isTest
    static void testGenerateStrongPasswordExclusion() {
        String exclude = 'abcABC';
        String password = DAORemoteUtil.generateStrongPassword(18, exclude);
        for (Integer i = 0; i < exclude.length(); i++) {
            System.assertEquals(-1, password.indexOf(exclude.substring(i, i+1)), 'Password should not contain excluded chars');
        }
    }

    @isTest
    static void testGenerateAlias() {
        String alias = DAORemoteUtil.generateAlias();
        System.assertEquals(8, alias.length(), 'Alias should be 8 characters');
    }

    @isTest
    static void testShuffleCharacters() {
        List<String> chars = new List<String>{'a','b','c','d','e'};
        List<String> shuffled = DAORemoteUtil.shuffleCharacters(chars);
        System.assertEquals(chars.size(), shuffled.size(), 'Shuffled list should have same size');
    }

    @isTest
    static void testGetRandomChar() {
        String pool = 'xyz';
        String ch = DAORemoteUtil.getRandomChar(pool);
        System.assertEquals(1, ch.length(), 'Random char should be a single character');
        System.assert(pool.contains(ch), 'Random char should be from pool');
    }
}