<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>direction</name>
                    <value>ltr</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>display</name>
                    <value>Display OmniScript on page</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>inlineLabel</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>inlineVariant</name>
                    <value>brand</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>language</name>
                    <value>English</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>subType</name>
                    <value>application</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>theme</name>
                    <value>lightning</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>type</name>
                    <value>rcu</value>
                </componentInstanceProperties>
                <componentName>runtime_omnistudio:omniscript</componentName>
                <identifier>runtime_omnistudio_omniscript</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>RCU DAO</masterLabel>
    <template>
        <name>flexipage:defaultAppHomeTemplate</name>
    </template>
    <type>AppPage</type>
</FlexiPage>
