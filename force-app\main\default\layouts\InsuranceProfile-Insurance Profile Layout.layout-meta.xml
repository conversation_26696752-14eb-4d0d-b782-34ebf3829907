<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MajorCitationCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsDrugAbuser</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsGoodDriver</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsAlcoholConsumer</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsAlcoholAbuser</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>ContactId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MinorCitationCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsHighRiskOccupation</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsGoodStudent</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsTobaccoConsumer</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
