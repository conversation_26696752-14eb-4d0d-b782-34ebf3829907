@isTest
public class PicklistMetadataControllerTest {

    @testSetup
    static void setupTestData() {
        // Insert a DAO_Application__c record
        DAO_Application__c app = new DAO_Application__c();
        insert app;

        // Insert DAO_Roles__c
        DAO_Roles__c role = new DAO_Roles__c(DAO_Application__c = app.Id);
        insert role;

        // Insert DAO_Application_Documents__c
        DAO_Application_Documents__c doc = new DAO_Application_Documents__c(
            DAO_Application__c = app.Id,
            Document_Type__c = 'Identification',
            Description__c = 'Sample Doc',
            Role__c = 'Controlling',
            DAO_Roles__c = role.Id
        );
        insert doc;

        // Create dummy ContentVersion
        ContentVersion cv = new ContentVersion(
            Title = 'Test File',
            PathOnClient = 'TestFile.txt',
            VersionData = Blob.valueOf('Sample content')
        );
        insert cv;
    }

    @isTest
    static void testGetFirstPicklistValues() {
        List<String> values = PicklistMetadataController.getFirstPicklistValues();
        System.assertNotEquals(null, values);
    }

    @isTest
    static void testGetApplicationDocuments() {
        DAO_Application__c app = [SELECT Id FROM DAO_Application__c LIMIT 1];
        List<DAO_Application_Documents__c> docs = PicklistMetadataController.getApplicationDocuments(app.Id);
        System.assertNotEquals(null, docs);
    }

    @isTest
    static void testGetRolesForApplication() {
        DAO_Application__c app = [SELECT Id FROM DAO_Application__c LIMIT 1];
        List<DAO_Roles__c> roles = PicklistMetadataController.getRolesForApplication(app.Id);
        System.assertNotEquals(null, roles);
    }

    @isTest
    static void testUploadFilesToApplication() {
        DAO_Application__c app = [SELECT Id FROM DAO_Application__c LIMIT 1];
        DAO_Roles__c role = [SELECT Id FROM DAO_Roles__c LIMIT 1];
        ContentDocument cd = [SELECT Id FROM ContentDocument LIMIT 1];

        List<Map<String, Object>> documents = new List<Map<String, Object>>();
        Map<String, Object> docMap = new Map<String, Object>{
            'id' => cd.Id,
            'documentType' => 'Identification',
            'description' => 'Test Desc',
            'role' => 'Controlling',
            'userrole' => role.Id
        };
        documents.add(docMap);

        Test.startTest();
        Boolean result = PicklistMetadataController.uploadFilesToApplication(documents, app.Id);
        Test.stopTest();

        System.assertEquals(true, result);
    }

    @isTest
    static void testDeleteFile() {
        ContentDocument cd = [SELECT Id FROM ContentDocument LIMIT 1];
        Test.startTest();
        PicklistMetadataController.deleteFile(cd.Id);
        Test.stopTest();
    }
    /*
    @isTest
    static void testCreateApplicationDocuments() {
        DAO_Application__c app = [SELECT Id FROM DAO_Application__c LIMIT 1];
        DAO_Roles__c role = [SELECT Id FROM DAO_Roles__c LIMIT 1];

        List<Map<String, Object>> documents = new List<Map<String, Object>>();
        Map<String, Object> docMap = new Map<String, Object>{
            'documentType' => 'Identification',
            'description' => 'Test Desc',
            'role' => role.Id,
            'userrole' => role.Id
        };
        documents.add(docMap);

        Test.startTest();
        PicklistMetadataController.createApplicationDocuments(documents, app.Id);
        Test.stopTest();
    }
*/
    @isTest
    static void testGetFileSizeByDocumentId() {
        ContentDocument cd = [SELECT Id FROM ContentDocument LIMIT 1];
        List<Id> docIds = new List<Id>{cd.Id};
        List<ContentVersion> versions = PicklistMetadataController.getFileSizeByDocumentId(docIds);
        System.assertNotEquals(null, versions);
    }

    @isTest
    static void testUpdateRoleIdAttach() {
        DAO_Roles__c role = [SELECT Id FROM DAO_Roles__c LIMIT 1];
        Test.startTest();
        PicklistMetadataController.updateRoleIdAttach(role.Id);
        Test.stopTest();
    }

    @isTest
    static void testGetDocumentTypes() {
        Test.startTest();
        List<String> result = PicklistMetadataController.getDocumentTypes('Corp', 'Manager', true);
        Test.stopTest();
        System.assertNotEquals(null, result);
    }
}