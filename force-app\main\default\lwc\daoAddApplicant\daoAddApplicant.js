import { LightningElement, track, wire, api } from 'lwc';
import {OmniscriptBaseMixin} from 'omnistudio/omniscriptBaseMixin';
import getRoles from '@salesforce/apex/RoleController.getRoles';
import insertRole from '@salesforce/apex/RoleController.insertRole';
import sendEmail from '@salesforce/apex/RoleController.sendEmail';
import deleteRole from '@salesforce/apex/RoleController.deleteRole';
import updateRole from '@salesforce/apex/RoleController.updateRole';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { refreshApex } from '@salesforce/apex';
import  siteUrl  from '@salesforce/label/c.SiteUrl';

export default class DaoAddApplicant extends OmniscriptBaseMixin(LightningElement) {
    @track roles = [];
    @track firstName = '';
    @track middleName = '';
    @track lastName = '';
    @track email = '';
    @track ownership = '';
    @track selectedRoleType = 'Individual';
    @api omniScript;
    @track showSavebutton = false;
    @track showEditButtonbutton = true;
    applicationId;

    roleOptions = [
        { label: 'Controlling Individual', value: 'Controlling Individual' },
        { label: 'Beneficial Owner', value: 'Beneficial Owner' },
        { label: 'Authorized Signer', value: 'Authorized Signer' },
    ];

    connectedCallback() {
        this.applicationId = this.omniJsonData.DRId_DAO_Application__c;
        console.log('ID recieved : ',this.applicationId);
         this.IsCommunityPage = this.omniJsonData.IsCommunityPage;
        console.log('OUTPUT : IsCommunityPage101',this.IsCommunityPage);
        this.refreshInterval = setInterval(() => {
        this.refreshData();
    }, 5000); // Refresh every 5 seconds
    }

     handleFieldChange(event) {
        const field = event.currentTarget.dataset.field;
        this[field] = event.target.value;
    }

    handleRoleTypeChange(event) {
        this.selectedRoleType = event.detail.value;
    }

    handleAddRole() {
        
        if (!this.firstName || !this.lastName || !this.email || !this.selectedRoleType) {
            this.showToast('Error', 'Required fileds are Missing', 'error');
            return;
        }

        if (this.ownership > 100) {
            this.showToast('Error', 'Ownership percentage should be less than or equal to 100', 'error');
            return;
        }
        const roleData = {
        Name: this.firstName + ' ' + this.middleName + ' ' + this.lastName,
        First_Name__c: this.firstName,
        Last_Name__c: this.lastName,
        Middle_Name__c: this.middleName,
        Email__c: this.email,
        Individual_Role__c: this.selectedRoleType,
        Business_Owned__c: this.ownership,
        DAO_Application__c: this.applicationId,
    };
         insertRole({ roleData })
            .then((result) => {
                 if (result === 'Error') {
                     this.showToast('Error', 'Role Insert limit Exceed', 'error');
                 }

                return refreshApex(this.wiredResult);
            })
            .then(() => {
                this.firstName = '';
                this.middleName = '';
                this.lastName = '';
                this.email = '';
                this.ownership = '';
            })
            .catch(error => {
                console.log('Error inserting role:', error);
            });
    }


    @wire(getRoles, { applicationId: '$applicationId' }) 
    wiredData(value) {
        this.wiredResult = value;
        const { data, error } = value;
        if (data) {
            console.log('OUTPUT : ',data)
            console.log(JSON.stringify(data, null, 2));
            this.roles = data.map((role, index) => {
                return { 
                    ...role,
                    BusinessOwned : role.Business_Owned__c != null ? role.Business_Owned__c + '%':"",
                    isEditing: false,
                    disableButton: index === 0, // Only the first item will have disableButton as true
                };
                
            });// data.map(role => ({ ...role, isEditing: false}));
            console.log("Roles data",this.roles);
            this.omniApplyCallResp(
                { AllRoles: this.roles }
            );
        } else if (error) {
            console.error('Error fetching roles:', error);
        }
    }

    refreshData() {
    refreshApex(this.wiredResult);
}

handleEdit(event) {
    const roleId = event.currentTarget.dataset.id;
    this.roles = this.roles.map(role => {
        if (role.Id === roleId) {
            role.isEditing = true;
        } else {
            role.isEditing = false;
        }
        return role;
    });
}

    handleInputChange(event) {
        
        const roleId = event.currentTarget.dataset.id;
        const field = event.currentTarget.dataset.field;
        const value = event.detail.value;
        this.roles = this.roles.map(role => {
            if (role.Id === roleId) {
                role[field] = value;
            }
            return role;
        });
    }

handleSave(event) {
    const roleId = event.currentTarget.dataset.id;
    const updatedRole = this.roles.find(role => role.Id === roleId);
    
    updateRole({ role: updatedRole })
        .then(() => {
            this.roles = this.roles.map(role => {
                if (role.Id === roleId) {
                    role.isEditing = false;
                }
                return role;
            });
            return refreshApex(this.wiredResult);
        })
        .catch(error => {
            console.error('Error updating role:', error);
        });
}

    handleSendEmail(event) {
        const roleId = event.currentTarget.dataset.id;
        console.log('$roleId: ', roleId);
        const role = this.roles.find(role => role.Id === roleId);
        console.log(JSON.stringify(role, null, 2));
        console.log(JSON.stringify(this.roles, null, 2));
        if(role.Id != null){
            console.log('Method Called'+role);
            sendEmail({ mailData: role})
            .then(() => {
                this.showToast('Success', 'Mail Sent Successfully', 'success');
            })
            .catch(error => {
                console.log('Error Mail : ',error);
            });
        }
    }

    handleOpenRole(event) {
        const roleId = event.currentTarget.dataset.id;
        const encodId = btoa(roleId);
        console.log('IsCommunityPage -- ', this.IsCommunityPage);
        if(this.IsCommunityPage){
            const url = `${siteUrl}${encodId}&c__isAutoClosed=Yes`;
            window.open(url, '_blank');
        } else{
            const url = `/lightning/page/omnistudio/omniscript?omniscript__type=rcu&omniscript__subType=roleUser&omniscript__language=English&omniscript__theme=lightning&omniscript__tabIcon=custom:custom18&omniscript__tabLabel=RCU_DAO_Role&c__ContextId=${encodId}`;
            window.open(url, '_blank');       
        }
        
       
    }

    handleDelete(event) {
        const roleId = event.currentTarget.dataset.id;

        deleteRole({ roleId: roleId })
            .then(() => {
                this.roles = this.roles.filter(role => role.Id !== roleId);
                return refreshApex(this.wiredResult);
            })
            .catch(error => {
            });
    }

     showToast(title, message, variant) {
        const evt = new ShowToastEvent({
            title: title,
            message: message,
            variant: variant,
        });
        this.dispatchEvent(evt);
    }
}