import { LightningElement } from 'lwc';
import RIGHT_BACKGROUND_IMAGE from '@salesforce/resourceUrl/CDAO_RightBackgroundImage';
import LEFT_BACKGROUND_IMAGE from '@salesforce/resourceUrl/CDAO_LeftBackgroundImage';
import SHARED_STYLES from '@salesforce/resourceUrl/CDAO_SharedStyles';
import { loadStyle } from 'lightning/platformResourceLoader';

import getApplication from '@salesforce/apex/rcuConsumerDAO.getApplication';
import saveApplication from '@salesforce/apex/rcuConsumerDAO.saveApplication';

// Import constants
import { 
    SCREENS, 
    SCREEN_SEQUENCE, 
    SCREEN_DATA_MAP, 
    SCREENS_REQUIRING_SAVE,
    ERROR_MESSAGES,
    DEFAULT_APPLICATION_DATA,
    FIELD_MAPPINGS,
    CSS_CLASSES,
    EVENTS
} from './rcuConsumerDAOConstants';

export default class RcuConsumerDao extends LightningElement {
    // Core properties
    rightBackgroundImageUrl = RIGHT_BACKGROUND_IMAGE;
    leftBackgroundImageUrl = LEFT_BACKGROUND_IMAGE;
    
    // Screen management
    screens = SCREEN_SEQUENCE.slice(0, 4); // Only use the first 4 screens for now
    currentScreenIndex = 0;
    
    // Application data - will be populated from server
    applicationData = JSON.parse(JSON.stringify(DEFAULT_APPLICATION_DATA));
    
    // Lifecycle hooks
    connectedCallback() {
        loadStyle(this, SHARED_STYLES)
            .then(() => {})
            .catch(error => {});
        
        // Load application data from server
        this.loadApplicationData();
    }
    
    // Load data from server
    loadApplicationData() {
        return new Promise((resolve, reject) => {
            getApplication()
                .then(result => {
                    this.applicationData.application = result;
                    resolve(result);
                })
                .catch(error => {
                    console.error('Error loading application data:', error);
                    reject(error);
                });
        });
    }
    
    // Save data to server
    saveApplicationData() {
        return new Promise((resolve, reject) => {
            try {
                // Make sure we have data to save
                if (!this.applicationData.application) {
                    console.error(ERROR_MESSAGES.NO_APPLICATION);
                    reject(ERROR_MESSAGES.NO_APPLICATION);
                    return;
                }
                
                // Deep clone the data to avoid reference issues
                const appData = JSON.parse(JSON.stringify(this.applicationData.application));
                const applicationDataJson = JSON.stringify(appData);
                
                saveApplication({ applicationData: applicationDataJson })
                    .then(result => {
                        if (result && result.startsWith && result.startsWith('Error:')) {
                            console.error('Apex returned error:', result);
                            reject(result);
                        } else {
                            resolve(result);
                        }
                    })
                    .catch(error => {
                        console.error('Error in Apex saveApplication call:', error);
                        reject(error);
                    });
            } catch (error) {
                console.error('Error preparing application data:', error);
                reject('Error preparing application data: ' + error.message);
            }
        });
    }
    
    // Direct handler for member info
    handleSaveInfo(event) {
        if (event && event.detail) {
            try {
                // If application doesn't exist, load it first
                if (!this.applicationData.application) {
                    // Load application data from server first
                    this.loadApplicationData()
                        .then(() => {
                            // Then update and save the data
                            this.updateApplicationData(SCREENS.PRIMARY_MEMBER, event.detail);
                        })
                        .catch(error => {
                            console.error('Error loading application data:', error);
                        });
                } else {
                    // Application exists, update and save directly
                    this.updateApplicationData(SCREENS.PRIMARY_MEMBER, event.detail);
                }
            } catch (error) {
                console.error('Error in handleSaveInfo:', error);
            }
        }
    }
    
    get currentScreenNumber() {
        return this.currentScreenIndex + 1;
    }

    get isWelcomePage() {
        return this.screens[this.currentScreenIndex] === SCREENS.WELCOME;
    }

    get isQuestionScreen() {
        return this.screens[this.currentScreenIndex] === SCREENS.QUESTIONS;
    }

    get isPrimaryMemberInfoScreen() {
        return this.screens[this.currentScreenIndex] === SCREENS.PRIMARY_MEMBER;
    }

    get isAddressAndIdScreen() {
        return this.screens[this.currentScreenIndex] === SCREENS.ADDRESS_ID;
    }

    get getPrimaryMemberData() {
        return this.applicationData.application.primaryApplicant;
    }

   // Handle next event from any screen
    handleNext(event) {
        const screenData = event.detail || {};
        const currentScreen = this.screens[this.currentScreenIndex];
        
        if (SCREENS_REQUIRING_SAVE.includes(currentScreen)) {
            // Update and save data
            this.updateApplicationData(currentScreen, screenData)
                .then(result => {
                    // Move to next screen after successful save
                    this.currentScreenIndex++;
                })
                .catch(error => {
                    console.error('Error saving application data:', error);
                    // Show error message
                    this.dispatchEvent(new CustomEvent(EVENTS.ERROR, {
                        detail: {
                            title: 'Error Saving Data',
                            message: ERROR_MESSAGES.SAVE_ERROR
                        }
                    }));
                });
        } else {
            // For screens that don't need to save data, just store locally and navigate
            // Store screen data in the appropriate property
            if (SCREEN_DATA_MAP[currentScreen]) {
                this.applicationData[SCREEN_DATA_MAP[currentScreen]] = screenData;
            }
            
            // Navigate to next screen
            this.currentScreenIndex++;
        }
    }


    handleBack() {
        if (this.currentScreenIndex > 0) {
            this.currentScreenIndex--;
        }
    }

    handleScreenNavigation(event) {
        const screenName = event.detail.screen;
        const screenIndex = this.screens.indexOf(screenName);
        
        if (screenIndex !== -1) {
            this.currentScreenIndex = screenIndex;
        }
    }

    handleAddApplicant(event) {
        // Implement the logic to add an applicant
    }


    renderedCallback() {
        // Set background image URLs dynamically
        const rightBgElement = this.template.querySelector(`.${CSS_CLASSES.RIGHT_BG}`);
        const leftBgElement = this.template.querySelector(`.${CSS_CLASSES.LEFT_BG}`);
        
        if (rightBgElement) {
          rightBgElement.style.backgroundImage = `url(${this.rightBackgroundImageUrl})`;
        }
        
        if (leftBgElement) {
          leftBgElement.style.backgroundImage = `url(${this.leftBackgroundImageUrl})`;
        }
    }


    // Internal helper to update specific parts of the application
    updateApplicationData(screenName, screenData) {
        // Create a new application object to avoid proxy issues
        const newApplication = JSON.parse(JSON.stringify(this.applicationData.application));
        
        // Ensure primaryApplicant exists
        if (!newApplication.primaryApplicant) {
            newApplication.primaryApplicant = {};
        }
        
        // Update the appropriate section based on screen name
        switch(screenName) {
            case SCREENS.PRIMARY_MEMBER:
                newApplication.primaryApplicant.MemberInformation = JSON.parse(JSON.stringify(screenData));
                break;
                
            case SCREENS.ADDRESS_ID:
                const addressFields = FIELD_MAPPINGS.ADDRESS_ID;
                newApplication.primaryApplicant[addressFields.PHYSICAL_ADDRESS] = screenData[addressFields.PHYSICAL_ADDRESS];
                newApplication.primaryApplicant[addressFields.MAILING_ADDRESS] = screenData[addressFields.MAILING_ADDRESS];
                newApplication.primaryApplicant[addressFields.SAME_ADDRESS] = screenData[addressFields.SAME_ADDRESS];
                newApplication.primaryApplicant[addressFields.IDENTITY_INFO] = screenData[addressFields.IDENTITY_INFO];
                break;
                
            case SCREENS.WELCOME:
                // Handle welcome page data if needed
                break;
                
            case SCREENS.QUESTIONS:
                const questionFields = FIELD_MAPPINGS.QUESTIONS;
                newApplication[questionFields.MEMBERSHIP_STATUS] = screenData[questionFields.MEMBERSHIP_STATUS];
                newApplication[questionFields.ACCOUNT_TYPE] = screenData[questionFields.ACCOUNT_TYPE];
                break;
        }
        
        // Update the application data with the new object
        this.applicationData.application = newApplication;
        
        // Also store in the screen-specific property for component reuse
        if (SCREEN_DATA_MAP[screenName]) {
            this.applicationData[SCREEN_DATA_MAP[screenName]] = JSON.parse(JSON.stringify(screenData));
        }
        
        // Save to server and return the promise
        return this.saveApplicationData();
    }

    // Update loadApplicationData to return a promise for chaining
    loadApplicationData() {
        return new Promise((resolve, reject) => {
            getApplication()
                .then(result => {
                    this.applicationData.application = result;
                    resolve(result);
                })
                .catch(error => {
                    console.error('Error loading application data:', error);
                    reject(error);
                });
        });
    }


}

















