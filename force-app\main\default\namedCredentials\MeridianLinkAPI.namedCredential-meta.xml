<?xml version="1.0" encoding="UTF-8"?>
<NamedCredential xmlns="http://soap.sforce.com/2006/04/metadata">
    <allowMergeFieldsInBody>false</allowMergeFieldsInBody>
    <allowMergeFieldsInHeader>false</allowMergeFieldsInHeader>
    <calloutStatus>Enabled</calloutStatus>
    <generateAuthorizationHeader>true</generateAuthorizationHeader>
    <label>MeridianLinkAPI</label>
    <namedCredentialParameters>
        <parameterName>Url</parameterName>
        <parameterType>Url</parameterType>
        <parameterValue>https://tapi.redwoodcu.org:8196/api/buildModel</parameterValue>
    </namedCredentialParameters>
    <namedCredentialParameters>
        <externalCredential>MeridianLinkOAuthToken</externalCredential>
        <parameterName>ExternalCredential</parameterName>
        <parameterType>Authentication</parameterType>
    </namedCredentialParameters>
    <namedCredentialParameters>
        <parameterName>AllowedNamespace_MFyHTnd1746639216510</parameterName>
        <parameterType>AllowedManagedPackageNamespaces</parameterType>
        <parameterValue>omnistudio</parameterValue>
    </namedCredentialParameters>
    <namedCredentialType>SecuredEndpoint</namedCredentialType>
</NamedCredential>
