<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LeadId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ExternalIdentifier</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EffectiveStartDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EffectiveEndDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Type</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Status</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Members Summary</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TotalMembers</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MbrWithoutDependentCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MemberWithOneChildCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MemberWithDependentCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FullTimeMemberCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>TotalDependents</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MemberWithSpouseCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MbrWithMoreThan1ChldCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MemberOptOutCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PartTimeMemberCount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>Name</fields>
        <fields>Account</fields>
        <fields>FirstName</fields>
        <fields>Lastname</fields>
        <fields>RelationshipToPrimaryMember</fields>
        <fields>OptOutPlanTypes</fields>
        <relatedList>GroupCensusMembers</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>Contract</fields>
        <fields>EnrollmentStartDate</fields>
        <fields>EnrollmentEndDate</fields>
        <fields>EnrollmentWaitingPeriod</fields>
        <relatedList>Contracts</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
