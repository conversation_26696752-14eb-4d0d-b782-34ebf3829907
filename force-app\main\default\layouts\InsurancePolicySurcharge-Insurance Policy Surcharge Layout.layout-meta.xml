<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SurchargeAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ApplicableObjectType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SourceSystem</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SourceSystemIdentifier</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>InsurancePolicyId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InsurancePolicyAssetId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InsurancePolicyParticipantId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InsurancePolicyCoverageId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>InsurancePolicyTransactionId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
