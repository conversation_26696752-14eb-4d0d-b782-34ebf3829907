<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Detail Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsActive</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>RecordPreference</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>MasterLabel</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsDefaultModel</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsModelLocked</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>DeveloperName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ModelDescription</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <miniLayout>
        <fields>IsActive</fields>
        <fields>RecordPreference</fields>
    </miniLayout>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
