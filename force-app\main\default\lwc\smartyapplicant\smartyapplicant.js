import { LightningElement, api } from "lwc";
import { OmniscriptBaseMixin } from "omnistudio/omniscriptBaseMixin";
import getAddress from "@salesforce/apex/DAOAddressAutoComplete.getAddress";

export default class smartyapplicant extends OmniscriptBaseMixin(LightningElement) {
  name = "";
  phone = "";
  website = "";
  accountNumber = "";
  fullAddress = "";
  doneTypingInterval = 300;
  typingTimer;
  suggestion;
  city = "";
  state = "";
  postalCode = "";
  street = "";
  isSameBusinessAddress="";
  showAddressForm = false;
  isSameAdd;
  _omniJsonData;
  @api omniScript;
  @api screentype;

  @api
    set omniJsonData(data) {
        this._omniJsonData = data;
        if(this._omniJsonData){
          console.log('%c$Screentype:', 'color: red;', this.screentype);
          console.log('%c$OmniJsonData:', 'color: blue;font-weight:bold;', JSON.stringify(data, null, 2));
          this.initializeConnectedCallback();
        }
    }

    get omniJsonData() {
        return this._omniJsonData;
    }

  initializeConnectedCallback() {
    
    if (this.screentype == "physicalAddressApplicant" && this._omniJsonData?.RoleInfo?.Same_as_Business_Address) {
      console.log('Checkpoint 2');
      this.street = this._omniJsonData.rolestreetAddress;
      this.city = this._omniJsonData.rolecityAddress;
      this.state = this._omniJsonData.rolestateAddress;
      this.postalCode = this._omniJsonData.rolezipcode;
      this.fullAddress = this._omniJsonData.rolephysicalFullAddres;
      
    }else if (this.screentype == "physicalAddressApplicant") {
      console.log('Checkpoint 2');
      this.street = this._omniJsonData?.rolestreetAddress1;
      this.city = this._omniJsonData?.rolecityAddress1;
      this.state = this._omniJsonData?.rolestateAddress1;
      this.postalCode = this._omniJsonData?.rolezipcode1;
      this.fullAddress = this._omniJsonData?.rolephysicalFullAddres1;
    }
    const state = this.omniGetSaveState();
    if (state) this.value = state;
  }

  handleInputChange(event) {
    clearTimeout(this.typingTimer);
    console.log("event value : " + event.detail.value);
    let value = event.target.value;
    this.typingTimer = setTimeout(() => {
      if (value) {
        getAddress({ search: value })
          .then((result) => {
            let temp = JSON.parse(result);
            let suggestionList = [];
            temp.suggestions.forEach((elem) => {
              let address =
                "" +
                elem.street_line +
                "," +
                elem.city +
                "," +
                elem.state +
                "," +
                elem.zipcode;

              suggestionList.push({ id: Date.now(), value: address });
            });
            console.log("this.suggestion " + suggestionList);
            this.suggestion = suggestionList;
            //       const suggestionPane = this.template.querySelector('.slds-popover');
            //     suggestionPane.classList.remove('slds-hide');
          })
          .catch((error) => {
            console.log("## error in creating records: " + error);
          });
      }
    }, this.doneTypingInterval);
  }

  setAddress(event) {
    let placeId = event.currentTarget.dataset.value.split(",");
    let key = event.currentTarget.dataset.key;
    this.suggestion = undefined;
    console.log("use diffrent addres", this._omniJsonData.sameasphysical);
    this.street = placeId.length > 0 ? placeId[0] : "";
    this.city = placeId.length > 1 ? placeId[1] : "";
    this.state = placeId.length > 2 ? placeId[2] : "";
    this.postalCode = placeId.length > 3 ? placeId[3] : "";
    this.country = placeId.length > 4 ? placeId[4] : "";
    this.fullAddress =
      this.street +
      " " +
      this.city +
      " " +
      this.state +
      " " +
      this.postalCode +
      " " +
      "US";
    this.omniSaveState({
      Address: placeId,
    });

    this.omniUpdateDataJson({
      Address: placeId,
    });
    let dataToPass;
    if (this.screentype == "physicalAddressApplicant") {
      dataToPass = {
        rolestreetAddress1: this.street,
        rolecityAddress1: this.city,
        rolestateAddress1: this.state,
        rolezipcode1: this.postalCode,
        rolephysicalFullAddres1: this.fullAddress,
      };
    }

    this.omniApplyCallResp(dataToPass);
  }
  handlePencilClick() {
    this.showAddressForm = !this.showAddressForm; // Toggle the visibility
  }

  handleAddressChange(event) {
    // Extract address details from event.detail
    const { street, city, province, postalCode } = event.detail;

    this.street = street || "";
    this.city = city || "";
    this.state = province || "";
    this.postalCode = postalCode || "";

    // Update the OmniScript data
    let dataToPass;
    if (this.screentype == "physicalAddressApplicant") {
      dataToPass = {
        rolestreetAddress1: this.street,
        rolecityAddress1: this.city,
        rolestateAddress1: this.state,
        rolezipcode1: this.postalCode,
        rolephysicalFullAddres1: this.fullAddress,
      };
    }
    
    this.omniApplyCallResp(dataToPass);
  }

  // Show pencil icon only if address is filled
  get isAddressFilled() {
    return this.fullAddress && this.fullAddress.trim() !== "";
  }
}