import { LightningElement, api } from 'lwc';
import { OmniscriptBaseMixin } from 'omnistudio/omniscriptBaseMixin';

export default class CustomNextButtoncmp extends OmniscriptBaseMixin(LightningElement) {
    @api showFirstButton = false;
    @api showSecondButton = false;

    filter;
    @api selectedOption;
    @api secondButtonLabel = 'Next';
    @api firstButtonLabel = 'Continue to Digital Banking';

    connectedCallback() {
        this.showSecondButton = true;
    }

    renderedCallback() {
        if (this.showSecondButton == true && this.selectedOption == 'Have BA and want other services') {
            this.showFirstButton = true;
            this.showSecondButton = false;
        }
        if (this.selectedOption != 'Have BA and want other services' && this.showSecondButton == false) {
            this.showFirstButton = false;
            this.showSecondButton = true;
        }

        if(this.template.querySelector('.second-button')){
            let ss = this.template.querySelector('.second-button');
            console.log('$ss: ',ss.innerText);
            const style = document.createElement('style');
            style.innerText = `
                .slds-button.slds-button_brand {
                    width: 80px !important;
                    height: 35px !important;
                    background: linear-gradient(45deg, rgb(4, 106, 56), rgb(132, 189, 0)) !important;
                    color: rgb(255, 255, 255) !important;
                    border-radius: 4px !important;
                    text-decoration: none !important;
                    font-weight: bold !important;
                    font-size: 14px !important;
                    border-color: rgb(167, 173, 170) !important;
                }
                .slds-button.slds-button_brand:hover {
                    background: #046a38;
                }
            `;
            this.template.querySelector('.second-button').appendChild(style);
        }
        if(this.template.querySelector('.first-button')){
            let ss = this.template.querySelector('.first-button');
            console.log('$ss: ',ss.innerText);
            const style = document.createElement('style');
            style.innerText = `
                .slds-button.slds-button_brand {
                    padding: 2px 0px !important;
                    background: linear-gradient(45deg, rgb(4, 106, 56), rgb(132, 189, 0)) !important;
                    color: rgb(255, 255, 255) !important;
                    border-radius: 5px !important;
                    text-decoration: none !important;
                    font-weight: bold !important;
                    font-size: 13px !important;
                    border-color: rgb(167, 173, 170) !important;
                }
                .slds-button.slds-button_brand:hover {
                    background: #046a38;
                }
            `;
            this.template.querySelector('.first-button').appendChild(style);
        }

    }
    NavigateToUrl() {
        window.location.href = 'https://digital.redwoodcu.org/login';
    }
    MoveToNextStep() {
        this.omniNextStep();
    }

}