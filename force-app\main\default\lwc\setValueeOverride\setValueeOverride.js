import { LightningElement } from 'lwc';
import omniscriptSetValues from 'omnistudio/omniscriptSetValues';
import {OmniscriptBaseMixin} from 'omnistudio/omniscriptBaseMixin';
export default class SetValueeOverride extends OmniscriptBaseMixin(omniscriptSetValues) {

connectedCallback() {
    super.connectedCallback();
}

execute(event){
    console.log('OUTPUT Called');
    pubsub.fire("checkMoveStep","movestep",{});
}

}