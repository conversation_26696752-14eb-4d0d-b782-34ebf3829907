<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Name</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>LoanApplicationId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LoanApplicationAssetId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LoanType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OtherLiabilityType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CreditorName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AccountNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>MonthlyPaymentAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>UnpaidBalanceAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ShouldBePaidInFull</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>IsJointLiability</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CreditLimitAmount</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LiabilityClass</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>OwnerId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>CreatedById</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>LastModifiedById</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
