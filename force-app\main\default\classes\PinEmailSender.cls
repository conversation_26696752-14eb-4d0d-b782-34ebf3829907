global with sharing class PinEmailSender implements omnistudio.VlocityOpenInterface {
    
    global Boolean invokeMethod(String methodName, Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        Boolean result = true;
        
        try {
            if (methodName.equals('sendEmailWithPin')) {
                String emailAddress = (String) inputMap.get('emailAddress');
                system.debug('emailAddress> '+emailAddress);
                String pin = generateRandomPin();
                sendEmail(emailAddress, pin);
                outMap.put('generatedPin', pin);
                
            } else {
                result = false; // Method not found
            }
        } catch (Exception e) {
            System.debug('Exception: ' + e.getMessage());
            result = false;
        }
        
        return result;
    }
    
    
    
    private String generateRandomPin() {
        Integer pin = Math.abs(Crypto.getRandomInteger());
        pin = Math.mod(pin, 10000); // Ensure the PIN is within 4 digits
        String pinStr = String.valueOf(pin);
        return pinStr.length() < 4 
            ? '0000'.substring(pinStr.length()) + pinStr 
            : pinStr; // Manually pad the PIN with leading zeros
    }
    
    private void sendEmail(String toAddress, String pin) {
        Messaging.SingleEmailMessage mail = new Messaging.SingleEmailMessage();
        mail.setToAddresses(new String[] { toAddress });
        mail.setSubject('Your 4-Digit PIN');
        mail.setPlainTextBody('Your PIN is: ' + pin);
        System.debug('mail>> '+mail);
        Messaging.sendEmail(new Messaging.SingleEmailMessage[] { mail });
    }
}