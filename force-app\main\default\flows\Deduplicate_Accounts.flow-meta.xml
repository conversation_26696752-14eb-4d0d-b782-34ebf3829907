<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_duplicate_email</name>
        <label>Send duplicate email</label>
        <locationX>176</locationX>
        <locationY>1250</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <stringValue><EMAIL></stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailSubject</name>
            <value>
                <stringValue>Duplicate email count</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailBody</name>
            <value>
                <elementReference>Dupsfoundemailbody</elementReference>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>63.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Add_dup_to_record_collection</name>
        <label>Add dup to record collection</label>
        <locationX>704</locationX>
        <locationY>782</locationY>
        <assignmentItems>
            <assignToReference>Duplicates</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_through_stored_accounts_list</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Increment_dup_counter</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Current_account_record_from_loop</name>
        <label>Current account record from loop</label>
        <locationX>616</locationX>
        <locationY>458</locationY>
        <assignmentItems>
            <assignToReference>CurrentAccount</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_through_accounts_looking_for_duplicates</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_through_stored_accounts_list</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Increment_dup_counter</name>
        <label>Increment dup counter</label>
        <locationX>704</locationX>
        <locationY>890</locationY>
        <assignmentItems>
            <assignToReference>Dup_Count</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_through_stored_accounts_list</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Store_account_records</name>
        <label>Store account records</label>
        <locationX>176</locationX>
        <locationY>242</locationY>
        <assignmentItems>
            <assignToReference>StoredAccounts</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_agent_account_records</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>Dup_Count</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>0.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_through_accounts_looking_for_duplicates</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Match_between_inner_and_outer_loop_accounts</name>
        <label>Match between inner and outer loop accounts</label>
        <locationX>836</locationX>
        <locationY>674</locationY>
        <defaultConnector>
            <targetReference>Loop_through_stored_accounts_list</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No match</defaultConnectorLabel>
        <rules>
            <name>Match</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_through_stored_accounts_list.PersonEmail</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>CurrentAccount.PersonEmail</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_through_stored_accounts_list.Id</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>CurrentAccount.Id</elementReference>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_through_stored_accounts_list.PersonEmail</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_dup_to_record_collection</targetReference>
            </connector>
            <label>Match</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <interviewLabel>Deduplicate Accounts {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Deduplicate Accounts</label>
    <loops>
        <name>Loop_through_accounts_looking_for_duplicates</name>
        <label>Loop through accounts looking for duplicates</label>
        <locationX>176</locationX>
        <locationY>350</locationY>
        <collectionReference>Get_agent_account_records</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Current_account_record_from_loop</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Send_duplicate_email</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_through_stored_accounts_list</name>
        <label>Loop through stored accounts list</label>
        <locationX>616</locationX>
        <locationY>566</locationY>
        <collectionReference>StoredAccounts</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Match_between_inner_and_outer_loop_accounts</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Loop_through_accounts_looking_for_duplicates</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_agent_account_records</name>
        <label>Get agent account records</label>
        <locationX>176</locationX>
        <locationY>134</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Store_account_records</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>012U8000001OAAYIA4</stringValue>
            </value>
        </filters>
        <filters>
            <field>PersonEmail</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <limit>
            <numberValue>20.0</numberValue>
        </limit>
        <object>Account</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>PersonEmail</queriedFields>
        <queriedFields>FirstName</queriedFields>
        <queriedFields>LastName</queriedFields>
        <sortField>PersonEmail</sortField>
        <sortOrder>Asc</sortOrder>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>50</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_agent_account_records</targetReference>
        </connector>
    </start>
    <status>Draft</status>
    <textTemplates>
        <name>Dupsfoundemailbody</name>
        <isViewedAsPlainText>false</isViewedAsPlainText>
        <text>&lt;p&gt;We found {!Dup_Count} duplicates:&lt;/p&gt;&lt;p&gt;{!Duplicates}&lt;/p&gt;</text>
    </textTemplates>
    <variables>
        <name>CurrentAccount</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>Dup_Count</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>Duplicates</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>StoredAccounts</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
</Flow>
