<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Salesforce Contract Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ContractNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>StartDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingFrequency</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>PaymentTerm</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CreditCardNumber</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CreditCardExpirationMonth</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>FirstNameOnCreditCard</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingName</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingEmail</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingAddressStreet</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingAddressState</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingAddressPostalCode</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SalesforceContractStatus</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>EndDate</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AutoRenewCode</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CreditCardType</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CreditCardExpirationYear</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>LastNameOnCreditCard</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingCompany</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingPhone</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingAddressCity</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>BillingAddressCountry</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <relatedLists>
        <fields>InvoiceNumber</fields>
        <fields>SalesforceInvoiceStatus</fields>
        <fields>Balance</fields>
        <fields>TotalAmount</fields>
        <fields>InvoiceDate</fields>
        <fields>DueDate</fields>
        <relatedList>SalesforceInvoices</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>QuoteNumber</fields>
        <fields>SalesforceQuoteStatus</fields>
        <fields>CreatedDate</fields>
        <relatedList>SalesforceQuotes</relatedList>
    </relatedLists>
    <showEmailCheckbox>false</showEmailCheckbox>
    <showRunAssignmentRulesCheckbox>false</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>false</showSubmitAndAttachButton>
</Layout>
