/**
 * @description       : 
 * <AUTHOR> Zennify
 * @last modified on  : 01-24-2025
 * @last modified by  : <PERSON><PERSON><PERSON>
**/
@IsTest
public with sharing class EncodeUserIdTest {
    @IsTest
    static void testInvokeMethod() {
        EncodeUserId encodeUserId = new EncodeUserId();
        Map<String, Object> inputMap = new Map<String, Object>();
        Map<String, Object> outMap = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>();
        args.put('input', inputMap);
        args.put('output', outMap);
        args.put('options', options);
        
        // Test with a valid role ID
        String validRoleId = 'YTFMVTgwMDAwMDB4c3FuTUFB';
        inputMap.put('ContextId', validRoleId);
        System.assertEquals(true, encodeUserId.call('encodeId',args));

        
        // Test with an invalid role ID
        String invalidRoleId = 'InvalidId';
        inputMap.put('ContextId', invalidRoleId);
        System.assertEquals(true, encodeUserId.call('encodeId',args));
        System.assertNotEquals(null, outMap.get('Id'));
        
        // Test with a null method name
        System.assertEquals(false, encodeUserId.call(null,args));
        System.assertNotEquals(null, outMap.get('Id'));
        
        // Test with an empty input map
        inputMap.clear();
        System.assertEquals(true, encodeUserId.call('encodeId',args));
        System.assertNotEquals(null, outMap.get('Id'));
    }

}