import { LightningElement, api, track, wire } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { OmniscriptBaseMixin } from "omnistudio/omniscriptBaseMixin";
import getDocumentTypes from '@salesforce/apex/PicklistMetadataController.getDocumentTypes';
import getPicklistValues from '@salesforce/apex/PicklistMetadataController.getPicklistValues';
import deleteDocumentAndFile from '@salesforce/apex/PicklistMetadataController.deleteDocumentAndFile';
import getRolesForApplication from '@salesforce/apex/PicklistMetadataController.getRolesForApplication';
import getApplicationDocuments from '@salesforce/apex/PicklistMetadataController.getApplicationDocuments';
import getFileSizeByDocumentId from '@salesforce/apex/PicklistMetadataController.getFileSizeByDocumentId';
import uploadFilesToApplication from '@salesforce/apex/PicklistMetadataController.uploadFilesToApplication';

export default class Rcu_DocumentUploader  extends OmniscriptBaseMixin(LightningElement) {
    
    @track fbnDBA;
    @track userRole;
    @track isId=false;
    uploadedFiles = [];
    @track uploadKey = 0;
    @track applicationId;
    @track documents = []; 
    @track uniqueKeyId = 0;    
    @track hasError = false;
    @track description = '';
    @track selectedRole = '';
    @track businessStructure;
    @track documentsData = [];
    @track isModalOpen = false;
    @api isAccountOpeningProcess;
    @track firstPicklistValue = '';
    @track secondPicklistValue = '';
    @track rolesPicklistOptions = '';
    acceptedFormats = ['.pdf', '.jpeg'];


    connectedCallback() {
        this.isId = false;
        console.log('OmniJsonData__: ', JSON.stringify(this.omniJsonData, null, 2));
        this.fetchOmniScriptData();
        console.log('$Calling-getAppDocs');
    }

    @track documentColumns = [
        { label: 'Document Type', fieldName: 'Document_Type__c' },
        { label: 'Description', fieldName: 'Description__c' },
        { label: 'Date/Time', fieldName: 'CreatedDate' },
        { type: 'button', typeAttributes: { label: 'Remove', name: 'remove', variant: 'destructive' } }
    ];

    @track documentsDataArray = [];

    getAppDocs(){
        console.log('$Called-getAppDocs');
        getApplicationDocuments({applicationId: this.applicationId}).then((result) => {
            console.log('$getApplicationDocuments-result: ',result);
            this.documentsDataArray = result.map(role => ({
                ...role,
                CreatedDate: new Date(role.CreatedDate).toLocaleString()
            }));
            if(this.documentsDataArray){
                console.log('$documentsDataArray: ', JSON.stringify(this.documentsDataArray, null, 2));
            }
        }).catch((error) => {
            console.error('$getApplicationDocuments-error: ',error);
        });
    }

    @track documentTypesPicklist = [];
    @wire(getPicklistValues, {objectName: 'DAO_Application_Documents__c', fieldName : 'Document_Type__c'})
    documentTypesPicklistHandler({ error, data }) {
        if (data) {
            console.log('$data-documentTypesPicklistHandler: ',data);
            this.documentTypesPicklist = data;
        }else if(error) {
            this.showToast('Error', 'Failed to load document type picklist values', 'error');
            console.error('Error fetching picklist values: ', error);
        }
    }

    @track rolePicklistValues = [];
    @wire(getPicklistValues, {objectName: 'DAO_Application_Documents__c', fieldName : 'Role__c'})
    rolesPicklistHandler({ error, data }) {
        if(data) {
            console.log('$data-rolesPicklistHandler: ',data);
            this.rolePicklistValues = data;
        }else if(error) {
            this.showToast('Error', 'Failed to load roles picklist values', 'error');
            console.error('Error fetching picklist values: ', error);
        }
    }

    columns = [
        { label: 'Document Type', fieldName: 'documentType' },
        { label: 'Description', fieldName: 'description' },
        { label: 'Date/Time', fieldName: 'dateTime' },
        { type: 'button', typeAttributes: { label: 'Remove', name: 'remove', variant: 'destructive' } }
    ];


    fetchRolesForApplication() {
        console.log('%c$-IN-fetchRolesForApplication', 'color: green;');
        getRolesForApplication({ applicationId: this.applicationId }).then(result => {
            console.log('$result: ',result);
            this.rolesPicklistOptions = result.map(role => ({
                label: role.Name,
                value: role.Id
            }));     
            console.log('OUTPUT : ',this.rolesPicklistOptions);
        }).catch(error => {
            this.showToast('Error', 'Failed to load roles', 'error');
            console.error('Error fetching roles:', error);
        });
    }

    fetchOmniScriptData() {
        console.log('%c$-IN-fetchOmniScriptData', 'color: green;');
        console.log('isAccountOpeningProcess:', this.isAccountOpeningProcess);

        this.applicationId = this.omniJsonData?.DRId_DAO_Application__c || this.omniJsonData?.applicationId || null;
        console.log('$applicationId-1: ',this.applicationId);
        if(!this.applicationId){
            this.applicationId = this.omniJsonData?.Application?.appId ?? this.applicationId;
            console.log('$applicationId-2: ',this.applicationId);
        }
        this.getAppDocs();
        this.businessStructure = this.omniJsonData?.PrimaryContactInfo?.BusinessStructure || null;
        console.log('$businessStructure: ',this.businessStructure);
        this.userRole = this.omniJsonData?.RoleInfo?.IndividualRole || this.omniJsonData?.applicantInfo?.allApplicantBlock?.selectIndividualRole || null;
        console.log('$userRole: ',this.userRole);
        this.fbnDBA = this.omniJsonData?.PrimaryContactInfo?.Fictitious_Business_Name_DBA || false;
        console.log('$fbnDBA: ',this.fbnDBA);

        const documentsState = this.omniGetSaveState('uploadedDocumentsArray');
        console.log('$documentsState: ',documentsState);
        if (documentsState) {
            this.documentsData = documentsState;
            console.log("$documentsData",this.documentsData);
        }
        console.log('%c$-OUT-fetchOmniScriptData', 'color: red;');
    }

    renderedCallback(){
        console.log('$omniJsonData-', JSON.stringify(this.omniJsonData, null, 2));
        if(this.documentsData)
            console.log('$documentsData-', JSON.stringify(this.documentsData, null, 2));
        if(this.documentTypeOptions)
            console.log('$documentTypeOptions-', JSON.stringify(this.documentTypeOptions, null, 2));
        if(this.rolePicklistValues)
            console.log('$rolePicklistValues-', JSON.stringify(this.rolePicklistValues, null, 2));
        if(this.rolesPicklistOptions)
            console.log('$rolesPicklistOptions-', JSON.stringify(this.rolesPicklistOptions, null, 2));
        if(this.documentsData)
            console.log('$documentsData-', JSON.stringify(this.documentsData, null, 2));

        if(this.template.querySelector('lightning-file-upload')){
            let ss = this.template.querySelector('lightning-file-upload');
            console.log('$ss: ',ss.innerText);
            const style = document.createElement('style');
            style.innerText = `
                .slds-file-selector__button.slds-button.slds-button_neutral {
                    color: rgb(46, 132, 74) !important;
                }
            `;
            this.template.querySelector('lightning-file-upload').appendChild(style);
        }
        if(this.template.querySelector('.slds-button_success')){
            let ss = this.template.querySelector('.slds-button_success');
            console.log('$ss: ',ss.innerText);
            const style = document.createElement('style');
            style.innerText = `
                .slds-button.slds-button_brand {
                    background-color: rgb(46, 132, 74) !important;
                    border: 1px solid rgb(46, 132, 74) !important;
                }
            `;
            this.template.querySelector('.slds-button_success').appendChild(style);
        }
    }

    @track documentTypeOptions = [];
    fetchDocumentTypeOptions() {
        console.log('%c$-IN-fetchDocumentTypeOptions', 'color: green;');
        const payload = {
            businessStructure: (this.isAccountOpeningProcess === 'true' || this.isAccountOpeningProcess === true) ? this.businessStructure : null,
            role: this.userRole,
            fbnDBA: this.fbnDBA
        };

        console.log('$Payload:', JSON.stringify(payload));

        getDocumentTypes(payload).then(result => {
            console.log('$result: ',result);
            this.documentTypeOptions = result.map(documentType => ({
                label: documentType,
                value: documentType
            }));
        }).catch(error => {
            console.error('Error fetching document types:', error);
            this.showToast('Error', 'Failed to load document types', 'error');
        });
        console.log('%c$-OUT-fetchDocumentTypeOptions', 'color: red;');
    }

    handlePicklistChange(event) {
        const name = event.target.name;
        if(name === 'firstPicklist') {
            this.firstPicklistValue = event.detail.value;
            console.log('$firstPicklistValue: ',this.firstPicklistValue);
            this.isId = this.firstPicklistValue == "Government Issued Identification";
        }else if(name === 'secondPicklist') {
            this.secondPicklistValue = event.detail.value;
        }
    }

    get showSelectRoleDropdowns() {
        return this.firstPicklistValue === 'Government Issued Identification';
    }

    handleRolePicklistChange(event) {
        this.selectedRole = event.detail.value;
        console.log("SlectedRole: ", this.selectedRole);
    }

    handleDescriptionChange(event) {
        this.description = event.target.value;
    }

    handleUploadFinished(event) {
        console.log('Upload finished');
        console.log('Upload finished:', event);
        console.log('Upload finished:', event.detail);
        console.log('Upload finished:', event.detail.files);
        this.uploadedFiles = event.detail.files;
        const documentIds = this.uploadedFiles.map(file => file.documentId);
        getFileSizeByDocumentId({ documentIds }).then((fileVersions) => {
            let isFileSizeValid = true;
            fileVersions.forEach(file => {
                const fileSizeMB = file.ContentSize / (1024 * 1024);
                if (fileSizeMB > 50) {
                    isFileSizeValid = false;
                }
            });
            if (!isFileSizeValid) {
                this.showToast('Error', 'File size exceeds 50 MB', 'error');
                this.hasError = true;
                this.resetFormvalue();
            } else {
                this.hasError = false;
            }
        }).catch(error => {
            console.error('Error getting file size:', error);
            this.hasError = true;
        });
    }

    resetFormvalue() {
        this.uploadedFiles = [];
        this.uploadKey += 1;
    }

    resetForm() {
        this.firstPicklistValue = '';
        this.description = '';
        this.secondPicklistValue = '';
        this.documents = [];
        this.uploadedFiles = [];
        this.uploadKey += 1;
        this.selectedRole = '';
        // this.isId = false;
    }

    // Upload Files to Application
    handleSubmit() {
        if (this.hasError) {
            this.showToast('Error', 'Please fix the errors before submitting', 'error');
            return;
        }
        if (this.firstPicklistValue && this.description && this.uploadedFiles.length > 0) {
            console.log('Uploaded files:', this.uploadedFiles);
            console.log('First picklist value:', this.firstPicklistValue);
            console.log('Second picklist value:', this.secondPicklistValue);
            console.log(' Description:', this.description);
            console.log(' selectedRole:', this.selectedRole);   
            console.log('application Id:', this.applicationId);
            const newDocuments = this.uploadedFiles.map(file => ({
                id: file.documentId,
                documentType: this.firstPicklistValue,
                description: this.description,
                role: this.secondPicklistValue,
                userrole: this.selectedRole,
                dateTime: new Date().toLocaleString()
            }));

            console.log('$newDocuments: ',newDocuments);

            this.documentsData = [...this.documentsData, ...newDocuments];

            uploadFilesToApplication({ documents: newDocuments, applicationRecordId: this.applicationId }).then(() => {
                this.showToast('Success', 'Files uploaded successfully', 'success');
                this.resetForm();
                this.getAppDocs();
            }).catch(error => {
                console.log('$docum -> ',newDocuments);
                console.log('$applicationId -> ', this.applicationId);
                this.showToast('$Error', 'Failed to upload files', 'error');
                console.error('$Error in file upload:', error);
            });

        } else {
            this.showToast('Error', 'Please fill out all fields and upload at least one file', 'error');
        }
    }

    // Uploaded File Data Table Row Actions
    handleRowAction(event) {
        const actionName = event.detail.action.name;
        const row = event.detail.row;
        console.log('$row: ',row);
        if (actionName === 'remove') {
            deleteDocumentAndFile({ applicationDocumentId: row.Id, contentDocumentId: row.DocumentId__c }).then(() => {
                    this.documentsData = this.documentsData.filter(doc => doc.Id !== row.Id);
                    this.showToast('Success', 'File removed successfully', 'success');
                    this.getAppDocs();
                    this.uniqueKeyId += 1;
            }).catch(error => {
                console.error('Error removing file:', error);
                this.showToast('Error', 'Failed to remove file', 'error');
            });
        }
    }

    // Modal Open
    handleClick() {
        this.fetchOmniScriptData();
        this.fetchDocumentTypeOptions();
        this.fetchRolesForApplication();
        this.isModalOpen = true;
    }

    // Modal Close
    closeModal() {
        this.pushToOmniScript();
        this.resetForm();
        this.resetFormvalue();
        this.isModalOpen = false;
    }

    // Show Toast Message
    showToast(title, message, variant) {
        const event = new ShowToastEvent({title, message, variant});
        this.dispatchEvent(event);
    }

    pushToOmniScript() {
        if (this.documentsData.length > 0) {
            this.omniSaveState(this.documentsData, 'uploadedDocumentsArray');
        }
    }
}