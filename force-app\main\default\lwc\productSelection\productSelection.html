<template>
    <table style="border-collapse: collapse; width: 100%; height: 213px; margin-left: auto; margin-right: auto;" border="1">
        <tbody>
            <tr style="height: 15px;">
                <td class="Table_header cell_width" style="background-color:white"></td>
                <td class="Table_header cell_width line" style="text-align:center;">
                    <strong>Essential</strong>
                    <div style="font-size:13px;font-weight:normal">
                        Ideal for small businesses with simple transaction needs.
                    </div>
                    <div style="font-size:13px;font-weight:normal">
                        <a href="https://www.redwoodcu.org/business/accounts/checking/essential-checking/" target="_blank" style="color: green;text-decoration:underline">Learn More</a>
                    </div>
                </td>
                <td class="Table_header cell_width line" style="text-align:center;">
                    <strong>Choice</strong>
                    <div style="font-size:13px;font-weight:normal">
                        Ideal for mid-to-large businesses with larger deposits or with high volume of transactions.
                    </div>
                    <div style="font-size:13px;font-weight:normal">
                        <a href="https://www.redwoodcu.org/business/accounts/checking/choice-checking/" target="_blank" style="color: green;text-decoration:underline">Learn More</a>
                    </div>
                </td>
                <td class="Table_header cell_width line" style="text-align:center;border-right:1px solid #7f7e7e">
                    <strong>Savings Account</strong>
                    <div style="font-size:13px;font-weight:normal">
                        A business savings account lets you earn dividends so you can grow your business faster.
                    </div>
                    <div style="font-size:13px;font-weight:normal">
                        <a href="https://www.redwoodcu.org/business/accounts/savings/business-savings/" target="_blank" style="color: green;text-decoration:underline">Learn More</a>
                    </div>
                </td>
            </tr>
            <tr style="background-color: #ebf1e9;">
                <td class="cell_width line Even_row">Opening Balance</td>
                <td class="cell_width line Even_row">$100</td>
                <td class="cell_width line Even_row">$100</td>
                <td class="cell_width Even_row" style="">Required</td>
            </tr>
            <tr style="background-color: #d5e3cf;">
                <td class="cell_width line Odd_row">Dividends Paid</td>
                <td class="cell_width line Odd_row">No</td>
                <td class="cell_width line Odd_row">Yes</td>
                <td class="cell_width line Odd_row" style="border-right:1px solid #7f7e7e">Yes<br/> </td>
            </tr>
            <tr style="background-color: #ebf1e9;">
                <td class="cell_width line Even_row" rowspan="2" >Monthly Fee</td>
                <td class="cell_width line Even_row" style="padding-bottom: 0px;">$8</td>
                <td class="cell_width line Even_row" style="padding-bottom: 0px;">$15</td>
            </tr>
             <tr style="background-color: #ebf1e9;">
                <td class="cell_width line Even_row" style="vertical-align: super;"><a href="#" onclick={openModal} data-id="fee1" style="color: green;text-decoration:underline;font-size:14px">Learn how to waive this fee</a></td>
                <td class="cell_width line Even_row" style="vertical-align: super;"><a href="#" onclick={openModal2} data-id="fee1" style="color: green;text-decoration:underline;font-size:14px">Learn how to waive this fee</a></td>
            </tr>
            <!-- <tr style="background-color: #d5e3cf;">
                <td class="cell_width line Even_row">501(c)(3) Monthly Fee</td>
                <td class="cell_width line Even_row">$0</td>
                <td class="cell_width line Even_row">$0</td>
            </tr> -->
            <tr style="background-color: #ebf1e9;">
                <td class="cell_width line Odd_row">Number of items deposited &amp; paid</td>
                <td class="cell_width line Odd_row">Up to 300 per month</td>
                <td class="cell_width line Odd_row">Combined 300 items with $0.20/item over limit</td>
            </tr>
            <tr style="background-color: #d5e3cf;">
                <td class="cell_width line Even_row">Coin and currency processing fee</td>
                <td class="cell_width line Even_row">$5 per $1,000 deposited or withdrawn</td>
                <td class="cell_width line Even_row">$5 per $2,500 deposited or withdrawn</td>
            </tr>
            <tr style="background-color: #ebf1e9;">
                <td class="cell_width line Odd_row">Remote Deposit Capture</td>
                <td class="cell_width line Odd_row">
                    Yes
                </td>
                <td class="cell_width line Odd_row">
                    Yes
                </td>
            </tr>
            <tr style="background-color: #d5e3cf;">
                <td class="cell_width line Even_row">Mobile Deposit</td>
                <td class="cell_width line Even_row">
                    Yes
                </td>
                <td class="cell_width line Even_row" style="width: 31.9139%; height: 27px; text-align: center;">
                    Yes
                </td>
            </tr>
            <tr style="background-color: #ebf1e9;">
                <td class="cell_width line Odd_row">Self-Service Wires</td>
                <td class="cell_width line Odd_row">
                    Yes
                </td>
                <td class="cell_width line Odd_row">
                    Yes
                </td>
            </tr>
            <tr style="background-color: #d5e3cf;">
                <td class="cell_width line Even_row">Sweep Service</td>
                <td class="cell_width line Even_row">
                    Yes
                </td>
                <td class="cell_width line Even_row">
                    Yes
                </td>
            </tr>
            <tr style="background-color: #ebf1e9;">
                <td class="cell_width line Odd_row">Self-Service ACH</td>
                <td class="cell_width line Odd_row">
                    No
                </td>
                <td class="cell_width line Odd_row">
                    Yes
                </td>
            </tr>
            <tr style="background-color: #ebf1e9;">
                <td class="cell_width line Odd_row">
                    <strong style="color:green;">Choose Your Account</strong>
                </td>
                <td class="cell_width line Odd_row">
                    <lightning-input label="text" variant="label-hidden" type="radio" onchange={changeEssentialRadio} if:true={essentialChecked} checked={essentialChecked}></lightning-input>
                    <lightning-input label="text" variant="label-hidden" type="radio" onchange={changeEssentialRadio} if:false={essentialChecked} required  ></lightning-input>
                </td>
                <td class="cell_width line Odd_row">
                    <lightning-input label="text" variant="label-hidden" type="radio" onchange={changeChoiseRadio} if:true={choiceChecked} checked={choiceChecked} ></lightning-input>
                    <lightning-input label="text" variant="label-hidden" type="radio" onchange={changeChoiseRadio} if:false={choiceChecked}></lightning-input>
                </td> 
            </tr>
            <template if:true={showMembershipBenefitsSection}>
                    <tr style="background-color: #ebf1e9;">
                    <td class="cell_width line Odd_row">
                        <strong style="font-size: 17px;text-align:left">Membership Benefits</strong>
                    </td>
                </tr>
                <tr style="background-color: #ebf1e9">
                    <td class="cell_width line Odd_row" style="border-bottom: 2px solid #e4e4e4"></td>
                    <td class="cell_width line Even_row" style="background-color: rgb(235, 241, 233);border-bottom: 2px solid #e4e4e4">Yes</td>
                    <td class="cell_width line Even_row" style="background-color: rgb(235, 241, 233);border-bottom: 2px solid #e4e4e4">No</td>
                </tr>
                <tr style="background-color: #ebf1e9">
                    <td class="cell_width line Odd_row" style="border-bottom: 2px solid #e4e4e4">
                        <strong style="color:green;">Visa® Debit Card</strong>
                    </td>
                    <!--<template if:true={essentailFinalCheck}>-->
                        <td class="cell_width line Odd_row" style="border-bottom: 2px solid #e4e4e4">
                            <lightning-input label="text" variant="label-hidden"
                                type="radio"
                                name="radioGroup1"
                                value="Yes"
                                if:true={essentialVisa}
                                checked = {essentialVisa}
                                onchange={changeVisaDebitCardRadio}>
                            </lightning-input>
                            <lightning-input label="text" variant="label-hidden"
                                type="radio"
                                name="radioGroup1"
                                value="Yes"
                                if:false={essentialVisa}
                                onchange={changeVisaDebitCardRadio}>
                            </lightning-input>
                        </td>
                        <td class="cell_width line Odd_row" style="border-bottom: 2px solid #e4e4e4">
                            <lightning-input label="text" variant="label-hidden"
                                type="radio"
                                name="radioGroup1"
                                value="No"
                                if:true={essentialVisa1}
                                checked = {essentialVisa1}
                                onchange={changeVisaDebitCardRadio}>
                            </lightning-input>
                            <lightning-input label="text" variant="label-hidden"
                                type="radio"
                                name="radioGroup1"
                                value="No"
                                if:false={essentialVisa1}
                                onchange={changeVisaDebitCardRadio}>
                            </lightning-input>
                        </td> 
                    <!--</template>-->
                    <!--<template if:true={choiceChecked}>
                        <td class="cell_width line Odd_row" style="border-bottom: 2px solid #e4e4e4">
                            <lightning-input label="text" variant="label-hidden"
                                type="radio"
                                name="radioGroup2"
                                value={checkingVisaValue}
                                if:true={checkingVisa}
                                checked = {checkingVisa}
                                onchange={changeVisaDebitCardRadioCType}>
                            </lightning-input>
                                <lightning-input label="text" variant="label-hidden"
                                type="radio"
                                name="radioGroup2"
                                value={checkingVisaValue}
                                if:false={checkingVisa}
                                onchange={changeVisaDebitCardRadioCType}>
                            </lightning-input>
                        </td>
                        <td class="cell_width line Odd_row" style="border-bottom: 2px solid #e4e4e4">
                            <lightning-input label="text" variant="label-hidden"
                                type="radio"
                                name="radioGroup2"
                                value={checkingVisaValue1}
                                if:true={checkingVisa1}
                                checked = {checkingVisa1}
                                onchange={changeVisaDebitCardRadioCType}>
                            </lightning-input>
                                <lightning-input label="text" variant="label-hidden"
                                type="radio"
                                name="radioGroup2"
                                value={checkingVisaValue1}
                                if:false={checkingVisa1}                                    
                                onchange={changeVisaDebitCardRadioCType}>
                            </lightning-input>
                        </td>
                    </template>-->
                </tr>
                <tr style="background-color: #ebf1e9">
                    <td class="cell_width line Odd_row" style="border-bottom: 2px solid #e4e4e4">
                        <strong style="color:green;">eStatements</strong>
                    </td>
                    <td class="cell_width line Odd_row" style="border-bottom: 2px solid #e4e4e4">
                        <lightning-input label="text" variant="label-hidden"
                            type="radio"
                            name="radioGroup5"
                            value="Yes"
                            if:true={accountEStatement}
                            checked = {accountEStatement}
                            onchange={changeEStatementRadio}>
                        </lightning-input>
                            <lightning-input label="text" variant="label-hidden"
                            type="radio"
                            name="radioGroup5"
                            value="Yes"
                            if:false={accountEStatement}
                            onchange={changeEStatementRadio}>
                        </lightning-input>
                    </td>
                    <td class="cell_width line Odd_row" style="border-bottom: 2px solid #e4e4e4">
                        <lightning-input label="text" variant="label-hidden"
                            type="radio"
                            name="radioGroup5"
                            value="No"
                            if:true={accountEStatement1}
                            checked = {accountEStatement1}
                            onchange={changeEStatementRadio}>
                        </lightning-input>
                            <lightning-input label="text" variant="label-hidden"
                            type="radio"
                            name="radioGroup5"
                            value="No"
                            if:false={accountEStatement1}
                            onchange={changeEStatementRadio}>
                        </lightning-input>
                    </td> 
                </tr>
                <tr style="background-color: #ebf1e9">
                    <td class="cell_width line Odd_row" style="border-bottom: 2px solid #e4e4e4">
                        <strong style="color:green;">Digital Banking</strong>
                    </td>
                    <td class="cell_width line Odd_row" style="border-bottom: 2px solid #e4e4e4">
                        <lightning-input label="text" variant="label-hidden"
                            type="radio"
                            name="radioGroup6"
                            value="Yes"
                            if:true={accountOnline}
                            checked = {accountOnline}
                            onchange={changeDigitalBankingRadio}>
                        </lightning-input>
                            <lightning-input label="text" variant="label-hidden"
                            type="radio"
                            name="radioGroup6"
                            value="Yes"
                            if:false={accountOnline}
                            onchange={changeDigitalBankingRadio}>
                        </lightning-input>
                    </td>
                    <td class="cell_width line Odd_row" style="border-bottom: 2px solid #e4e4e4">
                        <lightning-input label="text" variant="label-hidden"
                            type="radio"
                            name="radioGroup6"
                            value="No"
                            if:true={accountOnline1}
                            checked = {accountOnline1}
                            onchange={changeDigitalBankingRadio}>
                        </lightning-input>
                        <lightning-input label="text" variant="label-hidden"
                            type="radio"
                            name="radioGroup6"
                            value="No"
                            if:false={accountOnline1}
                            onchange={changeDigitalBankingRadio}>
                        </lightning-input>
                    </td> 
                </tr>
                
                <tr style="background-color: #ebf1e9">
                    <td class="cell_width line Odd_row" colspan="2" style="border-bottom: 2px solid #e4e4e4">
                        <div style="text-align: left;padding-left: 8px;">
                            <strong style="font-size: 17px;text-align:left">Business Visa Credit Card</strong>
                        </div>
                        <div style="text-align:left;margin-left:9px">
                            Are you interested in increasing your purchasing power and earning rewards for everyday purchases with a <a href="https://www.redwoodcu.org/business/cards/business-visa-platinum/" target="_blank" style="color:green;text-decoration:underline;font-size:15px">Business Visa Credit Card</a>
                        </div>
                    </td>
                </tr>
                <tr style="background-color: #ebf1e9">
                    <td class="cell_width line Odd_row" style="border-bottom: 2px solid #e4e4e4"></td>
                    <td class="cell_width line Even_row" style="background-color: rgb(235, 241, 233);border-bottom: 2px solid #e4e4e4">Yes</td>
                    <td class="cell_width line Even_row" style="background-color: rgb(235, 241, 233);border-bottom: 2px solid #e4e4e4">No</td>
                </tr>
                <tr style="background-color: #ebf1e9">
                    <td class="cell_width line Odd_row" >
                        <strong style="color:green;">Business Visa Credit Card</strong>
                    </td>
                    <td class="cell_width line Odd_row" >
                        <lightning-input label="text" variant="label-hidden"
                            type="radio"
                            name="radioGroup7"
                            value="Yes"
                            if:true={businessVisa}
                            checked = {businessVisa}
                            onchange={changeBussinessCreditCardRadio}>
                        </lightning-input>
                        <lightning-input label="text" variant="label-hidden"
                            type="radio"
                            name="radioGroup7"
                            value="Yes"
                            if:false={businessVisa}
                            onchange={changeBussinessCreditCardRadio}>
                        </lightning-input>
                    </td>
                    <td class="cell_width line Odd_row" >
                        <lightning-input label="text" variant="label-hidden"
                            type="radio"
                            name="radioGroup7"
                            value="No"
                            if:true={businessVisa1}
                            checked = {businessVisa1}
                            onchange={changeBussinessCreditCardRadio}>
                        </lightning-input>
                        <lightning-input label="text" variant="label-hidden"
                            type="radio"
                            name="radioGroup7"
                            value="No"
                            if:false={businessVisa1}
                            onchange={changeBussinessCreditCardRadio}>
                        </lightning-input>
                    </td> 
                </tr>
            </template>
        </tbody>
    </table>
    <template if:true={isModalOpen}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-scope">
            <div class="slds-modal__container">
                <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse slds-modal__close slds-button_icon-inverse" title="Close" onclick={closeModal}>
                    <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                    <span class="slds-assistive-text">Close</span>
                </button>
                <lightning-card variant="Narrow">
                    <p>To waive this fee, keep an average daily balance of $1,000 or a combined average daily business deposit balance of $5,000. (Business deposit accounts include business savings, business money market, and business certificate accounts.)<br>
                        Monthly Fee is waived for qualifying 501(c)(3) non profit or community organizations.
                    </p>
                    <br>
                </lightning-card>

            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
    <template if:true={isModalOpen1}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open">
            <div class="slds-modal__container">
                <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse slds-modal__close slds-button_icon-inverse" title="Close" onclick={closeModal}>
                    <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                    <span class="slds-assistive-text">Close</span>
                </button>
                <lightning-card variant="Narrow">
                    <p>To waive this fee, keep an average daily balance of $5,000 or a combined average daily business deposit balance of $10,000. (Business deposit accounts include business savings, business money market, and business certificate accounts.)<br>
                     Monthly Fee is waived for qualifying 501(c)(3) non profit or community organizations.
                    </p>
                    <br>
                </lightning-card>
            </div>
        </section>
        <div class="slds-backdrop slds-backdrop_open"></div>
    </template>
    <br>&nbsp;
    <div class="button-container">
        <button class="previous-btn" onclick={handlePreviousPage}>Previous</button>
        <button class="save-continue-btn" onclick={handleNextPage}>Save and Continue</button>
    </div>
</template>